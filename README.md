# ReYoutube Web

<div align="center">
  <h3>🚀 Advanced YouTube Channel Analytics Platform</h3>
  <p>A comprehensive web application for YouTube channel and video data analysis, built with Next.js 15 and modern web technologies.</p>

  [![Next.js](https://img.shields.io/badge/Next.js-15.3.0-black?style=flat-square&logo=next.js)](https://nextjs.org/)
  [![TypeScript](https://img.shields.io/badge/TypeScript-5.0-blue?style=flat-square&logo=typescript)](https://www.typescriptlang.org/)
  [![Prisma](https://img.shields.io/badge/Prisma-6.1.0-2D3748?style=flat-square&logo=prisma)](https://www.prisma.io/)
  [![PostgreSQL](https://img.shields.io/badge/PostgreSQL-13+-336791?style=flat-square&logo=postgresql)](https://www.postgresql.org/)
  [![Tailwind CSS](https://img.shields.io/badge/Tailwind_CSS-3.4.1-38B2AC?style=flat-square&logo=tailwind-css)](https://tailwindcss.com/)
</div>

## 📋 Table of Contents

- [Overview](#overview)
- [Features](#features)
- [Tech Stack](#tech-stack)
- [Prerequisites](#prerequisites)
- [Installation](#installation)
- [Configuration](#configuration)
- [Usage](#usage)
- [API Documentation](#api-documentation)
- [Database Schema](#database-schema)
- [Development](#development)
- [Testing](#testing)
- [Deployment](#deployment)
- [Contributing](#contributing)
- [License](#license)

## 🎯 Overview

ReYoutube Web is a powerful analytics platform designed for YouTube content creators, marketers, and analysts. It provides comprehensive insights into channel performance, video analytics, audience behavior, and revenue tracking with real-time data monitoring and predictive analytics.

### Key Capabilities

- **Real-time Analytics**: Monitor channel metrics, video performance, and audience engagement in real-time
- **Predictive Insights**: AI-powered trend prediction and performance forecasting
- **Multi-language Support**: Full internationalization with English and Chinese language support
- **Revenue Tracking**: Comprehensive revenue analytics and growth projections
- **Audience Analysis**: Deep dive into audience demographics and behavior patterns
- **Competitive Analysis**: Compare channels and benchmark performance

## ✨ Features

### 📊 Analytics & Insights
- **Channel Overview**: Comprehensive dashboard with key performance indicators
- **Video Analytics**: Detailed video performance metrics and trends
- **Audience Demographics**: Age, gender, geography, and interest analysis
- **Revenue Analytics**: Monetization tracking and revenue optimization insights
- **Growth Predictions**: AI-powered forecasting for future performance

### 🔍 Search & Discovery
- **Smart Channel Search**: Advanced search with autocomplete and filtering
- **@Username Access**: Direct channel access via `/@username` URLs
- **External Data Integration**: Automatic fallback to external APIs for missing data
- **Real-time Data Sync**: Continuous synchronization with YouTube API

### 👤 User Management
- **Authentication**: Secure login with Google OAuth and traditional email/password
- **User Profiles**: Personalized dashboards and preferences
- **Subscription Management**: Stripe-integrated payment processing
- **Multi-tenant Support**: Family and organization management features

### 🌐 Platform Features
- **Responsive Design**: Optimized for desktop, tablet, and mobile devices
- **Dark/Light Theme**: User-preferred theme switching
- **SEO Optimized**: Automatic sitemap generation and meta optimization
- **Performance Optimized**: Advanced caching and bundle optimization

## 🛠 Tech Stack

### Frontend
- **Framework**: Next.js 15.3.0 with App Router
- **Language**: TypeScript 5.0
- **Styling**: Tailwind CSS 3.4.1 + Tailwind Animate
- **UI Components**: Radix UI + Custom Component Library
- **Icons**: Lucide React + Heroicons
- **Charts**: Recharts for data visualization
- **Animations**: Framer Motion

### Backend
- **Runtime**: Node.js with Next.js API Routes
- **Database**: PostgreSQL 13+ with Prisma ORM 6.1.0
- **Authentication**: NextAuth.js with Google OAuth
- **Payment**: Stripe integration for subscriptions
- **Internationalization**: next-intl for multi-language support

### Development & DevOps
- **Package Manager**: pnpm 10.10.0
- **Linting**: ESLint with Next.js configuration
- **Testing**: Jest with ts-jest for unit testing
- **Database Testing**: Docker Compose for test environments
- **Bundle Analysis**: @next/bundle-analyzer
- **Deployment**: Vercel-optimized with standalone output

## 📋 Prerequisites

Before you begin, ensure you have the following installed:

- **Node.js**: Version 18.0 or higher
- **pnpm**: Version 8.0 or higher (recommended package manager)
- **PostgreSQL**: Version 13 or higher
- **Git**: For version control

### Optional
- **Docker**: For running test databases
- **Vercel CLI**: For deployment

## 🚀 Installation

### 1. Clone the Repository

```bash
git clone https://github.com/wenhaofree/reyoutube-web.git
cd reyoutube-web
```

### 2. Install Dependencies

```bash
pnpm install
```

### 3. Environment Setup

Create environment files:

```bash
cp .env.example .env.local
cp .env.test.example .env.test
```

## ⚙️ Configuration

### Database Configuration

Configure your PostgreSQL connection in `.env.local`:

```env
# Database Configuration
DATABASE_URL='postgresql://user:password@host:port/dbname?connection_limit=20&pool_timeout=30&statement_timeout=60000&idle_in_transaction_session_timeout=60000'
```

**Connection Parameters:**
- `connection_limit`: Maximum connection pool size (recommended: 20)
- `pool_timeout`: Connection acquisition timeout in seconds (recommended: 30)
- `statement_timeout`: SQL statement execution timeout in milliseconds (recommended: 60000)
- `idle_in_transaction_session_timeout`: Transaction idle timeout in milliseconds (recommended: 60000)

### Authentication Configuration

```env
# NextAuth Configuration
NEXTAUTH_SECRET=your-secret-key-here
NEXTAUTH_URL=http://localhost:3000

# Google OAuth
GOOGLE_CLIENT_ID=your-google-client-id
GOOGLE_CLIENT_SECRET=your-google-client-secret
```

### Payment Configuration

```env
# Stripe Configuration
STRIPE_SECRET_KEY=sk_test_your-stripe-secret-key
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=pk_test_your-stripe-publishable-key
STRIPE_WEBHOOK_SECRET=whsec_your-webhook-secret
```

### Application Configuration

```env
# Application Settings
NEXT_PUBLIC_WEB_URL=http://localhost:3000
NEXT_PUBLIC_API_URL=http://localhost:3000/api

# External API Configuration
YOUTUBE_API_KEY=your-youtube-api-key
```

### Database Setup

1. **Initialize Database Schema**:
```bash
pnpm run db:push
```

2. **Generate Prisma Client**:
```bash
pnpm run db:generate
```

3. **Sync Database Structure**:
```bash
pnpm run db:sync
```

## 🎮 Usage

### Development Server

Start the development server:

```bash
pnpm dev
```

The application will be available at [http://localhost:3000](http://localhost:3000).

### Database Management

- **Sync database structure**: `pnpm run db:sync`
- **Launch Prisma Studio**: `pnpm run db:studio`
- **Test database connection**: `pnpm run test:db`
- **Database health check**: `pnpm run db:health`

### Build and Production

```bash
# Build for production
pnpm build

# Start production server
pnpm start

# Analyze bundle size
pnpm run analyze
```

### Docker Development

```bash
# Start test database
pnpm run docker:up

# Stop test database
pnpm run docker:down

# Run tests with Docker
pnpm run test:db:docker
```

## 📚 API Documentation

### Channel Access Endpoints

#### @Username Access Feature

The platform implements a powerful @username access feature for simplified channel URLs:

**Endpoint**: `GET /@username`

**Features**:
- Direct channel access via `/@username` URLs
- Automatic database search for corresponding channels
- External API fallback for missing data
- Intelligent error handling and user experience

**Implementation Details**:

1. **Middleware Processing**:
   - Captures `/@username` format URLs
   - Rewrites requests to API route handlers

2. **Query Optimization**:
   - Supports both @username and username formats
   - External API as data source backup

3. **Error Handling**:
   - Comprehensive error logging
   - User-friendly error pages
   - Automatic redirect logic

**Usage Example**:
```bash
# Direct channel access
curl https://reyoutube.com/@channelname

# System behavior:
# 1. Search database for channel
# 2. If not found, query external API
# 3. Redirect to appropriate page based on results
```

### Core API Endpoints

#### Channel Analytics
```bash
# Get channel information
GET /api/channels/[channelId]

# Get channel statistics
GET /api/channels/[channelId]/stats?timeRange=30d

# Get channel videos
GET /api/channels/[channelId]/videos?page=1&limit=20
```

#### Search & Discovery
```bash
# Search channels
GET /api/search/channels?q=searchterm&limit=10

# Handle username lookup
GET /api/channels/handle/[username]
```

#### User Management
```bash
# User authentication
POST /api/auth/signin
POST /api/auth/signout

# User profile
GET /api/user/profile
PUT /api/user/profile
```

## 🗄️ Database Schema

### Core Tables

#### YouTube Data Models
- **ytb_channels**: Channel information and metadata
- **ytb_videos**: Video details and statistics
- **ytb_channel_stats_snapshots**: Historical channel performance data
- **ytb_video_stats_snapshots**: Historical video performance data
- **ytb_channel_time_statistics**: Time-based analytics and growth metrics

#### User & Business Models
- **users**: User accounts and authentication
- **orders**: Subscription and payment records
- **system_config**: Application configuration
- **system_log**: Audit trail and logging

#### Family Management (Extended Features)
- **family**: Family/organization management
- **family_member**: Member profiles and relationships
- **family_event**: Events and milestones
- **family_education**: Educational background
- **family_work**: Professional history

### Key Indexes and Optimizations

```sql
-- Channel performance optimization
CREATE INDEX idx_ytb_videos_channel_published
ON ytb_videos(channel_id, published_at DESC)
WHERE deleted_at IS NULL;

-- Search optimization
CREATE INDEX idx_ytb_channels_custom_url_trgm
ON ytb_channels USING gin(custom_url gin_trgm_ops);
```

## 🧪 Testing

### Unit Testing

Run the test suite:

```bash
# Run all tests
pnpm test:db

# Run tests with Docker
pnpm run test:db:docker

# Debug tests
pnpm run test:debug
```

### Database Testing

```bash
# Setup test database
pnpm run db:test:setup

# Initialize test data
pnpm run db:test:init

# Launch test database studio
pnpm run db:test:studio
```

### Test Configuration

The project uses Jest with ts-jest for TypeScript support. Test configuration is located in `src/tests/jest.config.js`.

## 🚀 Deployment

### Vercel Deployment (Recommended)

1. **Connect Repository**: Link your GitHub repository to Vercel
2. **Environment Variables**: Configure all required environment variables in Vercel dashboard
3. **Database**: Ensure PostgreSQL database is accessible from Vercel
4. **Deploy**: Automatic deployment on push to main branch

### Manual Deployment

```bash
# Build the application
pnpm build

# Start production server
pnpm start
```

### Docker Deployment

```dockerfile
# Example Dockerfile structure
FROM node:18-alpine
WORKDIR /app
COPY package*.json ./
RUN pnpm install --frozen-lockfile
COPY . .
RUN pnpm build
EXPOSE 3000
CMD ["pnpm", "start"]
```

## 🔧 Development

### Project Structure

```
reyoutube-web/
├── src/
│   ├── app/                 # Next.js App Router
│   │   ├── [locale]/       # Internationalized routes
│   │   ├── api/            # API routes
│   │   └── globals.css     # Global styles
│   ├── components/         # React components
│   │   ├── ui/            # UI components
│   │   ├── channel/       # Channel-specific components
│   │   └── charts/        # Chart components
│   ├── lib/               # Utility libraries
│   ├── hooks/             # Custom React hooks
│   ├── types/             # TypeScript type definitions
│   └── middleware.ts      # Next.js middleware
├── prisma/                # Database schema and migrations
├── messages/              # Internationalization files
├── public/                # Static assets
└── scripts/               # Build and utility scripts
```

### Code Style and Standards

- **TypeScript**: Strict mode enabled
- **ESLint**: Next.js configuration with custom rules
- **Prettier**: Code formatting (configured in package.json)
- **Conventional Commits**: Commit message standards

### Performance Optimizations

- **Bundle Analysis**: Use `pnpm run analyze` to analyze bundle size
- **Image Optimization**: Next.js Image component with multiple formats
- **Caching**: Comprehensive caching strategy for API responses
- **Database Indexing**: Optimized indexes for common queries

## 🔍 SEO & Sitemap

The project uses `next-sitemap` for automatic sitemap and robots.txt generation to improve search engine indexing.

### Features

- **Automatic Sitemap Generation**: Complete sitemap generation using `next-sitemap`
- **Dynamic Route Support**: Includes both static and dynamic routes
- **Multi-language Configuration**: Automatic inclusion of internationalized routes
- **Priority & Frequency Settings**: Configurable priority and update frequency
- **Robots.txt Generation**: Automatic robots.txt file pointing to sitemaps

### Configuration

1. **Set Base URL** in `.env`:
   ```env
   NEXT_PUBLIC_WEB_URL=https://www.reyoutube.com
   ```

2. **Customize Sitemap**:
   - Edit `next-sitemap.config.js` to add or modify routes
   - Use `additionalPaths` configuration for dynamic routes

3. **Build and Generate**:
   ```bash
   # Build project (includes postbuild hook for sitemap generation)
   pnpm run build

   # Or generate sitemap separately
   pnpm run sitemap
   ```

### Access Sitemaps

After building and deploying:
- **Sitemap Index**: `https://www.reyoutube.com/sitemap.xml`
- **Detailed Sitemap**: `https://www.reyoutube.com/sitemap-0.xml`
- **Robots File**: `https://www.reyoutube.com/robots.txt`

## 🤝 Contributing

We welcome contributions to ReYoutube Web! Please follow these guidelines:

### Getting Started

1. **Fork the Repository**
2. **Create a Feature Branch**: `git checkout -b feature/amazing-feature`
3. **Make Changes**: Follow our coding standards
4. **Test Your Changes**: Ensure all tests pass
5. **Commit Changes**: Use conventional commit messages
6. **Push to Branch**: `git push origin feature/amazing-feature`
7. **Open Pull Request**: Provide detailed description

### Development Guidelines

- **Code Style**: Follow ESLint and Prettier configurations
- **Testing**: Add tests for new features
- **Documentation**: Update documentation for significant changes
- **Performance**: Consider performance implications
- **Accessibility**: Ensure accessibility standards are met

### Commit Message Format

```
type(scope): description

[optional body]

[optional footer]
```

**Types**: feat, fix, docs, style, refactor, test, chore

### Pull Request Process

1. Update README.md with details of changes if applicable
2. Update the version numbers following SemVer
3. Ensure all tests pass and no linting errors
4. Request review from maintainers

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- **Next.js Team**: For the amazing React framework
- **Vercel**: For hosting and deployment platform
- **Prisma**: For the excellent database toolkit
- **Radix UI**: For accessible UI components
- **Tailwind CSS**: For utility-first CSS framework

## 📞 Support

- **Documentation**: Check this README and inline code comments
- **Issues**: Report bugs and request features via GitHub Issues
- **Discussions**: Join community discussions in GitHub Discussions
- **Email**: Contact the maintainers at [<EMAIL>](mailto:<EMAIL>)

## 🗺️ Roadmap

### Phase 1 (Current)
- ✅ Core analytics dashboard
- ✅ Channel search and discovery
- ✅ User authentication
- ✅ Multi-language support
- 🔄 Mobile responsiveness optimization
- 🔄 Performance improvements

### Phase 2 (Upcoming)
- 📋 Advanced filtering and sorting
- 📋 Export functionality
- 📋 API rate limiting
- 📋 Advanced user roles
- 📋 Webhook integrations

### Phase 3 (Future)
- 📋 Machine learning insights
- 📋 Custom dashboard builder
- 📋 Third-party integrations
- 📋 White-label solutions

---

<div align="center">
  <p>Made with ❤️ by the ReYoutube Team</p>
  <p>
    <a href="https://github.com/wenhaofree/reyoutube-web">GitHub</a> •
    <a href="https://reyoutube.com">Website</a> •
    <a href="mailto:<EMAIL>">Contact</a>
  </p>
</div>