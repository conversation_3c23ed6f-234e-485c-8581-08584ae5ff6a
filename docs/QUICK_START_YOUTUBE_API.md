# YouTube API 快速启动指南

本指南将帮助您快速配置和测试 YouTube Data API v3 集成。

## 🚀 快速开始

### 1. 获取 YouTube API Key

1. 访问 [Google Cloud Console](https://console.cloud.google.com/)
2. 创建项目或选择现有项目
3. 启用 "YouTube Data API v3"
4. 创建 API 密钥
5. 复制 API 密钥

### 2. 配置环境变量

在项目根目录创建或编辑 `.env.local` 文件：

```env
YOUTUBE_API_KEY=your-youtube-api-key-here
```

### 3. 启动开发服务器

```bash
pnpm dev
```

### 4. 测试 API 功能

#### 方法一：使用测试页面
访问: http://localhost:3000/test/youtube-api

#### 方法二：使用测试脚本
```bash
pnpm run test:youtube-api
```

#### 方法三：直接 API 调用
```bash
# 测试频道 API
curl "http://localhost:3000/api/youtube/channels?username=GoogleDevelopers"

# 测试视频 API  
curl "http://localhost:3000/api/youtube/videos?id=dQw4w9WgXcQ"
```

## 📋 测试用例

### 频道测试
- **Google Developers**: `@GoogleDevelopers` 或 `UCVHFbqXqoYvEWM1Ddxl0QDg`
- **TechCrunch**: `@TechCrunch` 或 `UCCjyq_K1Xwfg8Lndy7lKMpA`
- **Marques Brownlee**: `@mkbhd` 或 `UCBJycsmduvYEL83R_U4JriQ`

### 视频测试
- **Rick Roll**: `dQw4w9WgXcQ`
- **First YouTube Video**: `jNQXAC9IVRw`
- **Gangnam Style**: `9bZkp7q19f0`

## 🔧 故障排除

### 常见问题

1. **API Key 无效**
   ```
   Error: YouTube API request failed, status: 400, message: API key not valid
   ```
   **解决方案**: 检查 API 密钥是否正确，确保已启用 YouTube Data API v3

2. **配额超限**
   ```
   Error: YouTube API request failed, status: 403, message: Quota exceeded
   ```
   **解决方案**: 等待配额重置或升级到付费计划

3. **环境变量未设置**
   ```
   Error: YouTube API key not configured
   ```
   **解决方案**: 确保 `.env.local` 文件中设置了 `YOUTUBE_API_KEY`

### 验证步骤

1. **检查环境变量**:
   ```bash
   echo $YOUTUBE_API_KEY
   ```

2. **测试 API 连接**:
   ```bash
   curl "https://www.googleapis.com/youtube/v3/channels?part=snippet&id=UCVHFbqXqoYvEWM1Ddxl0QDg&key=YOUR_API_KEY"
   ```

3. **检查服务器日志**:
   查看终端输出中的 `[YouTube API]` 日志

## 📊 API 端点说明

### 频道信息
- **端点**: `/api/youtube/channels`
- **参数**: `id` (频道ID) 或 `username` (用户名)
- **示例**: `/api/youtube/channels?username=GoogleDevelopers`

### 视频信息
- **端点**: `/api/youtube/videos`
- **参数**: `id` (视频ID，必需)
- **示例**: `/api/youtube/videos?id=dQw4w9WgXcQ`

## 🎯 下一步

1. **集成到现有功能**: 将 YouTube API 数据与数据库数据结合
2. **缓存优化**: 实现数据缓存减少 API 调用
3. **错误处理**: 完善错误处理和用户反馈
4. **批量处理**: 实现批量数据获取功能

## 📚 相关文档

- [完整设置指南](./YOUTUBE_API_SETUP.md)
- [YouTube Data API v3 文档](https://developers.google.com/youtube/v3)
- [项目 README](../README.md)

---

如有问题，请查看 [故障排除指南](./YOUTUBE_API_SETUP.md#故障排除) 或提交 Issue。
