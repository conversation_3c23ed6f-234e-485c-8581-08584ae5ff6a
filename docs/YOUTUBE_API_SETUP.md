# YouTube Data API v3 集成指南

本文档详细说明如何在 ReYoutube Web 项目中配置和使用 Google YouTube Data API v3。

## 📋 目录

- [API 概述](#api-概述)
- [获取 API Key](#获取-api-key)
- [环境配置](#环境配置)
- [API 端点](#api-端点)
- [使用示例](#使用示例)
- [测试页面](#测试页面)
- [错误处理](#错误处理)
- [配额限制](#配额限制)

## 🎯 API 概述

YouTube Data API v3 允许应用程序获取 YouTube 内容的详细信息，包括：

- **频道信息**: 频道统计、描述、缩略图等
- **视频信息**: 视频统计、元数据、内容详情等
- **播放列表**: 播放列表内容和信息
- **搜索功能**: 搜索视频、频道和播放列表

## 🔑 获取 API Key

### 1. 创建 Google Cloud 项目

1. 访问 [Google Cloud Console](https://console.cloud.google.com/)
2. 创建新项目或选择现有项目
3. 记录项目ID，后续配置需要

### 2. 启用 YouTube Data API v3

1. 在 Google Cloud Console 中，导航到 **API 和服务** > **库**
2. 搜索 "YouTube Data API v3"
3. 点击 API 并选择 **启用**

### 3. 创建 API 凭据

1. 导航到 **API 和服务** > **凭据**
2. 点击 **创建凭据** > **API 密钥**
3. 复制生成的 API 密钥
4. （推荐）点击 **限制密钥** 来配置使用限制：
   - **应用程序限制**: 选择 "HTTP 引荐来源网址"
   - **API 限制**: 选择 "限制密钥"，然后选择 "YouTube Data API v3"

### 4. 配置 API 密钥限制（推荐）

为了安全起见，建议限制 API 密钥的使用：

```
HTTP 引荐来源网址限制:
- http://localhost:3000/*
- https://yourdomain.com/*
- https://*.yourdomain.com/*
```

## ⚙️ 环境配置

### 1. 添加环境变量

在项目根目录的 `.env.local` 文件中添加：

```env
# YouTube Data API v3 Configuration
YOUTUBE_API_KEY=your-youtube-api-key-here
```

### 2. 验证配置

确保 API 密钥正确配置：

```bash
# 检查环境变量
echo $YOUTUBE_API_KEY

# 或在 Node.js 中
console.log(process.env.YOUTUBE_API_KEY);
```

## 🚀 API 端点

### 频道信息 API

**端点**: `GET /api/youtube/channels`

**参数**:
- `id` (可选): YouTube 频道ID (如: UCxxxxxx)
- `username` (可选): 频道用户名 (如: GoogleDevelopers)
- `part` (可选): 返回数据部分，默认: `snippet,statistics,contentDetails,brandingSettings`

**示例请求**:
```bash
# 通过频道ID获取
GET /api/youtube/channels?id=UCVHFbqXqoYvEWM1Ddxl0QDg

# 通过用户名获取
GET /api/youtube/channels?username=GoogleDevelopers
```

**响应格式**:
```json
{
  "success": true,
  "data": {
    "id": "UCVHFbqXqoYvEWM1Ddxl0QDg",
    "title": "Google Developers",
    "description": "The Google Developers channel...",
    "customUrl": "GoogleDevelopers",
    "publishedAt": "2007-08-23T00:34:43Z",
    "thumbnails": {
      "default": { "url": "..." },
      "medium": { "url": "..." },
      "high": { "url": "..." }
    },
    "statistics": {
      "viewCount": 123456789,
      "subscriberCount": 2340000,
      "videoCount": 5678,
      "hiddenSubscriberCount": false
    },
    "metadata": {
      "subscriberCountFormatted": "2.3M",
      "viewCountFormatted": "123M",
      "videoCountFormatted": "5.7K",
      "createdYearsAgo": 16
    }
  },
  "source": "youtube_api",
  "timestamp": "2024-01-15T10:30:00.000Z"
}
```

### 视频信息 API

**端点**: `GET /api/youtube/videos`

**参数**:
- `id` (必需): YouTube 视频ID
- `part` (可选): 返回数据部分，默认: `snippet,statistics,contentDetails`

**示例请求**:
```bash
GET /api/youtube/videos?id=dQw4w9WgXcQ
```

**响应格式**:
```json
{
  "success": true,
  "data": {
    "id": "dQw4w9WgXcQ",
    "title": "Rick Astley - Never Gonna Give You Up",
    "description": "The official video for...",
    "publishedAt": "2009-10-25T06:57:33Z",
    "channelId": "UCuAXFkgsw1L7xaCfnd5JJOw",
    "channelTitle": "Rick Astley",
    "thumbnails": { "..." },
    "statistics": {
      "viewCount": 1234567890,
      "likeCount": 12345678,
      "commentCount": 1234567
    },
    "contentDetails": {
      "duration": "PT3M33S",
      "definition": "hd",
      "caption": true
    },
    "metadata": {
      "viewCountFormatted": "1.2B",
      "likeCountFormatted": "12M",
      "commentCountFormatted": "1.2M",
      "durationFormatted": "3:33",
      "publishedDaysAgo": 5234
    }
  },
  "source": "youtube_api",
  "timestamp": "2024-01-15T10:30:00.000Z"
}
```

## 💡 使用示例

### 在 React 组件中使用

```typescript
import { useState } from 'react';

interface ChannelData {
  id: string;
  title: string;
  statistics: {
    subscriberCount: number;
    viewCount: number;
    videoCount: number;
  };
}

export function ChannelInfo({ channelId }: { channelId: string }) {
  const [channel, setChannel] = useState<ChannelData | null>(null);
  const [loading, setLoading] = useState(false);

  const fetchChannel = async () => {
    setLoading(true);
    try {
      const response = await fetch(`/api/youtube/channels?id=${channelId}`);
      const result = await response.json();
      
      if (result.success) {
        setChannel(result.data);
      }
    } catch (error) {
      console.error('Failed to fetch channel:', error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div>
      <button onClick={fetchChannel} disabled={loading}>
        {loading ? 'Loading...' : 'Fetch Channel'}
      </button>
      
      {channel && (
        <div>
          <h2>{channel.title}</h2>
          <p>Subscribers: {channel.statistics.subscriberCount}</p>
          <p>Views: {channel.statistics.viewCount}</p>
          <p>Videos: {channel.statistics.videoCount}</p>
        </div>
      )}
    </div>
  );
}
```

### 在服务端使用

```typescript
// pages/api/example.ts
import type { NextApiRequest, NextApiResponse } from 'next';

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  const channelId = req.query.channelId as string;
  
  try {
    const response = await fetch(
      `${process.env.NEXT_PUBLIC_WEB_URL}/api/youtube/channels?id=${channelId}`
    );
    const data = await response.json();
    
    res.status(200).json(data);
  } catch (error) {
    res.status(500).json({ error: 'Failed to fetch channel data' });
  }
}
```

## 🧪 测试页面

项目包含一个完整的测试页面，用于验证 API 功能：

**访问地址**: `http://localhost:3000/test/youtube-api`

### 测试功能

1. **频道信息测试**:
   - 支持频道ID和用户名输入
   - 显示完整的频道统计信息
   - 展示频道缩略图和描述

2. **视频信息测试**:
   - 支持视频ID和完整YouTube链接
   - 显示视频统计和元数据
   - 展示视频缩略图和标签

### 测试用例

```bash
# 频道测试用例
@GoogleDevelopers
UCVHFbqXqoYvEWM1Ddxl0QDg
GoogleDevelopers

# 视频测试用例
dQw4w9WgXcQ
https://www.youtube.com/watch?v=dQw4w9WgXcQ
https://youtu.be/dQw4w9WgXcQ
```

## ❌ 错误处理

### 常见错误类型

1. **API Key 未配置**:
```json
{
  "error": "YouTube API key not configured",
  "message": "YOUTUBE_API_KEY environment variable is required"
}
```

2. **频道/视频未找到**:
```json
{
  "error": "Channel not found",
  "message": "No channel found for ID: UCxxxxxx"
}
```

3. **API 配额超限**:
```json
{
  "error": "YouTube API request failed",
  "status": 403,
  "message": "Quota exceeded"
}
```

4. **无效的 API Key**:
```json
{
  "error": "YouTube API request failed",
  "status": 400,
  "message": "API key not valid"
}
```

### 错误处理最佳实践

```typescript
async function fetchYouTubeData(endpoint: string) {
  try {
    const response = await fetch(endpoint);
    const data = await response.json();
    
    if (!response.ok) {
      // 处理 API 错误
      throw new Error(data.message || `HTTP ${response.status}`);
    }
    
    if (!data.success) {
      // 处理业务逻辑错误
      throw new Error(data.error || 'Request failed');
    }
    
    return data.data;
  } catch (error) {
    console.error('YouTube API Error:', error);
    throw error;
  }
}
```

## 📊 配额限制

### YouTube Data API v3 配额

- **每日配额**: 10,000 单位（免费层）
- **每个请求成本**:
  - `channels.list`: 1 单位
  - `videos.list`: 1 单位
  - `search.list`: 100 单位

### 优化配额使用

1. **缓存结果**: 避免重复请求相同数据
2. **批量请求**: 一次请求多个资源
3. **选择性字段**: 只请求需要的 `part` 参数
4. **错误重试**: 实现指数退避重试机制

### 监控配额使用

```typescript
// 在 API 响应中添加配额信息
const response = await fetch(apiUrl);
const quotaUsed = response.headers.get('X-RateLimit-Remaining');
console.log(`Remaining quota: ${quotaUsed}`);
```

## 🔧 故障排除

### 1. API Key 问题

```bash
# 测试 API Key 是否有效
curl "https://www.googleapis.com/youtube/v3/channels?part=snippet&id=UCVHFbqXqoYvEWM1Ddxl0QDg&key=YOUR_API_KEY"
```

### 2. 网络问题

```bash
# 检查网络连接
curl -I https://www.googleapis.com/youtube/v3/

# 检查 DNS 解析
nslookup www.googleapis.com
```

### 3. 环境变量问题

```bash
# 检查环境变量
printenv | grep YOUTUBE
```

## 📚 相关资源

- [YouTube Data API v3 官方文档](https://developers.google.com/youtube/v3)
- [Google Cloud Console](https://console.cloud.google.com/)
- [API Explorer](https://developers.google.com/youtube/v3/docs/)
- [配额和限制](https://developers.google.com/youtube/v3/getting-started#quota)

---

如有问题，请查看项目的 [GitHub Issues](https://github.com/wenhaofree/reyoutube-web/issues) 或联系开发团队。
