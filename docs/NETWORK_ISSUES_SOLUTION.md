# 网络连接问题解决方案

## 🔍 问题诊断结果

根据网络诊断测试，您的环境存在以下情况：

### ✅ 正常功能
- **DNS 解析**: 能够正确解析 Google 域名
- **API Key 配置**: YouTube API Key 已正确配置
- **本地服务**: Next.js 开发服务器运行正常

### ❌ 问题所在
- **HTTPS 连接超时**: 无法连接到 Google APIs (googleapis.com)
- **网络限制**: 可能存在防火墙、代理或地区限制

## 🛠️ 解决方案

### 方案 1: 使用模拟数据测试界面 (推荐)

我已经为您创建了模拟 API 端点，可以在网络受限的情况下测试界面功能：

#### 模拟频道 API
```bash
# 测试模拟频道 API
curl "http://localhost:3000/api/youtube/channels/mock?username=GoogleDevelopers"
curl "http://localhost:3000/api/youtube/channels/mock?id=UCVHFbqXqoYvEWM1Ddxl0QDg"
curl "http://localhost:3000/api/youtube/channels/mock?username=TechCrunch"
```

#### 模拟视频 API
```bash
# 测试模拟视频 API
curl "http://localhost:3000/api/youtube/videos/mock?id=dQw4w9WgXcQ"
curl "http://localhost:3000/api/youtube/videos/mock?id=jNQXAC9IVRw"
curl "http://localhost:3000/api/youtube/videos/mock?id=9bZkp7q19f0"
```

#### 可用的测试数据
- **频道**: GoogleDevelopers, TechCrunch, UCVHFbqXqoYvEWM1Ddxl0QDg
- **视频**: dQw4w9WgXcQ (Rick Roll), jNQXAC9IVRw (First YouTube video), 9bZkp7q19f0 (Gangnam Style)

### 方案 2: 网络配置解决

#### 2.1 检查防火墙设置
```bash
# macOS 检查防火墙
sudo pfctl -sr | grep -i google

# 临时禁用防火墙测试 (谨慎使用)
sudo pfctl -d
```

#### 2.2 使用代理服务器
如果您的网络需要代理，可以配置环境变量：

```bash
# 在 .env.local 中添加
HTTP_PROXY=http://your-proxy:port
HTTPS_PROXY=https://your-proxy:port
```

#### 2.3 使用 VPN
如果 Google 服务在您的地区被限制，建议使用 VPN 连接。

#### 2.4 DNS 设置
尝试使用不同的 DNS 服务器：

```bash
# 使用 Google DNS
networksetup -setdnsservers Wi-Fi ******* *******

# 使用 Cloudflare DNS
networksetup -setdnsservers Wi-Fi ******* *******
```

### 方案 3: 企业网络环境

如果您在企业网络环境中，请联系网络管理员：

1. **白名单域名**:
   - `*.googleapis.com`
   - `*.youtube.com`
   - `*.google.com`

2. **开放端口**: 443 (HTTPS)

3. **证书信任**: 确保 Google 的 SSL 证书被信任

## 🧪 测试步骤

### 1. 测试模拟 API
```bash
# 运行模拟 API 测试
pnpm run test:youtube-api:mock  # (需要添加此脚本)

# 或直接访问测试页面
http://localhost:3000/test/youtube-api?mock=true
```

### 2. 网络诊断
```bash
# 运行网络诊断
pnpm run diagnose:network

# 基础网络测试
ping google.com
curl -I https://www.googleapis.com
nslookup www.googleapis.com
```

### 3. 逐步测试真实 API
一旦网络问题解决，可以逐步测试：

```bash
# 1. 测试基础连接
curl -I https://www.googleapis.com

# 2. 测试 YouTube API
curl "https://www.googleapis.com/youtube/v3/channels?part=snippet&id=UCVHFbqXqoYvEWM1Ddxl0QDg&key=YOUR_API_KEY"

# 3. 测试本地 API
curl "http://localhost:3000/api/youtube/channels?username=GoogleDevelopers"
```

## 📊 日志分析

### 成功的日志应该显示：
```
[YouTube API] ✅ API Key 已配置 (长度: 39)
[YouTube API] 📡 发送请求到 YouTube API...
[YouTube API] ⏱️  API 请求耗时: 1234ms
[YouTube API] 📊 响应状态: 200
[YouTube API] ✅ 成功获取数据，找到 1 个频道
```

### 网络问题的日志显示：
```
[YouTube API] 💥 处理请求时发生错误: TypeError: fetch failed
[cause]: [Error [ConnectTimeoutError]: Connect Timeout Error
```

## 🔧 开发建议

### 1. 添加降级机制
在生产环境中，建议添加降级机制：

```typescript
// 先尝试真实 API，失败时使用缓存或模拟数据
async function fetchChannelData(channelId: string) {
  try {
    return await fetchFromYouTubeAPI(channelId);
  } catch (error) {
    console.warn('YouTube API failed, using fallback data');
    return await fetchFromCache(channelId) || getMockData(channelId);
  }
}
```

### 2. 添加重试机制
```typescript
async function fetchWithRetry(url: string, retries = 3) {
  for (let i = 0; i < retries; i++) {
    try {
      return await fetch(url, { timeout: 10000 });
    } catch (error) {
      if (i === retries - 1) throw error;
      await new Promise(resolve => setTimeout(resolve, 1000 * (i + 1)));
    }
  }
}
```

### 3. 环境检测
```typescript
// 检测网络环境并选择合适的 API
const isNetworkRestricted = await checkNetworkAccess();
const apiEndpoint = isNetworkRestricted 
  ? '/api/youtube/channels/mock' 
  : '/api/youtube/channels';
```

## 📚 相关资源

- [网络诊断脚本](../scripts/diagnose-network.js)
- [模拟 API 文档](./MOCK_API_USAGE.md)
- [YouTube API 设置指南](./YOUTUBE_API_SETUP.md)
- [故障排除指南](./TROUBLESHOOTING.md)

## 💡 下一步

1. **立即可用**: 使用模拟 API 测试界面功能
2. **网络修复**: 根据上述方案解决网络连接问题
3. **生产部署**: 在生产环境中实施降级和重试机制
4. **监控告警**: 添加 API 可用性监控

---

如需进一步帮助，请查看详细的故障排除指南或联系技术支持。
