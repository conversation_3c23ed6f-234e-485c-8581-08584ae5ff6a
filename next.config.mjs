import createNextIntlPlugin from 'next-intl/plugin';

const withNextIntl = createNextIntlPlugin();

// Bundle Analyzer 配置
import pkg from '@next/bundle-analyzer';
const withBundleAnalyzer = pkg({
  enabled: process.env.ANALYZE === 'true',
});

/** @type {import('next').NextConfig} */
const nextConfig = {
  reactStrictMode: true,
  // 环境变量配置
  env: {
    NEXT_PUBLIC_API_URL: process.env.NEXT_PUBLIC_API_URL,
  },
  // 生产环境优化
  compress: process.env.NODE_ENV === 'production',
  poweredByHeader: false,
  generateEtags: true,
  // 缓存策略
  onDemandEntries: {
    maxInactiveAge: 60 * 1000,
    pagesBufferLength: 5,
  },
  // 构建优化
  // swcMinify: true, // 此选项在Next.js 15中已不再支持，默认启用
  compiler: {
    removeConsole: process.env.NODE_ENV === 'production',
  },
  // 实验性功能
  experimental: {
    optimizeCss: true,
    optimizePackageImports: ['@radix-ui/react-icons', 'lucide-react'],
  },
  // 输出优化
  output: 'standalone',
  // 允许的开发源
  allowedDevOrigins: [
    'http://127.0.0.1:3000',
  ],
  images: {
    remotePatterns: [
      {
        protocol: 'https',
        hostname: 'avatars.githubusercontent.com',
      },
      {
        protocol: 'https',
        hostname: 'i.ytimg.com',
      },
      {
        protocol: 'https',
        hostname: 'img.youtube.com',
      },
      {
        protocol: 'https',
        hostname: 'storage.googleapis.com',
      },
    ],
    formats: ['image/avif', 'image/webp'],
    minimumCacheTTL: 60,
    deviceSizes: [640, 750, 828, 1080, 1200, 1920, 2048, 3840],
    imageSizes: [16, 32, 48, 64, 96, 128, 256, 384],
  },
  // 在类型问题完全解决前暂时禁用类型检查和 ESLint 检查
  typescript: {
    ignoreBuildErrors: true,
  },
  eslint: {
    ignoreDuringBuilds: true,
  },
};

export default withNextIntl(withBundleAnalyzer(nextConfig));
