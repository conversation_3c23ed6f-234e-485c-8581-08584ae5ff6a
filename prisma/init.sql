-- -------------------------------------------------------------------------
-- YouTube数据采集系统数据库初始化脚本
-- 数据库: PostgreSQL
-- 创建日期: 2023-12-01
-- 描述: 此脚本创建YouTube数据采集系统所需的所有表结构
-- -------------------------------------------------------------------------

-- 原始数据抓取表 - 存储从YouTube API获取的原始JSON数据
CREATE TABLE ytb_raw_fetch (
    id              BIGSERIAL       PRIMARY KEY,
    task_id         UUID            NOT NULL,                   -- 关联的任务ID
    endpoint        VARCHAR(255)    NOT NULL,                   -- API端点路径
    response        JSONB           NOT NULL,                   -- 原始JSON响应数据
    params          JSONB,                                      -- 请求参数
    etag            VARCHAR(255),                               -- YouTube API返回的ETag标识
    quota_cost      INTEGER         NOT NULL DEFAULT 1,          -- API配额消耗量
    fetched_at      TIMESTAMPTZ     NOT NULL DEFAULT NOW(),     -- 数据获取时间
    created_at      TIMESTAMPTZ     NOT NULL DEFAULT NOW(),     -- 记录创建时间
    updated_at      TIMESTAMPTZ     NOT NULL DEFAULT NOW(),     -- 记录更新时间
    deleted_at      TIMESTAMPTZ                                 -- 记录软删除时间
);
COMMENT ON TABLE ytb_raw_fetch IS '存储从YouTube API获取的原始JSON数据';
COMMENT ON COLUMN ytb_raw_fetch.id IS '主键ID';
COMMENT ON COLUMN ytb_raw_fetch.task_id IS '关联的采集任务ID';
COMMENT ON COLUMN ytb_raw_fetch.endpoint IS 'YouTube API端点路径';
COMMENT ON COLUMN ytb_raw_fetch.response IS '原始JSON响应数据';
COMMENT ON COLUMN ytb_raw_fetch.params IS '请求参数JSON格式';
COMMENT ON COLUMN ytb_raw_fetch.etag IS 'YouTube资源ETag，用于缓存控制';
COMMENT ON COLUMN ytb_raw_fetch.fetched_at IS '数据获取时间戳';
COMMENT ON COLUMN ytb_raw_fetch.created_at IS '记录创建时间';
COMMENT ON COLUMN ytb_raw_fetch.quota_cost IS 'YouTube API配额消耗量';
COMMENT ON COLUMN ytb_raw_fetch.updated_at IS '记录更新时间';
COMMENT ON COLUMN ytb_raw_fetch.deleted_at IS '记录软删除时间';

CREATE INDEX idx_ytb_raw_fetch_task_id ON ytb_raw_fetch(task_id);
CREATE INDEX idx_ytb_raw_fetch_endpoint ON ytb_raw_fetch(endpoint);

-- 任务记录表 - 跟踪所有数据采集任务
CREATE TABLE ytb_tasks (
    id              UUID            PRIMARY KEY DEFAULT gen_random_uuid(),
    task_type       VARCHAR(50)     NOT NULL,                   -- 任务类型：channel_info/video_info/comments/analytics
    status          VARCHAR(20)     NOT NULL DEFAULT 'pending', -- 任务状态：pending/running/success/failed
    parameters      JSONB           NOT NULL,                   -- 任务参数
    result          JSONB,                                      -- 任务结果
    error_message   TEXT,                                       -- 错误信息
    started_at      TIMESTAMPTZ,                                -- 开始执行时间
    completed_at    TIMESTAMPTZ,                                -- 完成时间
    retry_count     INTEGER         NOT NULL DEFAULT 0,         -- 重试次数
    next_retry_at   TIMESTAMPTZ,                                 -- 下次重试时间
    created_at      TIMESTAMPTZ     NOT NULL DEFAULT NOW(),     -- 创建时间
    updated_at      TIMESTAMPTZ     NOT NULL DEFAULT NOW(),     -- 记录更新时间
    deleted_at      TIMESTAMPTZ                                 -- 记录软删除时间
);
COMMENT ON TABLE ytb_tasks IS '跟踪所有YouTube数据采集任务的执行状态';
COMMENT ON COLUMN ytb_tasks.id IS '任务唯一标识符';
COMMENT ON COLUMN ytb_tasks.task_type IS '任务类型：channel_info/video_info/comments/analytics等';
COMMENT ON COLUMN ytb_tasks.status IS '任务执行状态：pending/running/success/failed';
COMMENT ON COLUMN ytb_tasks.parameters IS '任务执行参数，JSON格式';
COMMENT ON COLUMN ytb_tasks.result IS '任务执行结果摘要，JSON格式';
COMMENT ON COLUMN ytb_tasks.error_message IS '任务失败时的错误信息';
COMMENT ON COLUMN ytb_tasks.created_at IS '任务创建时间';
COMMENT ON COLUMN ytb_tasks.started_at IS '任务开始执行时间';
COMMENT ON COLUMN ytb_tasks.completed_at IS '任务完成时间';
COMMENT ON COLUMN ytb_tasks.retry_count IS '任务重试次数';
COMMENT ON COLUMN ytb_tasks.next_retry_at IS '下次重试时间';
COMMENT ON COLUMN ytb_tasks.updated_at IS '记录更新时间';
COMMENT ON COLUMN ytb_tasks.deleted_at IS '记录软删除时间';

CREATE INDEX idx_ytb_tasks_status ON ytb_tasks(status);
CREATE INDEX idx_ytb_tasks_created_at ON ytb_tasks(created_at);
CREATE INDEX idx_ytb_tasks_next_retry_at ON ytb_tasks(next_retry_at);

-- 频道表 - 存储YouTube频道的基本信息
CREATE TABLE ytb_channels (
    id                  BIGSERIAL       PRIMARY KEY,
    channel_id          VARCHAR(50)     NOT NULL UNIQUE,        -- YouTube频道ID
    title               VARCHAR(255)    NOT NULL,               -- 频道标题
    description         TEXT,                                   -- 频道描述
    custom_url          VARCHAR(100),                           -- 自定义URL
    published_at        TIMESTAMPTZ,                            -- 频道创建时间
    country             VARCHAR(2),                             -- 国家/地区代码
    view_count          BIGINT,                                 -- 总观看次数
    subscriber_count    INTEGER,                                -- 订阅者数量
    video_count         INTEGER,                                -- 视频数量
    topic_categories    TEXT[],                                 -- 主题分类
    thumbnail_url       TEXT,                                   -- 缩略图URL
    banner_url          TEXT,                                   -- 横幅图URL
    last_refreshed_at   TIMESTAMPTZ     NOT NULL DEFAULT NOW(), -- 最后更新时间
    is_verified         BOOLEAN         DEFAULT FALSE,          -- 是否已验证
    created_at          TIMESTAMPTZ     NOT NULL DEFAULT NOW(), -- 记录创建时间
    updated_at          TIMESTAMPTZ     NOT NULL DEFAULT NOW(), -- 记录更新时间
    deleted_at          TIMESTAMPTZ                             -- 软删除时间
);
COMMENT ON TABLE ytb_channels IS '存储YouTube频道的基本信息';
COMMENT ON COLUMN ytb_channels.id IS '主键ID';
COMMENT ON COLUMN ytb_channels.channel_id IS 'YouTube平台分配的频道ID';
COMMENT ON COLUMN ytb_channels.title IS '频道标题/名称';
COMMENT ON COLUMN ytb_channels.description IS '频道描述内容';
COMMENT ON COLUMN ytb_channels.custom_url IS '频道自定义URL';
COMMENT ON COLUMN ytb_channels.published_at IS '频道在YouTube上的创建时间';
COMMENT ON COLUMN ytb_channels.country IS '频道所属国家/地区ISO代码';
COMMENT ON COLUMN ytb_channels.view_count IS '频道总观看次数';
COMMENT ON COLUMN ytb_channels.subscriber_count IS '频道订阅者数量';
COMMENT ON COLUMN ytb_channels.video_count IS '频道视频数量';
COMMENT ON COLUMN ytb_channels.topic_categories IS '频道主题分类列表';
COMMENT ON COLUMN ytb_channels.thumbnail_url IS '频道缩略图URL';
COMMENT ON COLUMN ytb_channels.banner_url IS '频道横幅图URL';
COMMENT ON COLUMN ytb_channels.last_refreshed_at IS '频道数据最后更新时间';
COMMENT ON COLUMN ytb_channels.is_verified IS '频道是否已验证';
COMMENT ON COLUMN ytb_channels.created_at IS '记录创建时间';
COMMENT ON COLUMN ytb_channels.updated_at IS '记录更新时间';
COMMENT ON COLUMN ytb_channels.deleted_at IS '记录软删除时间';

CREATE INDEX idx_ytb_channels_channel_id ON ytb_channels(channel_id);
CREATE INDEX idx_ytb_channels_last_refreshed_at ON ytb_channels(last_refreshed_at);

-- 频道统计快照表 - 存储频道数据的历史快照，实现时间序列分析
CREATE TABLE ytb_channel_stats_snapshots (
    id                  BIGSERIAL       PRIMARY KEY,
    channel_id          BIGINT          NOT NULL REFERENCES ytb_channels(id), -- 关联频道ID
    view_count          BIGINT          NOT NULL DEFAULT 0,       -- 快照时的观看次数
    subscriber_count    INTEGER         NOT NULL DEFAULT 0,       -- 快照时的订阅者数量
    video_count         INTEGER         NOT NULL DEFAULT 0,       -- 快照时的视频数量
    snapshot_at         TIMESTAMPTZ     NOT NULL DEFAULT NOW(),   -- 快照时间
    created_at          TIMESTAMPTZ     NOT NULL DEFAULT NOW(),   -- 记录创建时间
    updated_at          TIMESTAMPTZ     NOT NULL DEFAULT NOW(),   -- 记录更新时间
    deleted_at          TIMESTAMPTZ                                 -- 记录软删除时间
);
COMMENT ON TABLE ytb_channel_stats_snapshots IS '存储频道统计数据的历史快照，用于时间序列分析';
COMMENT ON COLUMN ytb_channel_stats_snapshots.id IS '快照记录ID';
COMMENT ON COLUMN ytb_channel_stats_snapshots.channel_id IS '关联的频道ID';
COMMENT ON COLUMN ytb_channel_stats_snapshots.view_count IS '快照时的频道总观看次数';
COMMENT ON COLUMN ytb_channel_stats_snapshots.subscriber_count IS '快照时的频道订阅者数量';
COMMENT ON COLUMN ytb_channel_stats_snapshots.video_count IS '快照时的频道视频数量';
COMMENT ON COLUMN ytb_channel_stats_snapshots.snapshot_at IS '快照采集时间';
COMMENT ON COLUMN ytb_channel_stats_snapshots.created_at IS '记录创建时间';
COMMENT ON COLUMN ytb_channel_stats_snapshots.updated_at IS '记录更新时间';
COMMENT ON COLUMN ytb_channel_stats_snapshots.deleted_at IS '记录软删除时间';

CREATE INDEX idx_ytb_channel_stats_channel_id_snapshot_at ON ytb_channel_stats_snapshots(channel_id, snapshot_at);

-- 视频表 - 存储YouTube视频的基本信息
CREATE TABLE ytb_videos (
    id                  BIGSERIAL       PRIMARY KEY,
    youtube_id          VARCHAR(50)     NOT NULL UNIQUE,        -- YouTube视频ID
    channel_id          VARCHAR(50)     NOT NULL,               -- 所属频道ID
    title               VARCHAR(255)    NOT NULL,               -- 视频标题
    description         TEXT,                                   -- 视频描述
    published_at        TIMESTAMPTZ,                            -- 发布时间
    duration            INTERVAL,                               -- 视频时长
    dimension           VARCHAR(10),                            -- 视频尺寸(2d/3d)
    caption             BOOLEAN         DEFAULT FALSE,          -- 是否有字幕
    licensed_content    BOOLEAN         DEFAULT FALSE,          -- 是否为授权内容
    tags                TEXT[],                                 -- 视频标签
    category_id         INTEGER,                                -- 分类ID
    default_language    VARCHAR(10),                            -- 默认语言
    thumbnail_url       TEXT,                                   -- 缩略图URL
    view_count          BIGINT,                                 -- 观看次数
    like_count          INTEGER,                                -- 点赞数
    dislike_count       INTEGER,                                -- 不喜欢数(已弃用但保留)
    favorite_count      INTEGER,                                -- 收藏数
    comment_count       INTEGER,                                -- 评论数
    privacy_status      VARCHAR(20),                            -- 隐私状态(public/private/unlisted)
    last_refreshed_at   TIMESTAMPTZ     NOT NULL DEFAULT NOW(), -- 最后更新时间
    created_at          TIMESTAMPTZ     NOT NULL DEFAULT NOW(), -- 记录创建时间
    updated_at          TIMESTAMPTZ     NOT NULL DEFAULT NOW(), -- 记录更新时间
    deleted_at          TIMESTAMPTZ                             -- 软删除时间
);
COMMENT ON TABLE ytb_videos IS '存储YouTube视频的基本信息';
COMMENT ON COLUMN ytb_videos.id IS '主键ID';
COMMENT ON COLUMN ytb_videos.youtube_id IS 'YouTube平台分配的视频ID';
COMMENT ON COLUMN ytb_videos.channel_id IS '视频所属频道ID';
COMMENT ON COLUMN ytb_videos.title IS '视频标题';
COMMENT ON COLUMN ytb_videos.description IS '视频描述内容';
COMMENT ON COLUMN ytb_videos.published_at IS '视频发布时间';
COMMENT ON COLUMN ytb_videos.duration IS '视频时长，ISO 8601格式';
COMMENT ON COLUMN ytb_videos.dimension IS '视频尺寸(2d/3d)';
COMMENT ON COLUMN ytb_videos.caption IS '是否有字幕';
COMMENT ON COLUMN ytb_videos.licensed_content IS '是否为授权内容';
COMMENT ON COLUMN ytb_videos.tags IS '视频标签列表';
COMMENT ON COLUMN ytb_videos.category_id IS '视频分类ID';
COMMENT ON COLUMN ytb_videos.default_language IS '视频默认语言代码';
COMMENT ON COLUMN ytb_videos.thumbnail_url IS '视频缩略图URL';
COMMENT ON COLUMN ytb_videos.view_count IS '视频观看次数';
COMMENT ON COLUMN ytb_videos.like_count IS '视频点赞数';
COMMENT ON COLUMN ytb_videos.dislike_count IS '视频不喜欢数(API已弃用但保留字段)';
COMMENT ON COLUMN ytb_videos.favorite_count IS '视频收藏数';
COMMENT ON COLUMN ytb_videos.comment_count IS '视频评论数';
COMMENT ON COLUMN ytb_videos.privacy_status IS '视频隐私状态(public/private/unlisted)';
COMMENT ON COLUMN ytb_videos.last_refreshed_at IS '视频数据最后更新时间';
COMMENT ON COLUMN ytb_videos.created_at IS '记录创建时间';
COMMENT ON COLUMN ytb_videos.updated_at IS '记录更新时间';
COMMENT ON COLUMN ytb_videos.deleted_at IS '记录软删除时间';

CREATE INDEX idx_ytb_videos_youtube_id ON ytb_videos(youtube_id);
CREATE INDEX idx_ytb_videos_channel_id ON ytb_videos(channel_id);
CREATE INDEX idx_ytb_videos_published_at ON ytb_videos(published_at);
CREATE INDEX idx_ytb_videos_last_refreshed_at ON ytb_videos(last_refreshed_at);

-- 视频统计快照表 - 存储视频数据的历史快照，实现时间序列分析
CREATE TABLE ytb_video_stats_snapshots (
    id                  BIGSERIAL       PRIMARY KEY,
    video_id            BIGINT          NOT NULL REFERENCES ytb_videos(id), -- 关联视频ID
    view_count          BIGINT          NOT NULL DEFAULT 0,       -- 快照时的观看次数
    like_count          INTEGER         NOT NULL DEFAULT 0,       -- 快照时的点赞数
    dislike_count       INTEGER         NOT NULL DEFAULT 0,       -- 快照时的不喜欢数
    favorite_count      INTEGER         NOT NULL DEFAULT 0,       -- 快照时的收藏数
    comment_count       INTEGER         NOT NULL DEFAULT 0,       -- 快照时的评论数
    snapshot_at         TIMESTAMPTZ     NOT NULL DEFAULT NOW(),   -- 快照时间
    created_at          TIMESTAMPTZ     NOT NULL DEFAULT NOW(),   -- 记录创建时间
    updated_at          TIMESTAMPTZ     NOT NULL DEFAULT NOW(),   -- 记录更新时间
    deleted_at          TIMESTAMPTZ                                 -- 记录软删除时间
);
COMMENT ON TABLE ytb_video_stats_snapshots IS '存储视频统计数据的历史快照，用于时间序列分析';
COMMENT ON COLUMN ytb_video_stats_snapshots.id IS '快照记录ID';
COMMENT ON COLUMN ytb_video_stats_snapshots.video_id IS '关联的视频ID';
COMMENT ON COLUMN ytb_video_stats_snapshots.view_count IS '快照时的视频观看次数';
COMMENT ON COLUMN ytb_video_stats_snapshots.like_count IS '快照时的视频点赞数';
COMMENT ON COLUMN ytb_video_stats_snapshots.dislike_count IS '快照时的视频不喜欢数';
COMMENT ON COLUMN ytb_video_stats_snapshots.favorite_count IS '快照时的视频收藏数';
COMMENT ON COLUMN ytb_video_stats_snapshots.comment_count IS '快照时的视频评论数';
COMMENT ON COLUMN ytb_video_stats_snapshots.snapshot_at IS '快照采集时间';
COMMENT ON COLUMN ytb_video_stats_snapshots.created_at IS '记录创建时间';
COMMENT ON COLUMN ytb_video_stats_snapshots.updated_at IS '记录更新时间';
COMMENT ON COLUMN ytb_video_stats_snapshots.deleted_at IS '记录软删除时间';

CREATE INDEX idx_ytb_video_stats_video_id_snapshot_at ON ytb_video_stats_snapshots(video_id, snapshot_at);

-- 评论表 - 存储视频评论
CREATE TABLE ytb_comments (
    id                  BIGSERIAL       PRIMARY KEY,
    youtube_id          VARCHAR(50)     NOT NULL UNIQUE,        -- YouTube评论ID
    video_id            BIGINT          NOT NULL REFERENCES ytb_videos(id), -- 关联视频ID
    parent_id           BIGINT          REFERENCES ytb_comments(id), -- 父评论ID(回复)
    author_name         VARCHAR(100)    NOT NULL,               -- 评论者名称
    author_channel_id   VARCHAR(50),                            -- 评论者频道ID
    author_profile_image TEXT,                                  -- 评论者头像URL
    text_display        TEXT            NOT NULL,               -- 评论文本(显示)
    text_original       TEXT            NOT NULL,               -- 评论文本(原始)
    like_count          INTEGER         NOT NULL DEFAULT 0,     -- 点赞数
    published_at        TIMESTAMPTZ     NOT NULL,               -- 发布时间
    can_rate            BOOLEAN         DEFAULT TRUE,           -- 是否可评分
    viewer_rating       VARCHAR(10),                            -- 当前查看者评分
    is_public           BOOLEAN         DEFAULT TRUE,           -- 是否公开
    last_refreshed_at   TIMESTAMPTZ     NOT NULL DEFAULT NOW(), -- 数据最后更新时间
    created_at          TIMESTAMPTZ     NOT NULL DEFAULT NOW(), -- 记录创建时间
    updated_at          TIMESTAMPTZ     NOT NULL DEFAULT NOW(),  -- 记录更新时间
    deleted_at          TIMESTAMPTZ                             -- 软删除时间
);
COMMENT ON TABLE ytb_comments IS '存储YouTube视频的评论数据';
COMMENT ON COLUMN ytb_comments.id IS '主键ID';
COMMENT ON COLUMN ytb_comments.youtube_id IS 'YouTube平台分配的评论ID';
COMMENT ON COLUMN ytb_comments.video_id IS '评论所属视频ID';
COMMENT ON COLUMN ytb_comments.parent_id IS '父评论ID，用于回复关系';
COMMENT ON COLUMN ytb_comments.author_name IS '评论者名称';
COMMENT ON COLUMN ytb_comments.author_channel_id IS '评论者的YouTube频道ID';
COMMENT ON COLUMN ytb_comments.author_profile_image IS '评论者头像URL';
COMMENT ON COLUMN ytb_comments.text_display IS '评论文本(显示版本，可能包含格式)';
COMMENT ON COLUMN ytb_comments.text_original IS '评论文本(原始版本)';
COMMENT ON COLUMN ytb_comments.like_count IS '评论获得的点赞数';
COMMENT ON COLUMN ytb_comments.published_at IS '评论发布时间';
COMMENT ON COLUMN ytb_comments.updated_at IS '记录更新时间';
COMMENT ON COLUMN ytb_comments.can_rate IS '评论是否可评分';
COMMENT ON COLUMN ytb_comments.viewer_rating IS '当前查看者对评论的评分';
COMMENT ON COLUMN ytb_comments.is_public IS '评论是否公开可见';
COMMENT ON COLUMN ytb_comments.last_refreshed_at IS '评论数据最后更新时间';
COMMENT ON COLUMN ytb_comments.created_at IS '记录创建时间';
COMMENT ON COLUMN ytb_comments.deleted_at IS '记录软删除时间';

CREATE INDEX idx_ytb_comments_youtube_id ON ytb_comments(youtube_id);
CREATE INDEX idx_ytb_comments_video_id ON ytb_comments(video_id);
CREATE INDEX idx_ytb_comments_parent_id ON ytb_comments(parent_id);
CREATE INDEX idx_ytb_comments_published_at ON ytb_comments(published_at);

-- 评论情感分析表 - 存储评论的NLP分析结果
CREATE TABLE ytb_comment_analysis (
    id                  BIGSERIAL       PRIMARY KEY,
    comment_id          BIGINT          NOT NULL REFERENCES ytb_comments(id), -- 关联评论ID
    sentiment_score     NUMERIC(4,3),                           -- 情感分数(-1至1)
    sentiment_magnitude NUMERIC(4,3),                           -- 情感强度(0至无穷)
    sentiment_label     VARCHAR(20),                            -- 情感标签(positive/negative/neutral)
    language_detected   VARCHAR(10),                            -- 检测到的语言
    entities            JSONB,                                  -- 提取的实体
    categories          JSONB,                                  -- 内容分类
    analyzed_at         TIMESTAMPTZ     NOT NULL DEFAULT NOW(), -- 分析时间
    created_at          TIMESTAMPTZ     NOT NULL DEFAULT NOW(), -- 记录创建时间
    updated_at          TIMESTAMPTZ     NOT NULL DEFAULT NOW(), -- 记录更新时间
    deleted_at          TIMESTAMPTZ                                 -- 记录软删除时间
);
COMMENT ON TABLE ytb_comment_analysis IS '存储评论的自然语言处理分析结果';
COMMENT ON COLUMN ytb_comment_analysis.id IS '主键ID';
COMMENT ON COLUMN ytb_comment_analysis.comment_id IS '关联的评论ID';
COMMENT ON COLUMN ytb_comment_analysis.sentiment_score IS '情感分析分数，范围从-1(负面)到1(正面)';
COMMENT ON COLUMN ytb_comment_analysis.sentiment_magnitude IS '情感强度，范围从0到无穷';
COMMENT ON COLUMN ytb_comment_analysis.sentiment_label IS '情感标签(positive/negative/neutral)';
COMMENT ON COLUMN ytb_comment_analysis.language_detected IS '检测到的评论语言代码';
COMMENT ON COLUMN ytb_comment_analysis.entities IS '从评论中提取的实体，JSON格式';
COMMENT ON COLUMN ytb_comment_analysis.categories IS '评论内容分类，JSON格式';
COMMENT ON COLUMN ytb_comment_analysis.analyzed_at IS '评论分析时间';
COMMENT ON COLUMN ytb_comment_analysis.created_at IS '记录创建时间';
COMMENT ON COLUMN ytb_comment_analysis.updated_at IS '记录更新时间';
COMMENT ON COLUMN ytb_comment_analysis.deleted_at IS '记录软删除时间';

CREATE UNIQUE INDEX idx_ytb_comment_analysis_comment_id ON ytb_comment_analysis(comment_id);

-- 授权频道表 - 存储用户授权的频道信息，用于获取私密分析数据
CREATE TABLE ytb_authorized_channels (
    id                      BIGSERIAL       PRIMARY KEY,
    channel_id              BIGINT          REFERENCES ytb_channels(id), -- 关联频道ID
    user_id                 VARCHAR(100)    NOT NULL,                  -- 授权用户ID
    refresh_token           TEXT            NOT NULL,                  -- 加密存储的刷新令牌
    access_token            TEXT,                                      -- 加密存储的访问令牌
    token_expiry            TIMESTAMPTZ,                               -- 令牌过期时间
    scope                   TEXT            NOT NULL,                  -- 授权范围
    is_active               BOOLEAN         NOT NULL DEFAULT TRUE,     -- 授权是否有效
    last_token_refresh      TIMESTAMPTZ,                               -- 上次令牌刷新时间
    last_analytics_fetched  TIMESTAMPTZ,                               -- 上次获取分析数据时间
    created_at              TIMESTAMPTZ     NOT NULL DEFAULT NOW(),    -- 记录创建时间
    updated_at              TIMESTAMPTZ     NOT NULL DEFAULT NOW(),    -- 记录更新时间
    deleted_at              TIMESTAMPTZ                                -- 软删除时间
);
COMMENT ON TABLE ytb_authorized_channels IS '存储用户授权的频道信息，用于获取私密分析数据';
COMMENT ON COLUMN ytb_authorized_channels.id IS '主键ID';
COMMENT ON COLUMN ytb_authorized_channels.channel_id IS '关联的频道ID';
COMMENT ON COLUMN ytb_authorized_channels.user_id IS '授权用户ID';
COMMENT ON COLUMN ytb_authorized_channels.refresh_token IS '加密存储的OAuth2刷新令牌';
COMMENT ON COLUMN ytb_authorized_channels.access_token IS '加密存储的OAuth2访问令牌';
COMMENT ON COLUMN ytb_authorized_channels.token_expiry IS '访问令牌过期时间';
COMMENT ON COLUMN ytb_authorized_channels.scope IS '授权范围字符串';
COMMENT ON COLUMN ytb_authorized_channels.is_active IS '授权是否仍然有效';
COMMENT ON COLUMN ytb_authorized_channels.last_token_refresh IS '上次刷新令牌的时间';
COMMENT ON COLUMN ytb_authorized_channels.last_analytics_fetched IS '上次获取分析数据的时间';
COMMENT ON COLUMN ytb_authorized_channels.created_at IS '记录创建时间';
COMMENT ON COLUMN ytb_authorized_channels.updated_at IS '记录更新时间';
COMMENT ON COLUMN ytb_authorized_channels.deleted_at IS '记录软删除时间';

CREATE INDEX idx_ytb_authorized_channels_channel_id ON ytb_authorized_channels(channel_id);
CREATE INDEX idx_ytb_authorized_channels_user_id ON ytb_authorized_channels(user_id);
CREATE INDEX idx_ytb_authorized_channels_is_active ON ytb_authorized_channels(is_active);

-- 频道私密分析数据表 - 存储通过OAuth授权获取的频道分析数据
CREATE TABLE ytb_channel_analytics (
    id                      BIGSERIAL       PRIMARY KEY,
    authorized_channel_id   BIGINT          NOT NULL REFERENCES ytb_authorized_channels(id), -- 关联授权频道ID
    data_date               DATE            NOT NULL,                  -- 数据日期
    granularity             VARCHAR(20)     NOT NULL DEFAULT 'day',    -- 数据粒度(day/month)
    watch_time_minutes      INTEGER         NOT NULL DEFAULT 0,        -- 观看时间(分钟)
    views                   INTEGER         NOT NULL DEFAULT 0,        -- 观看次数
    unique_viewers          INTEGER         NOT NULL DEFAULT 0,        -- 独立观众数
    subscribers_gained      INTEGER         NOT NULL DEFAULT 0,        -- 新增订阅者
    subscribers_lost        INTEGER         NOT NULL DEFAULT 0,        -- 流失订阅者
    estimated_revenue       NUMERIC(12,2),                             -- 估计收入
    monetized_playbacks     INTEGER,                                   -- 获利播放次数
    playback_based_cpm      NUMERIC(10,6),                             -- 基于播放的CPM
    ad_impressions          INTEGER,                                   -- 广告展示次数
    average_view_duration   NUMERIC(10,2),                             -- 平均观看时长
    average_view_percentage NUMERIC(6,2),                              -- 平均观看百分比
    data_source             VARCHAR(50)     NOT NULL DEFAULT 'api',    -- 数据来源
    created_at              TIMESTAMPTZ     NOT NULL DEFAULT NOW(),    -- 记录创建时间
    updated_at              TIMESTAMPTZ     NOT NULL DEFAULT NOW(),    -- 记录更新时间
    deleted_at              TIMESTAMPTZ                                 -- 记录软删除时间
);
COMMENT ON TABLE ytb_channel_analytics IS '存储通过OAuth授权获取的频道分析数据';
COMMENT ON COLUMN ytb_channel_analytics.id IS '主键ID';
COMMENT ON COLUMN ytb_channel_analytics.authorized_channel_id IS '关联的授权频道ID';
COMMENT ON COLUMN ytb_channel_analytics.data_date IS '数据对应的日期';
COMMENT ON COLUMN ytb_channel_analytics.granularity IS '数据粒度(day/month)';
COMMENT ON COLUMN ytb_channel_analytics.watch_time_minutes IS '总观看时间(分钟)';
COMMENT ON COLUMN ytb_channel_analytics.views IS '观看次数';
COMMENT ON COLUMN ytb_channel_analytics.unique_viewers IS '独立观众数量';
COMMENT ON COLUMN ytb_channel_analytics.subscribers_gained IS '新增订阅者数量';
COMMENT ON COLUMN ytb_channel_analytics.subscribers_lost IS '流失订阅者数量';
COMMENT ON COLUMN ytb_channel_analytics.estimated_revenue IS '估计收入(美元)';
COMMENT ON COLUMN ytb_channel_analytics.monetized_playbacks IS '获利播放次数';
COMMENT ON COLUMN ytb_channel_analytics.playback_based_cpm IS '基于播放的千次展示费用';
COMMENT ON COLUMN ytb_channel_analytics.ad_impressions IS '广告展示次数';
COMMENT ON COLUMN ytb_channel_analytics.average_view_duration IS '平均观看时长(秒)';
COMMENT ON COLUMN ytb_channel_analytics.average_view_percentage IS '平均观看百分比';
COMMENT ON COLUMN ytb_channel_analytics.data_source IS '数据来源(api/估算)';
COMMENT ON COLUMN ytb_channel_analytics.created_at IS '记录创建时间';
COMMENT ON COLUMN ytb_channel_analytics.updated_at IS '记录更新时间';
COMMENT ON COLUMN ytb_channel_analytics.deleted_at IS '记录软删除时间';

CREATE UNIQUE INDEX idx_ytb_channel_analytics_unique ON ytb_channel_analytics(authorized_channel_id, data_date, granularity);
CREATE INDEX idx_ytb_channel_analytics_data_date ON ytb_channel_analytics(data_date);

-- 视频私密分析数据表 - 存储通过OAuth授权获取的视频分析数据
CREATE TABLE ytb_video_analytics (
    id                      BIGSERIAL       PRIMARY KEY,
    video_id                BIGINT          NOT NULL REFERENCES ytb_videos(id), -- 关联视频ID
    authorized_channel_id   BIGINT          NOT NULL REFERENCES ytb_authorized_channels(id), -- 关联授权频道ID
    data_date               DATE            NOT NULL,                  -- 数据日期
    granularity             VARCHAR(20)     NOT NULL DEFAULT 'day',    -- 数据粒度(day/lifetime)
    watch_time_minutes      INTEGER         NOT NULL DEFAULT 0,        -- 观看时间(分钟)
    views                   INTEGER         NOT NULL DEFAULT 0,        -- 观看次数
    unique_viewers          INTEGER         NOT NULL DEFAULT 0,        -- 独立观众数
    subscribers_gained      INTEGER         NOT NULL DEFAULT 0,        -- 新增订阅者
    subscribers_lost        INTEGER         NOT NULL DEFAULT 0,        -- 流失订阅者
    estimated_revenue       NUMERIC(12,2),                             -- 估计收入
    monetized_playbacks     INTEGER,                                   -- 获利播放次数
    playback_based_cpm      NUMERIC(10,6),                             -- 基于播放的CPM
    ad_impressions          INTEGER,                                   -- 广告展示次数
    average_view_duration   NUMERIC(10,2),                             -- 平均观看时长
    average_view_percentage NUMERIC(6,2),                              -- 平均观看百分比
    engagement_rate         NUMERIC(6,2),                              -- 互动率
    card_impressions        INTEGER,                                   -- 卡片展示次数
    card_clicks             INTEGER,                                   -- 卡片点击次数
    card_ctr                NUMERIC(6,2),                              -- 卡片点击率
    data_source             VARCHAR(50)     NOT NULL DEFAULT 'api',    -- 数据来源
    created_at              TIMESTAMPTZ     NOT NULL DEFAULT NOW(),    -- 记录创建时间
    updated_at              TIMESTAMPTZ     NOT NULL DEFAULT NOW(),    -- 记录更新时间
    deleted_at              TIMESTAMPTZ                                 -- 记录软删除时间
);
COMMENT ON TABLE ytb_video_analytics IS '存储通过OAuth授权获取的视频分析数据';
COMMENT ON COLUMN ytb_video_analytics.id IS '主键ID';
COMMENT ON COLUMN ytb_video_analytics.video_id IS '关联的视频ID';
COMMENT ON COLUMN ytb_video_analytics.authorized_channel_id IS '关联的授权频道ID';
COMMENT ON COLUMN ytb_video_analytics.data_date IS '数据对应的日期';
COMMENT ON COLUMN ytb_video_analytics.granularity IS '数据粒度(day/lifetime)';
COMMENT ON COLUMN ytb_video_analytics.watch_time_minutes IS '总观看时间(分钟)';
COMMENT ON COLUMN ytb_video_analytics.views IS '观看次数';
COMMENT ON COLUMN ytb_video_analytics.unique_viewers IS '独立观众数量';
COMMENT ON COLUMN ytb_video_analytics.subscribers_gained IS '新增订阅者数量';
COMMENT ON COLUMN ytb_video_analytics.subscribers_lost IS '流失订阅者数量';
COMMENT ON COLUMN ytb_video_analytics.estimated_revenue IS '估计收入(美元)';
COMMENT ON COLUMN ytb_video_analytics.monetized_playbacks IS '获利播放次数';
COMMENT ON COLUMN ytb_video_analytics.playback_based_cpm IS '基于播放的千次展示费用';
COMMENT ON COLUMN ytb_video_analytics.ad_impressions IS '广告展示次数';
COMMENT ON COLUMN ytb_video_analytics.average_view_duration IS '平均观看时长(秒)';
COMMENT ON COLUMN ytb_video_analytics.average_view_percentage IS '平均观看百分比';
COMMENT ON COLUMN ytb_video_analytics.engagement_rate IS '用户互动率(%)';
COMMENT ON COLUMN ytb_video_analytics.card_impressions IS '卡片展示次数';
COMMENT ON COLUMN ytb_video_analytics.card_clicks IS '卡片点击次数';
COMMENT ON COLUMN ytb_video_analytics.card_ctr IS '卡片点击率(%)';
COMMENT ON COLUMN ytb_video_analytics.data_source IS '数据来源(api/估算)';
COMMENT ON COLUMN ytb_video_analytics.created_at IS '记录创建时间';
COMMENT ON COLUMN ytb_video_analytics.updated_at IS '记录更新时间';
COMMENT ON COLUMN ytb_video_analytics.deleted_at IS '记录软删除时间';

CREATE UNIQUE INDEX idx_ytb_video_analytics_unique ON ytb_video_analytics(video_id, authorized_channel_id, data_date, granularity);
CREATE INDEX idx_ytb_video_analytics_data_date ON ytb_video_analytics(data_date);

-- 观众地理数据表 - 存储观众地理分布信息
CREATE TABLE ytb_audience_geography (
    id                      BIGSERIAL       PRIMARY KEY,
    authorized_channel_id   BIGINT          NOT NULL REFERENCES ytb_authorized_channels(id), -- 关联授权频道ID
    video_id                BIGINT          REFERENCES ytb_videos(id),  -- 关联视频ID(NULL表示频道级别)
    data_date               DATE            NOT NULL,                  -- 数据日期
    country_code            VARCHAR(2)      NOT NULL,                  -- 国家/地区代码
    views                   INTEGER         NOT NULL DEFAULT 0,        -- 观看次数
    watch_time_minutes      INTEGER         NOT NULL DEFAULT 0,        -- 观看时间(分钟)
    average_view_duration   NUMERIC(10,2),                             -- 平均观看时长
    average_view_percentage NUMERIC(6,2),                              -- 平均观看百分比
    created_at              TIMESTAMPTZ     NOT NULL DEFAULT NOW(),
    updated_at              TIMESTAMPTZ     NOT NULL DEFAULT NOW(),
    deleted_at              TIMESTAMPTZ
);
COMMENT ON TABLE ytb_audience_geography IS '存储观众地理分布信息';
COMMENT ON COLUMN ytb_audience_geography.id IS '主键ID';
COMMENT ON COLUMN ytb_audience_geography.authorized_channel_id IS '关联的授权频道ID';
COMMENT ON COLUMN ytb_audience_geography.video_id IS '关联的视频ID，NULL表示频道级别';
COMMENT ON COLUMN ytb_audience_geography.data_date IS '数据对应的日期';
COMMENT ON COLUMN ytb_audience_geography.country_code IS '国家/地区代码';
COMMENT ON COLUMN ytb_audience_geography.views IS '观看次数';
COMMENT ON COLUMN ytb_audience_geography.watch_time_minutes IS '观看时间(分钟)';
COMMENT ON COLUMN ytb_audience_geography.average_view_duration IS '平均观看时长(秒)';
COMMENT ON COLUMN ytb_audience_geography.average_view_percentage IS '平均观看百分比';
COMMENT ON COLUMN ytb_audience_geography.created_at IS '记录创建时间';
COMMENT ON COLUMN ytb_audience_geography.updated_at IS '记录更新时间';
COMMENT ON COLUMN ytb_audience_geography.deleted_at IS '记录软删除时间';

CREATE INDEX idx_ytb_audience_geography_authorized_channel_id ON ytb_audience_geography(authorized_channel_id);
CREATE INDEX idx_ytb_audience_geography_video_id ON ytb_audience_geography(video_id);
CREATE INDEX idx_ytb_audience_geography_data_date ON ytb_audience_geography(data_date);

-- 创建更新 updated_at 的触发器函数（如果不存在）
CREATE OR REPLACE FUNCTION update_modified_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- 为所有表添加 updated_at 自动更新触发器
DO $$
DECLARE
    t text;
BEGIN
    FOR t IN 
        SELECT table_name FROM information_schema.tables 
        WHERE table_schema = 'public' AND table_name LIKE 'ytb_%'
    LOOP
        EXECUTE format('
            DROP TRIGGER IF EXISTS update_trigger ON %I;
            CREATE TRIGGER update_trigger
            BEFORE UPDATE ON %I
            FOR EACH ROW
            EXECUTE FUNCTION update_modified_column();
        ', t, t);
    END LOOP;
END;
$$;
