# ReYoutube Web

<div align="center">
  <h3>🚀 先进的 YouTube 频道分析平台</h3>
  <p>基于 Next.js 15 和现代 Web 技术构建的综合性 YouTube 频道和视频数据分析 Web 应用程序。</p>
  
  [![Next.js](https://img.shields.io/badge/Next.js-15.3.0-black?style=flat-square&logo=next.js)](https://nextjs.org/)
  [![TypeScript](https://img.shields.io/badge/TypeScript-5.0-blue?style=flat-square&logo=typescript)](https://www.typescriptlang.org/)
  [![Prisma](https://img.shields.io/badge/Prisma-6.1.0-2D3748?style=flat-square&logo=prisma)](https://www.prisma.io/)
  [![PostgreSQL](https://img.shields.io/badge/PostgreSQL-13+-336791?style=flat-square&logo=postgresql)](https://www.postgresql.org/)
  [![Tailwind CSS](https://img.shields.io/badge/Tailwind_CSS-3.4.1-38B2AC?style=flat-square&logo=tailwind-css)](https://tailwindcss.com/)
</div>

## 📋 目录

- [项目概述](#项目概述)
- [功能特性](#功能特性)
- [技术栈](#技术栈)
- [环境要求](#环境要求)
- [安装指南](#安装指南)
- [配置说明](#配置说明)
- [使用方法](#使用方法)
- [API 文档](#api-文档)
- [数据库架构](#数据库架构)
- [开发指南](#开发指南)
- [测试](#测试)
- [部署](#部署)
- [贡献指南](#贡献指南)
- [许可证](#许可证)

## 🎯 项目概述

ReYoutube Web 是一个专为 YouTube 内容创作者、营销人员和分析师设计的强大分析平台。它提供全面的频道表现洞察、视频分析、受众行为分析和收入跟踪，具备实时数据监控和预测分析功能。

### 核心能力

- **实时分析**：实时监控频道指标、视频表现和受众参与度
- **预测洞察**：AI 驱动的趋势预测和表现预测
- **多语言支持**：完整的国际化支持，包含中英文语言
- **收入跟踪**：全面的收入分析和增长预测
- **受众分析**：深入了解受众人口统计和行为模式
- **竞争分析**：频道对比和表现基准测试

## ✨ 功能特性

### 📊 分析与洞察
- **频道概览**：包含关键绩效指标的综合仪表板
- **视频分析**：详细的视频表现指标和趋势
- **受众人口统计**：年龄、性别、地理位置和兴趣分析
- **收入分析**：货币化跟踪和收入优化洞察
- **增长预测**：AI 驱动的未来表现预测

### 🔍 搜索与发现
- **智能频道搜索**：带有自动完成和过滤功能的高级搜索
- **@用户名访问**：通过 `/@username` URL 直接访问频道
- **外部数据集成**：缺失数据的外部 API 自动回退
- **实时数据同步**：与 YouTube API 持续同步

### 👤 用户管理
- **身份验证**：Google OAuth 和传统邮箱/密码安全登录
- **用户档案**：个性化仪表板和偏好设置
- **订阅管理**：集成 Stripe 的支付处理
- **多租户支持**：家庭和组织管理功能

### 🌐 平台功能
- **响应式设计**：针对桌面、平板和移动设备优化
- **深色/浅色主题**：用户偏好主题切换
- **SEO 优化**：自动站点地图生成和元数据优化
- **性能优化**：高级缓存和包优化

## 🛠 技术栈

### 前端
- **框架**：Next.js 15.3.0 with App Router
- **语言**：TypeScript 5.0
- **样式**：Tailwind CSS 3.4.1 + Tailwind Animate
- **UI 组件**：Radix UI + 自定义组件库
- **图标**：Lucide React + Heroicons
- **图表**：Recharts 数据可视化
- **动画**：Framer Motion

### 后端
- **运行时**：Node.js with Next.js API Routes
- **数据库**：PostgreSQL 13+ with Prisma ORM 6.1.0
- **身份验证**：NextAuth.js with Google OAuth
- **支付**：Stripe 订阅集成
- **国际化**：next-intl 多语言支持

### 开发与运维
- **包管理器**：pnpm 10.10.0
- **代码检查**：ESLint with Next.js configuration
- **测试**：Jest with ts-jest 单元测试
- **数据库测试**：Docker Compose 测试环境
- **包分析**：@next/bundle-analyzer
- **部署**：Vercel 优化的独立输出

## 📋 环境要求

开始之前，请确保已安装以下软件：

- **Node.js**：版本 18.0 或更高
- **pnpm**：版本 8.0 或更高（推荐的包管理器）
- **PostgreSQL**：版本 13 或更高
- **Git**：用于版本控制

### 可选
- **Docker**：用于运行测试数据库
- **Vercel CLI**：用于部署

## 🚀 安装指南

### 1. 克隆仓库

```bash
git clone https://github.com/wenhaofree/reyoutube-web.git
cd reyoutube-web
```

### 2. 安装依赖

```bash
pnpm install
```

### 3. 环境设置

创建环境文件：

```bash
cp .env.example .env.local
cp .env.test.example .env.test
```

## ⚙️ 配置说明

### 数据库配置

在 `.env.local` 中配置 PostgreSQL 连接：

```env
# 数据库配置
DATABASE_URL='postgresql://user:password@host:port/dbname?connection_limit=20&pool_timeout=30&statement_timeout=60000&idle_in_transaction_session_timeout=60000'
```

**连接参数说明：**
- `connection_limit`：连接池最大大小（推荐：20）
- `pool_timeout`：连接获取超时时间（秒）（推荐：30）
- `statement_timeout`：SQL 语句执行超时时间（毫秒）（推荐：60000）
- `idle_in_transaction_session_timeout`：事务空闲超时时间（毫秒）（推荐：60000）

### 身份验证配置

```env
# NextAuth 配置
NEXTAUTH_SECRET=your-secret-key-here
NEXTAUTH_URL=http://localhost:3000

# Google OAuth
GOOGLE_CLIENT_ID=your-google-client-id
GOOGLE_CLIENT_SECRET=your-google-client-secret
```

### 支付配置

```env
# Stripe 配置
STRIPE_SECRET_KEY=sk_test_your-stripe-secret-key
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=pk_test_your-stripe-publishable-key
STRIPE_WEBHOOK_SECRET=whsec_your-webhook-secret
```

### 应用程序配置

```env
# 应用设置
NEXT_PUBLIC_WEB_URL=http://localhost:3000
NEXT_PUBLIC_API_URL=http://localhost:3000/api

# 外部 API 配置
YOUTUBE_API_KEY=your-youtube-api-key
```

### 数据库设置

1. **初始化数据库架构**：
```bash
pnpm run db:push
```

2. **生成 Prisma 客户端**：
```bash
pnpm run db:generate
```

3. **同步数据库结构**：
```bash
pnpm run db:sync
```

## 🎮 使用方法

### 开发服务器

启动开发服务器：

```bash
pnpm dev
```

应用程序将在 [http://localhost:3000](http://localhost:3000) 可用。

### 数据库管理

- **同步数据库结构**：`pnpm run db:sync`
- **启动 Prisma Studio**：`pnpm run db:studio`
- **测试数据库连接**：`pnpm run test:db`
- **数据库健康检查**：`pnpm run db:health`

### 构建和生产

```bash
# 生产构建
pnpm build

# 启动生产服务器
pnpm start

# 分析包大小
pnpm run analyze
```

### Docker 开发

```bash
# 启动测试数据库
pnpm run docker:up

# 停止测试数据库
pnpm run docker:down

# 使用 Docker 运行测试
pnpm run test:db:docker
```

## 📚 API 文档

### 频道访问端点

#### @用户名访问功能

平台实现了强大的 @用户名访问功能，用于简化频道 URL：

**端点**：`GET /@username`

**功能特性**：
- 通过 `/@username` URL 直接访问频道
- 自动搜索数据库中对应的频道
- 缺失数据的外部 API 回退
- 智能错误处理和用户体验

**实现细节**：

1. **中间件处理**：
   - 捕获 `/@username` 格式的 URL
   - 将请求重写到 API 路由处理器

2. **查询优化**：
   - 支持 @username 和 username 两种格式
   - 外部 API 作为数据源备份

3. **错误处理**：
   - 全面的错误日志记录
   - 用户友好的错误页面
   - 自动重定向逻辑

**使用示例**：
```bash
# 直接频道访问
curl https://reyoutube.com/@channelname

# 系统行为：
# 1. 在数据库中搜索频道
# 2. 如果未找到，查询外部 API
# 3. 根据结果重定向到相应页面
```

### 核心 API 端点

#### 频道分析
```bash
# 获取频道信息
GET /api/channels/[channelId]

# 获取频道统计
GET /api/channels/[channelId]/stats?timeRange=30d

# 获取频道视频
GET /api/channels/[channelId]/videos?page=1&limit=20
```

#### 搜索与发现
```bash
# 搜索频道
GET /api/search/channels?q=searchterm&limit=10

# 处理用户名查找
GET /api/channels/handle/[username]
```

#### 用户管理
```bash
# 用户身份验证
POST /api/auth/signin
POST /api/auth/signout

# 用户档案
GET /api/user/profile
PUT /api/user/profile
```

## 🗄️ 数据库架构

### 核心表

#### YouTube 数据模型
- **ytb_channels**：频道信息和元数据
- **ytb_videos**：视频详情和统计
- **ytb_channel_stats_snapshots**：历史频道表现数据
- **ytb_video_stats_snapshots**：历史视频表现数据
- **ytb_channel_time_statistics**：基于时间的分析和增长指标

#### 用户与业务模型
- **users**：用户账户和身份验证
- **orders**：订阅和支付记录
- **system_config**：应用程序配置
- **system_log**：审计跟踪和日志记录

#### 家庭管理（扩展功能）
- **family**：家庭/组织管理
- **family_member**：成员档案和关系
- **family_event**：事件和里程碑
- **family_education**：教育背景
- **family_work**：职业历史

### 关键索引和优化

```sql
-- 频道表现优化
CREATE INDEX idx_ytb_videos_channel_published
ON ytb_videos(channel_id, published_at DESC)
WHERE deleted_at IS NULL;

-- 搜索优化
CREATE INDEX idx_ytb_channels_custom_url_trgm
ON ytb_channels USING gin(custom_url gin_trgm_ops);
```

## 🧪 测试

### 单元测试

运行测试套件：

```bash
# 运行所有测试
pnpm test:db

# 使用 Docker 运行测试
pnpm run test:db:docker

# 调试测试
pnpm run test:debug
```

### 数据库测试

```bash
# 设置测试数据库
pnpm run db:test:setup

# 初始化测试数据
pnpm run db:test:init

# 启动测试数据库 studio
pnpm run db:test:studio
```

### 测试配置

项目使用 Jest 和 ts-jest 进行 TypeScript 支持。测试配置位于 `src/tests/jest.config.js`。

## 🚀 部署

### Vercel 部署（推荐）

1. **连接仓库**：将 GitHub 仓库链接到 Vercel
2. **环境变量**：在 Vercel 仪表板中配置所有必需的环境变量
3. **数据库**：确保 PostgreSQL 数据库可从 Vercel 访问
4. **部署**：推送到主分支时自动部署

### 手动部署

```bash
# 构建应用程序
pnpm build

# 启动生产服务器
pnpm start
```

### Docker 部署

```dockerfile
# 示例 Dockerfile 结构
FROM node:18-alpine
WORKDIR /app
COPY package*.json ./
RUN pnpm install --frozen-lockfile
COPY . .
RUN pnpm build
EXPOSE 3000
CMD ["pnpm", "start"]
```

## 🔧 开发指南

### 项目结构

```
reyoutube-web/
├── src/
│   ├── app/                 # Next.js App Router
│   │   ├── [locale]/       # 国际化路由
│   │   ├── api/            # API 路由
│   │   └── globals.css     # 全局样式
│   ├── components/         # React 组件
│   │   ├── ui/            # UI 组件
│   │   ├── channel/       # 频道特定组件
│   │   └── charts/        # 图表组件
│   ├── lib/               # 工具库
│   ├── hooks/             # 自定义 React hooks
│   ├── types/             # TypeScript 类型定义
│   └── middleware.ts      # Next.js 中间件
├── prisma/                # 数据库架构和迁移
├── messages/              # 国际化文件
├── public/                # 静态资源
└── scripts/               # 构建和工具脚本
```

### 代码风格和标准

- **TypeScript**：启用严格模式
- **ESLint**：Next.js 配置和自定义规则
- **Prettier**：代码格式化（在 package.json 中配置）
- **约定式提交**：提交消息标准

### 性能优化

- **包分析**：使用 `pnpm run analyze` 分析包大小
- **图像优化**：Next.js Image 组件支持多种格式
- **缓存**：API 响应的全面缓存策略
- **数据库索引**：针对常见查询的优化索引

## 🔍 SEO 与站点地图

项目使用 `next-sitemap` 进行自动站点地图和 robots.txt 生成，以改善搜索引擎索引。

### 功能特性

- **自动站点地图生成**：使用 `next-sitemap` 完整生成站点地图
- **动态路由支持**：包含静态和动态路由
- **多语言配置**：自动包含国际化路由
- **优先级和频率设置**：可配置的优先级和更新频率
- **Robots.txt 生成**：自动生成指向站点地图的 robots.txt 文件

### 配置

1. **在 `.env` 中设置基础 URL**：
   ```env
   NEXT_PUBLIC_WEB_URL=https://www.reyoutube.com
   ```

2. **自定义站点地图**：
   - 编辑 `next-sitemap.config.js` 添加或修改路由
   - 使用 `additionalPaths` 配置动态路由

3. **构建和生成**：
   ```bash
   # 构建项目（包含站点地图生成的 postbuild 钩子）
   pnpm run build

   # 或单独生成站点地图
   pnpm run sitemap
   ```

### 访问站点地图

构建和部署后：
- **站点地图索引**：`https://www.reyoutube.com/sitemap.xml`
- **详细站点地图**：`https://www.reyoutube.com/sitemap-0.xml`
- **Robots 文件**：`https://www.reyoutube.com/robots.txt`

## 🤝 贡献指南

我们欢迎对 ReYoutube Web 的贡献！请遵循以下指南：

### 开始贡献

1. **Fork 仓库**
2. **创建功能分支**：`git checkout -b feature/amazing-feature`
3. **进行更改**：遵循我们的编码标准
4. **测试更改**：确保所有测试通过
5. **提交更改**：使用约定式提交消息
6. **推送到分支**：`git push origin feature/amazing-feature`
7. **打开 Pull Request**：提供详细描述

### 开发指南

- **代码风格**：遵循 ESLint 和 Prettier 配置
- **测试**：为新功能添加测试
- **文档**：为重大更改更新文档
- **性能**：考虑性能影响
- **可访问性**：确保满足可访问性标准

### 提交消息格式

```
type(scope): description

[optional body]

[optional footer]
```

**类型**：feat, fix, docs, style, refactor, test, chore

### Pull Request 流程

1. 如适用，更新 README.md 中的更改详情
2. 遵循 SemVer 更新版本号
3. 确保所有测试通过且无 linting 错误
4. 请求维护者审查

## 📄 许可证

本项目采用 MIT 许可证 - 详情请参阅 [LICENSE](LICENSE) 文件。

## 🙏 致谢

- **Next.js 团队**：提供出色的 React 框架
- **Vercel**：提供托管和部署平台
- **Prisma**：提供优秀的数据库工具包
- **Radix UI**：提供可访问的 UI 组件
- **Tailwind CSS**：提供实用优先的 CSS 框架

## 📞 支持

- **文档**：查看此 README 和内联代码注释
- **问题**：通过 GitHub Issues 报告错误和请求功能
- **讨论**：在 GitHub Discussions 中加入社区讨论
- **邮箱**：联系维护者 [<EMAIL>](mailto:<EMAIL>)

## 🗺️ 路线图

### 第一阶段（当前）
- ✅ 核心分析仪表板
- ✅ 频道搜索和发现
- ✅ 用户身份验证
- ✅ 多语言支持
- 🔄 移动响应式优化
- 🔄 性能改进

### 第二阶段（即将到来）
- 📋 高级过滤和排序
- 📋 导出功能
- 📋 API 速率限制
- 📋 高级用户角色
- 📋 Webhook 集成

### 第三阶段（未来）
- 📋 机器学习洞察
- 📋 自定义仪表板构建器
- 📋 第三方集成
- 📋 白标解决方案

---

<div align="center">
  <p>由 ReYoutube 团队用 ❤️ 制作</p>
  <p>
    <a href="https://github.com/wenhaofree/reyoutube-web">GitHub</a> •
    <a href="https://reyoutube.com">网站</a> •
    <a href="mailto:<EMAIL>">联系我们</a>
  </p>
</div>
