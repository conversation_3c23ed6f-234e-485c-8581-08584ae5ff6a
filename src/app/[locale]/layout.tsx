import { type Metadata } from 'next';
import {NextIntlClientProvider} from 'next-intl';
import {getMessages} from '@/i18n/routing';
import {notFound} from 'next/navigation';
import {routing} from '@/i18n/routing';
import '../globals.css';
import Header from '@/components/Header';
// import Footer from '@/components/Footer';
import { getLandingPage } from '@/app/actions';
import { Providers } from '@/app/providers';

// import { SplashCursor } from "@/components/ui/splash-cursor"
import Script from 'next/script';

// Stagewise Toolbar (development only)
// import { StagewiseToolbarWrapper } from '@/components/StagewiseToolbarWrapper';

// 元数据配置
export const metadata: Metadata = {
  title: 'Reyoutube - YouTube Channel Statistics & Analytics', // 添加了一个标题
  description: 'Reyoutube gives you the power of in depth YouTube Channel Statistics. Search any creator, compare performance, see growth trajectories, and much more!',
  keywords: ['youtube', 'channel', 'statistics', 'analytics', 'reyoutube', 'revenue', 'projections', 'videos', 'reyoutube.com'],
  metadataBase: new URL('https://www.reyoutube.com'), // 推荐设置基础 URL
  alternates: {
    canonical: '/', // 根据需要调整规范 URL
    languages: { // 如果支持多语言，可以列出
      'en': '/en',
      'zh': '/zh',
      // 添加其他支持的语言
    },
  },
  openGraph: { // 添加 Open Graph 元数据以优化社交媒体分享
    title: 'Reyoutube - YouTube Channel Statistics & Analytics',
    description: 'In-depth YouTube channel statistics and analytics.',
    url: 'https://www.reyoutube.com',
    siteName: 'Reyoutube',
    // images: [ // 可以添加预览图像
    //   {
    //     url: 'https://www.reyoutube.com/og-image.png', // 替换为您的图像 URL
    //     width: 1200,
    //     height: 630,
    //   },
    // ],
    locale: 'en_US', // 默认语言环境
    type: 'website',
  },
  twitter: { // 添加 Twitter Card 元数据
    card: 'summary_large_image',
    title: 'Reyoutube - YouTube Channel Statistics & Analytics',
    description: 'In-depth YouTube channel statistics and analytics.',
    // siteId: '你的Twitter网站ID',
    // creator: '@你的Twitter用户名',
    // creatorId: '你的Twitter用户ID',
    // images: ['https://www.reyoutube.com/twitter-image.png'], // 替换为您的图像 URL
  },
  // 可以根据需要添加更多元数据，例如 icons, manifest 等
};

// 修改类型定义，使用 generateStaticParams 来处理参数
export async function generateStaticParams() {
  return routing.locales.map((locale) => ({locale}));
}

// 更新 Layout 组件以支持异步 params
export default async function Layout({
  children,
  params,
}: {
  children: React.ReactNode;
  params: Promise<{ locale: string }>;
}) {
  // 使用 await 获取 locale
  const { locale } = await params;
  
  // 验证 locale
  if (!routing.locales.includes(locale as any)) {
    notFound();
  }
 
  // 并行获取消息和页面数据
  const [messages, page] = await Promise.all([
    getMessages(locale),
    getLandingPage(locale)
  ]);

  return (
    <html lang={locale} className="scroll-smooth" suppressHydrationWarning>
      <body>
          <Script
            src="https://umami.wenhaofree.com/script.js"
            data-website-id="834d2553-b9d9-4dc0-bf53-126cdce2c415"
            strategy="lazyOnload"
          />
          {/* <SplashCursor /> */}
          {/* Stagewise Toolbar: Only enabled in development mode */}
          {/* <StagewiseToolbarWrapper /> */}
          <NextIntlClientProvider messages={messages} locale={locale}>
          <Providers>
            <div className="fixed inset-x-0 top-0 z-50 h-16 border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
                <div className="h-full">
                  {page.header && <Header header={page.header} />}
                </div>
            </div>
            <main className="flex-1 pt-16">
                {/* 移除 max-w-screen-xl 类以允许内容完全扩展 */}
                <div className="mx-auto px-4 sm:px-6 lg:px-8">
                  {children}
                </div>
              </main>
            {/* <div className="border-t">
              {page.footer && <Footer footer={page.footer} />}
            </div> */}
            </Providers>
          </NextIntlClientProvider>

      </body>
    </html>
  );
}