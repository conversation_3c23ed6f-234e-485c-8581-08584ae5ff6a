"use client"

import { useState } from "react"
import Link from "next/link"
import { <PERSON>, <PERSON>Content, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { 
  TrendingUp, 
  Users, 
  Eye, 
  DollarSign, 
  Calendar,
  ArrowRight,
  BarChart3,
  Target,
  Award,
  Zap
} from "lucide-react"

interface CaseStudy {
  id: string
  title: string
  channelName: string
  niche: string
  challenge: string
  solution: string
  results: {
    subscriberGrowth: string
    viewIncrease: string
    revenueGrowth: string
    timeframe: string
  }
  keyMetrics: {
    beforeSubscribers: string
    afterSubscribers: string
    beforeViews: string
    afterViews: string
    beforeRevenue: string
    afterRevenue: string
  }
  strategies: string[]
  testimonial: {
    quote: string
    author: string
    role: string
  }
  image: string
  slug: string
  featured: boolean
}

const caseStudies: CaseStudy[] = [
  {
    id: "1",
    title: "From 10K to 1M Subscribers: Tech Review Channel's Growth Journey",
    channelName: "TechReview Pro",
    niche: "Technology Reviews",
    challenge: "Struggling to break through the 10K subscriber plateau despite consistent uploads and good content quality. Low engagement rates and difficulty competing with established tech channels.",
    solution: "Implemented a comprehensive analytics-driven strategy focusing on audience retention optimization, strategic SEO, and community building. Redesigned content format based on audience retention data.",
    results: {
      subscriberGrowth: "9,900%",
      viewIncrease: "1,200%", 
      revenueGrowth: "2,500%",
      timeframe: "18 months"
    },
    keyMetrics: {
      beforeSubscribers: "10,000",
      afterSubscribers: "1,000,000",
      beforeViews: "50,000/month",
      afterViews: "650,000/month",
      beforeRevenue: "$500/month",
      afterRevenue: "$13,000/month"
    },
    strategies: [
      "Audience retention analysis and content restructuring",
      "Strategic keyword targeting for tech reviews",
      "Community engagement and feedback integration",
      "Thumbnail and title A/B testing",
      "Collaboration with other tech channels",
      "Consistent upload schedule optimization"
    ],
    testimonial: {
      quote: "The analytics insights from ReYoutube completely transformed our approach. We went from guessing what worked to making data-driven decisions that consistently delivered results.",
      author: "Sarah Chen",
      role: "Creator, TechReview Pro"
    },
    image: "/images/case-studies/tech-review-pro.jpg",
    slug: "tech-review-pro-growth-journey",
    featured: true
  },
  {
    id: "2", 
    title: "Cooking Channel Doubles Revenue Through Strategic Content Optimization",
    channelName: "Home Chef Masters",
    niche: "Cooking & Food",
    challenge: "High subscriber count but low monetization. Viewers weren't engaging with sponsored content, and ad revenue was below industry standards for the channel size.",
    solution: "Analyzed audience behavior patterns to optimize content for higher engagement and better monetization. Implemented strategic product placement and improved call-to-action positioning.",
    results: {
      subscriberGrowth: "45%",
      viewIncrease: "78%",
      revenueGrowth: "156%", 
      timeframe: "8 months"
    },
    keyMetrics: {
      beforeSubscribers: "450,000",
      afterSubscribers: "652,500",
      beforeViews: "2.1M/month",
      afterViews: "3.7M/month", 
      beforeRevenue: "$8,500/month",
      afterRevenue: "$21,760/month"
    },
    strategies: [
      "Revenue optimization through better ad placement",
      "Sponsored content integration improvement",
      "Audience engagement strategy refinement",
      "Content format diversification",
      "Email list building from YouTube traffic",
      "Merchandise integration optimization"
    ],
    testimonial: {
      quote: "Understanding our audience's viewing patterns helped us create content that not only engaged viewers but also converted them into customers. Our revenue more than doubled!",
      author: "Marcus Rodriguez",
      role: "Creator, Home Chef Masters"
    },
    image: "/images/case-studies/home-chef-masters.jpg",
    slug: "home-chef-masters-revenue-optimization",
    featured: true
  }
]

export default function CaseStudiesPage() {
  const [selectedNiche, setSelectedNiche] = useState("all")
  
  const niches = ["all", "technology", "cooking", "education", "gaming", "lifestyle", "business"]
  
  const filteredCaseStudies = selectedNiche === "all" 
    ? caseStudies 
    : caseStudies.filter(study => study.niche.toLowerCase().includes(selectedNiche))

  return (
    <div className="min-h-screen bg-background">
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="text-center mb-12">
          <Badge variant="outline" className="mb-4">Success Stories</Badge>
          <h1 className="text-4xl md:text-5xl font-bold mb-6">
            YouTube Analytics Case Studies
          </h1>
          <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
            Real success stories from YouTube creators who transformed their channels using 
            data-driven strategies and analytics insights. Learn from their journeys and 
            apply proven techniques to grow your own channel.
          </p>
        </div>

        {/* Stats Overview */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-6 mb-12">
          <Card>
            <CardContent className="p-6 text-center">
              <Users className="h-8 w-8 mx-auto mb-3 text-blue-500" />
              <div className="text-2xl font-bold mb-1">50M+</div>
              <div className="text-sm text-muted-foreground">Total Subscribers Gained</div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-6 text-center">
              <Eye className="h-8 w-8 mx-auto mb-3 text-green-500" />
              <div className="text-2xl font-bold mb-1">2.5B+</div>
              <div className="text-sm text-muted-foreground">Additional Views Generated</div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-6 text-center">
              <DollarSign className="h-8 w-8 mx-auto mb-3 text-yellow-500" />
              <div className="text-2xl font-bold mb-1">$12M+</div>
              <div className="text-sm text-muted-foreground">Revenue Increase</div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-6 text-center">
              <Award className="h-8 w-8 mx-auto mb-3 text-purple-500" />
              <div className="text-2xl font-bold mb-1">500+</div>
              <div className="text-sm text-muted-foreground">Successful Channels</div>
            </CardContent>
          </Card>
        </div>

        {/* Filter */}
        <div className="flex flex-wrap gap-2 justify-center mb-12">
          {niches.map((niche) => (
            <Button
              key={niche}
              variant={selectedNiche === niche ? "default" : "outline"}
              size="sm"
              onClick={() => setSelectedNiche(niche)}
              className="capitalize"
            >
              {niche === "all" ? "All Niches" : niche}
            </Button>
          ))}
        </div>

        {/* Featured Case Studies */}
        <div className="space-y-12">
          {filteredCaseStudies.map((study) => (
            <Card key={study.id} className="overflow-hidden">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                {/* Content */}
                <div className="p-8">
                  <div className="flex items-center gap-2 mb-4">
                    <Badge variant="secondary">{study.niche}</Badge>
                    {study.featured && <Badge variant="default">Featured</Badge>}
                  </div>
                  
                  <h2 className="text-2xl font-bold mb-4">{study.title}</h2>
                  <p className="text-lg font-semibold text-primary mb-2">{study.channelName}</p>
                  
                  <div className="space-y-4 mb-6">
                    <div>
                      <h3 className="font-semibold mb-2 flex items-center gap-2">
                        <Target className="h-4 w-4" />
                        Challenge
                      </h3>
                      <p className="text-muted-foreground">{study.challenge}</p>
                    </div>
                    
                    <div>
                      <h3 className="font-semibold mb-2 flex items-center gap-2">
                        <Zap className="h-4 w-4" />
                        Solution
                      </h3>
                      <p className="text-muted-foreground">{study.solution}</p>
                    </div>
                  </div>

                  <Button asChild>
                    <Link href={`/case-studies/${study.slug}`}>
                      Read Full Case Study
                      <ArrowRight className="ml-2 h-4 w-4" />
                    </Link>
                  </Button>
                </div>

                {/* Metrics */}
                <div className="p-8 bg-muted/30">
                  <h3 className="text-lg font-semibold mb-6 flex items-center gap-2">
                    <BarChart3 className="h-5 w-5" />
                    Key Results
                  </h3>
                  
                  <div className="grid grid-cols-2 gap-4 mb-6">
                    <div className="text-center p-4 bg-background rounded-lg">
                      <div className="text-2xl font-bold text-green-600 mb-1">
                        +{study.results.subscriberGrowth}
                      </div>
                      <div className="text-sm text-muted-foreground">Subscriber Growth</div>
                    </div>
                    <div className="text-center p-4 bg-background rounded-lg">
                      <div className="text-2xl font-bold text-blue-600 mb-1">
                        +{study.results.viewIncrease}
                      </div>
                      <div className="text-sm text-muted-foreground">View Increase</div>
                    </div>
                    <div className="text-center p-4 bg-background rounded-lg">
                      <div className="text-2xl font-bold text-yellow-600 mb-1">
                        +{study.results.revenueGrowth}
                      </div>
                      <div className="text-sm text-muted-foreground">Revenue Growth</div>
                    </div>
                    <div className="text-center p-4 bg-background rounded-lg">
                      <div className="text-2xl font-bold text-purple-600 mb-1">
                        {study.results.timeframe}
                      </div>
                      <div className="text-sm text-muted-foreground">Timeframe</div>
                    </div>
                  </div>

                  <div className="bg-background p-4 rounded-lg">
                    <blockquote className="text-sm italic mb-3">
                      "{study.testimonial.quote}"
                    </blockquote>
                    <div className="text-sm">
                      <div className="font-semibold">{study.testimonial.author}</div>
                      <div className="text-muted-foreground">{study.testimonial.role}</div>
                    </div>
                  </div>
                </div>
              </div>
            </Card>
          ))}
        </div>

        {/* CTA Section */}
        <div className="mt-16 text-center">
          <Card className="bg-gradient-to-r from-primary/10 to-primary/5 border-primary/20">
            <CardContent className="p-8">
              <h2 className="text-2xl font-bold mb-4">Ready to Write Your Success Story?</h2>
              <p className="text-muted-foreground mb-6 max-w-2xl mx-auto">
                Join thousands of creators who have transformed their channels using our 
                analytics platform. Start your journey to YouTube success today.
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Button size="lg" asChild>
                  <Link href="/signup">Start Free Analysis</Link>
                </Button>
                <Button variant="outline" size="lg" asChild>
                  <Link href="/demo">Watch Demo</Link>
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}
