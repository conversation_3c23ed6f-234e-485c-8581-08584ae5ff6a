"use client"

import { useState, useEffect } from "react"
import { use<PERSON>ara<PERSON> } from "next/navigation"
import Link from "next/link"
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { 
  ArrowLeft,
  TrendingUp, 
  Users, 
  Eye, 
  DollarSign,
  Calendar,
  Target,
  Zap,
  CheckCircle,
  BarChart3,
  Award
} from "lucide-react"

// Detailed case study data
const caseStudyDetails = {
  "tech-review-pro-growth-journey": {
    id: "1",
    title: "From 10K to 1M Subscribers: Tech Review Channel's Growth Journey",
    channelName: "TechReview Pro",
    niche: "Technology Reviews",
    publishDate: "2024-01-20",
    readTime: "15 min read",
    
    overview: {
      challenge: "TechReview Pro was stuck at 10,000 subscribers despite consistent uploads and good content quality. The channel faced low engagement rates, poor discoverability, and difficulty competing with established tech channels in a saturated market.",
      solution: "We implemented a comprehensive analytics-driven strategy focusing on audience retention optimization, strategic SEO, community building, and data-driven content decisions.",
      timeframe: "18 months",
      investment: "Analytics tools and optimization time"
    },

    beforeAfter: {
      subscribers: { before: "10,000", after: "1,000,000", growth: "9,900%" },
      monthlyViews: { before: "50,000", after: "650,000", growth: "1,200%" },
      monthlyRevenue: { before: "$500", after: "$13,000", growth: "2,500%" },
      avgViewDuration: { before: "3:45", after: "8:20", growth: "122%" },
      engagementRate: { before: "2.1%", after: "7.8%", growth: "271%" },
      ctr: { before: "3.2%", after: "9.1%", growth: "184%" }
    },

    strategies: [
      {
        title: "Audience Retention Analysis & Content Restructuring",
        description: "Used detailed retention graphs to identify drop-off points and restructured content format",
        impact: "Increased average view duration by 122%",
        details: [
          "Analyzed retention graphs for 50+ videos to identify patterns",
          "Restructured intro format to hook viewers within first 15 seconds", 
          "Added pattern interrupts every 45-60 seconds to maintain attention",
          "Created compelling preview segments at video beginning",
          "Optimized pacing based on audience attention spans"
        ]
      },
      {
        title: "Strategic SEO & Keyword Optimization",
        description: "Implemented comprehensive keyword research and optimization strategy",
        impact: "Improved search visibility by 340%",
        details: [
          "Conducted extensive keyword research for tech review terms",
          "Optimized titles with high-volume, low-competition keywords",
          "Created detailed descriptions with semantic keyword clusters",
          "Implemented strategic tag usage for better categorization",
          "Developed content calendar around trending tech topics"
        ]
      },
      {
        title: "Thumbnail & Title A/B Testing",
        description: "Systematic testing of visual elements and titles to maximize click-through rates",
        impact: "Increased CTR from 3.2% to 9.1%",
        details: [
          "Created multiple thumbnail variations for each video",
          "Tested different color schemes and visual elements",
          "Experimented with text overlay positioning and fonts",
          "A/B tested emotional triggers in titles",
          "Implemented consistent branding across all thumbnails"
        ]
      }
    ],

    timeline: [
      {
        month: "Month 1-2",
        milestone: "Analytics Setup & Initial Analysis",
        description: "Implemented comprehensive analytics tracking and conducted initial audience analysis",
        metrics: "Baseline metrics established, 12K subscribers"
      },
      {
        month: "Month 3-6", 
        milestone: "Content Format Optimization",
        description: "Restructured content based on retention data and implemented new format",
        metrics: "45K subscribers, 40% improvement in retention"
      },
      {
        month: "Month 7-12",
        milestone: "SEO & Discovery Optimization", 
        description: "Implemented comprehensive SEO strategy and thumbnail optimization",
        metrics: "180K subscribers, 250% increase in search traffic"
      },
      {
        month: "Month 13-18",
        milestone: "Community Building & Scaling",
        description: "Focused on community engagement and scaling successful strategies",
        metrics: "1M subscribers, $13K monthly revenue"
      }
    ],

    testimonial: {
      quote: "The analytics insights from ReYoutube completely transformed our approach. We went from guessing what worked to making data-driven decisions that consistently delivered results. The detailed retention analysis helped us understand exactly when and why viewers were leaving, allowing us to create content that truly engaged our audience.",
      author: "Sarah Chen",
      role: "Creator, TechReview Pro",
      avatar: "/images/testimonials/sarah-chen.jpg"
    },

    keyLessons: [
      "Data-driven content decisions consistently outperform intuition-based approaches",
      "Small improvements in retention can lead to massive growth in recommendations",
      "Thumbnail and title optimization can double or triple your click-through rates",
      "Community engagement is crucial for long-term sustainable growth",
      "Consistency in analytics review and optimization is key to success"
    ],

    tools: [
      "ReYoutube Analytics Platform",
      "YouTube Studio Analytics", 
      "VidIQ for keyword research",
      "Canva for thumbnail creation",
      "Google Trends for topic research"
    ]
  }
}

export default function CaseStudyDetailPage() {
  const params = useParams()
  const slug = params.slug as string
  const [caseStudy, setCaseStudy] = useState<any>(null)
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    const study = caseStudyDetails[slug as keyof typeof caseStudyDetails]
    setCaseStudy(study)
    setIsLoading(false)
  }, [slug])

  if (isLoading) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
          <p>Loading case study...</p>
        </div>
      </div>
    )
  }

  if (!caseStudy) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold mb-4">Case Study Not Found</h1>
          <p className="text-muted-foreground mb-6">The case study you're looking for doesn't exist.</p>
          <Button asChild>
            <Link href="/case-studies">
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to Case Studies
            </Link>
          </Button>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-background">
      <div className="container mx-auto px-4 py-8 max-w-4xl">
        {/* Back Button */}
        <Button variant="ghost" asChild className="mb-6">
          <Link href="/case-studies">
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to Case Studies
          </Link>
        </Button>

        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center gap-2 mb-4">
            <Badge variant="secondary">{caseStudy.niche}</Badge>
            <Badge variant="outline">{caseStudy.readTime}</Badge>
          </div>
          
          <h1 className="text-3xl md:text-4xl font-bold mb-4">{caseStudy.title}</h1>
          <p className="text-xl text-primary font-semibold mb-2">{caseStudy.channelName}</p>
          <p className="text-muted-foreground">Published on {caseStudy.publishDate}</p>
        </div>

        {/* Overview */}
        <Card className="mb-8">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Target className="h-5 w-5" />
              Case Study Overview
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <h3 className="font-semibold mb-2">Challenge</h3>
              <p className="text-muted-foreground">{caseStudy.overview.challenge}</p>
            </div>
            <div>
              <h3 className="font-semibold mb-2">Solution</h3>
              <p className="text-muted-foreground">{caseStudy.overview.solution}</p>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 pt-4">
              <div>
                <h3 className="font-semibold mb-1">Timeframe</h3>
                <p className="text-muted-foreground">{caseStudy.overview.timeframe}</p>
              </div>
              <div>
                <h3 className="font-semibold mb-1">Investment</h3>
                <p className="text-muted-foreground">{caseStudy.overview.investment}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Before/After Metrics */}
        <Card className="mb-8">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <BarChart3 className="h-5 w-5" />
              Before vs After Results
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {Object.entries(caseStudy.beforeAfter).map(([key, data]: [string, any]) => (
                <div key={key} className="p-4 bg-muted/30 rounded-lg">
                  <h4 className="font-semibold mb-2 capitalize">
                    {key.replace(/([A-Z])/g, ' $1').trim()}
                  </h4>
                  <div className="space-y-1">
                    <div className="text-sm text-muted-foreground">
                      Before: {data.before}
                    </div>
                    <div className="text-sm font-semibold">
                      After: {data.after}
                    </div>
                    <div className="text-sm font-bold text-green-600">
                      Growth: +{data.growth}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Continue with more content... */}
        <div className="text-center mt-8">
          <Button asChild>
            <Link href="/case-studies">
              View More Case Studies
            </Link>
          </Button>
        </div>
      </div>
    </div>
  )
}
