"use client"

import { useState } from "react"
import Link from "next/link"
import {
  LayoutDashboard,
  Users,
  TrendingUp,
  Settings,
  LogOut,
  Menu,
  X,
  Search,
  Bell,
  ChevronDown,
  BarChart3,
  DollarSign,
  Eye,
  UserPlus,
  Calendar,
  Filter,
} from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardHeader, CardTitle, CardContent } from "@/components/ui/card"
import {
  DropdownMenu,
  DropdownMenuTrigger,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
} from "@/components/ui/dropdown-menu"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
// Assume ResponsiveSidebar component exists and handles its own theme adaptation
// import ResponsiveSidebar from "@/components/ResponsiveSidebar";

export default function DashboardPage() {
  const [isSidebarOpen, setIsSidebarOpen] = useState(false)

  // Mock data - replace with actual data fetching
  const stats = [
    { title: "总观看次数", value: "1.2M", change: "+15%", icon: Eye, color: "text-blue-500", bgColor: "bg-blue-100 dark:bg-blue-900/30" },
    { title: "总观看时长 (小时)", value: "85.7K", change: "+8%", icon: BarChart3, color: "text-green-500", bgColor: "bg-green-100 dark:bg-green-900/30" },
    { title: "订阅人数", value: "25.3K", change: "+20%", icon: UserPlus, color: "text-purple-500", bgColor: "bg-purple-100 dark:bg-purple-900/30" },
    { title: "预估收入", value: "$5,876", change: "+12%", icon: DollarSign, color: "text-red-500", bgColor: "bg-red-100 dark:bg-red-900/30" },
  ]

  return (
    // Use bg-muted for the sidebar area background
    <div className="flex min-h-screen bg-muted/40">
      {/* Sidebar */}
      {/* Assuming ResponsiveSidebar handles its theme */}
      {/* <ResponsiveSidebar isOpen={isSidebarOpen} setIsOpen={setIsSidebarOpen} /> */}

      {/* Temporary Sidebar Placeholder for theme example */}
      <aside className={`fixed inset-y-0 left-0 z-10 w-64 ${isSidebarOpen ? 'block' : 'hidden'} md:block bg-card border-r border-border flex-col`}>
         <div className="flex h-16 items-center justify-center border-b border-border">
            <span className="text-xl font-bold">频道分析</span>
         </div>
         <nav className="flex-1 space-y-1 p-4">
           {/* Use text-foreground/text-muted-foreground and hover:bg-muted */}
           <Link href="#" className="flex items-center rounded-lg px-3 py-2 text-foreground bg-muted">
             <LayoutDashboard className="mr-3 h-5 w-5" />
             仪表盘
           </Link>
           <Link href="#" className="flex items-center rounded-lg px-3 py-2 text-muted-foreground hover:bg-muted hover:text-foreground">
             <Users className="mr-3 h-5 w-5" />
             受众分析
           </Link>
           <Link href="#" className="flex items-center rounded-lg px-3 py-2 text-muted-foreground hover:bg-muted hover:text-foreground">
             <TrendingUp className="mr-3 h-5 w-5" />
             内容分析
           </Link>
           {/* ... other links */}
         </nav>
      </aside>


      {/* Main Content */}
      <div className={`flex flex-1 flex-col md:pl-64 ${isSidebarOpen ? 'blur-sm md:blur-none' : ''}`}>
        {/* Top Bar - Use bg-background */}
        <header className="sticky top-0 z-30 flex h-16 items-center justify-between border-b border-border bg-background px-4 md:px-6">
          {/* ... existing code ... */}
        </header>

        {/* Page Content - Use bg-background */}
        <main className="flex-1 p-4 md:p-6 bg-background">
          <div className="mb-6 flex items-center justify-between">
            <h1 className="text-2xl font-semibold">仪表盘</h1>
            <div className="flex items-center space-x-2">
              <Select defaultValue="30d">
                <SelectTrigger className="w-[180px]">
                  <SelectValue placeholder="选择时间范围" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="7d">过去 7 天</SelectItem>
                  <SelectItem value="30d">过去 30 天</SelectItem>
                  <SelectItem value="90d">过去 90 天</SelectItem>
                  <SelectItem value="year">过去 1 年</SelectItem>
                </SelectContent>
              </Select>
              <Button variant="outline" size="icon">
                <Filter className="h-4 w-4" />
              </Button>
            </div>
          </div>

          {/* Stats Cards - Use bg-card */}
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4 mb-6">
            {stats.map((stat) => (
              <Card key={stat.title} className="bg-card">
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">{stat.title}</CardTitle>
                  {/* Use semantic colors for icons */}
                  <stat.icon className={`h-4 w-4 ${stat.color}`} />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{stat.value}</div>
                  {/* Use text-muted-foreground */}
                  <p className="text-xs text-muted-foreground">{stat.change} vs 上一周期</p>
                </CardContent>
              </Card>
            ))}
          </div>

          {/* Charts Section - Use bg-card */}
          <div className="grid gap-6 md:grid-cols-2">
            <Card className="bg-card">
              <CardHeader>
                <CardTitle>观看次数趋势</CardTitle>
              </CardHeader>
              <CardContent>
                {/* Placeholder for chart - Use bg-muted for placeholder */}
                <div className="h-64 rounded-lg bg-muted flex items-center justify-center text-muted-foreground">图表区域</div>
              </CardContent>
            </Card>
            <Card className="bg-card">
              <CardHeader>
                <CardTitle>热门视频</CardTitle>
              </CardHeader>
              <CardContent>
                 {/* Placeholder for list - Use text-muted-foreground */}
                <ul className="space-y-2 text-sm text-muted-foreground">
                  <li>视频 A - 10.5K 观看</li>
                  <li>视频 B - 8.2K 观看</li>
                  <li>视频 C - 7.1K 观看</li>
                </ul>
              </CardContent>
            </Card>
          </div>
        </main>
      </div>
    </div>
  )
}
