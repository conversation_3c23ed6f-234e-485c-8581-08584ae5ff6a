"use client"

import Link from "next/link"
import { useParams } from "next/navigation"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { 
  Home, 
  BarChart3, 
  BookOpen, 
  FileText, 
  Users, 
  Settings, 
  Shield, 
  Mail,
  Search,
  TrendingUp,
  Star,
  CreditCard,
  HelpCircle
} from "lucide-react"

export default function SitemapPage() {
  const params = useParams()
  const locale = params.locale as string || 'en'

  const siteStructure = [
    {
      title: "Main Pages",
      icon: Home,
      pages: [
        { name: "Home", url: `/${locale}`, description: "Main landing page with features and search" },
        { name: "About Us", url: `/${locale}/about`, description: "Learn about ReYoutube and our mission" },
        { name: "Contact", url: `/${locale}/contact`, description: "Get in touch with our support team" },
      ]
    },
    {
      title: "Analytics & Tools",
      icon: BarChart3,
      pages: [
        { name: "Dashboard", url: `/${locale}/dashboard`, description: "Your personal analytics dashboard" },
        { name: "Channel Analysis", url: `/${locale}/channel/external`, description: "Analyze any YouTube channel" },
        { name: "Compare Channels", url: `/${locale}/compare`, description: "Side-by-side channel comparison" },
        { name: "Rankings", url: `/${locale}/rankings`, description: "Top performing channels and videos" },
        { name: "Reports", url: `/${locale}/reports`, description: "Detailed analytics reports" },
        { name: "Watch Analytics", url: `/${locale}/watch`, description: "Video performance analysis" },
      ]
    },
    {
      title: "Learning Resources",
      icon: BookOpen,
      pages: [
        { name: "Blog", url: `/${locale}/blog`, description: "Latest insights and strategies" },
        { name: "YouTube Analytics Guide", url: `/${locale}/blog/complete-guide-youtube-analytics`, description: "Complete guide to understanding YouTube analytics" },
        { name: "YouTube SEO Guide", url: `/${locale}/blog/youtube-seo-mastery-guide`, description: "Master YouTube SEO for better rankings" },
        { name: "Guides", url: `/${locale}/guides`, description: "Step-by-step tutorials and guides" },
        { name: "Analytics Fundamentals", url: `/${locale}/guides/youtube-analytics-fundamentals`, description: "Beginner's guide to YouTube analytics" },
        { name: "Advanced SEO Strategies", url: `/${locale}/guides/advanced-youtube-seo-strategies`, description: "Advanced YouTube SEO techniques" },
        { name: "Resources", url: `/${locale}/resources`, description: "Tools and resources for creators" },
      ]
    },
    {
      title: "User Account",
      icon: Users,
      pages: [
        { name: "Login", url: `/${locale}/login`, description: "Sign in to your account" },
        { name: "Profile", url: `/${locale}/profile`, description: "Manage your profile and settings" },
        { name: "Favorites", url: `/${locale}/favorites`, description: "Your saved channels and videos" },
        { name: "Tasks", url: `/${locale}/tasks`, description: "Manage your analytics tasks" },
        { name: "Orders", url: `/${locale}/orders`, description: "View your subscription and orders" },
      ]
    },
    {
      title: "Legal & Support",
      icon: Shield,
      pages: [
        { name: "Privacy Policy", url: `/${locale}/privacy`, description: "How we protect your privacy" },
        { name: "Terms of Service", url: `/${locale}/terms`, description: "Terms and conditions of use" },
        { name: "Cookie Policy", url: `/${locale}/cookies`, description: "Information about our cookie usage" },
        { name: "Sitemap", url: `/${locale}/sitemap`, description: "Complete site navigation" },
      ]
    }
  ]

  const quickLinks = [
    { name: "Start Free Trial", url: `/${locale}/login`, icon: Star, color: "text-green-600" },
    { name: "Analyze Channel", url: `/${locale}/channel/external`, icon: Search, color: "text-blue-600" },
    { name: "Compare Channels", url: `/${locale}/compare`, icon: TrendingUp, color: "text-purple-600" },
    { name: "View Guides", url: `/${locale}/guides`, icon: BookOpen, color: "text-orange-600" },
    { name: "Contact Support", url: `/${locale}/contact`, icon: HelpCircle, color: "text-red-600" },
  ]

  return (
    <div className="min-h-screen bg-background">
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="text-center mb-12">
          <Badge variant="outline" className="mb-4">Navigation</Badge>
          <h1 className="text-4xl md:text-5xl font-bold mb-6">Site Map</h1>
          <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
            Find everything you need on ReYoutube. Explore our comprehensive YouTube analytics platform, 
            learning resources, and support pages.
          </p>
        </div>

        {/* Quick Links */}
        <div className="mb-16">
          <h2 className="text-2xl font-bold text-center mb-8">Quick Access</h2>
          <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-5 gap-4">
            {quickLinks.map((link) => (
              <Link key={link.name} href={link.url}>
                <Card className="hover:shadow-lg transition-shadow cursor-pointer h-full">
                  <CardContent className="p-6 text-center">
                    <link.icon className={`h-8 w-8 mx-auto mb-3 ${link.color}`} />
                    <h3 className="font-semibold text-sm">{link.name}</h3>
                  </CardContent>
                </Card>
              </Link>
            ))}
          </div>
        </div>

        {/* Main Site Structure */}
        <div className="space-y-8">
          {siteStructure.map((section) => (
            <Card key={section.title}>
              <CardHeader>
                <CardTitle className="flex items-center gap-3">
                  <section.icon className="h-6 w-6 text-primary" />
                  {section.title}
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {section.pages.map((page) => (
                    <Link key={page.url} href={page.url}>
                      <div className="p-4 rounded-lg border hover:bg-muted transition-colors cursor-pointer">
                        <h4 className="font-semibold mb-2 text-primary hover:underline">
                          {page.name}
                        </h4>
                        <p className="text-sm text-muted-foreground">
                          {page.description}
                        </p>
                      </div>
                    </Link>
                  ))}
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Additional Information */}
        <div className="mt-16 grid grid-cols-1 md:grid-cols-2 gap-8">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <FileText className="h-5 w-5" />
                Technical Information
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <h4 className="font-semibold mb-2">XML Sitemap</h4>
                <p className="text-sm text-muted-foreground mb-2">
                  For search engines and automated tools:
                </p>
                <Link 
                  href="/sitemap.xml" 
                  className="text-primary hover:underline text-sm"
                  target="_blank"
                >
                  /sitemap.xml
                </Link>
              </div>
              <div>
                <h4 className="font-semibold mb-2">Robots.txt</h4>
                <p className="text-sm text-muted-foreground mb-2">
                  Crawler instructions and guidelines:
                </p>
                <Link 
                  href="/robots.txt" 
                  className="text-primary hover:underline text-sm"
                  target="_blank"
                >
                  /robots.txt
                </Link>
              </div>
              <div>
                <h4 className="font-semibold mb-2">API Documentation</h4>
                <p className="text-sm text-muted-foreground">
                  Developer resources and API endpoints available for integration.
                </p>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Mail className="h-5 w-5" />
                Need Help?
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <h4 className="font-semibold mb-2">Can't Find What You're Looking For?</h4>
                <p className="text-sm text-muted-foreground mb-3">
                  Our support team is here to help you navigate ReYoutube and find the information you need.
                </p>
                <Link href={`/${locale}/contact`}>
                  <Badge variant="outline" className="cursor-pointer hover:bg-muted">
                    Contact Support
                  </Badge>
                </Link>
              </div>
              <div>
                <h4 className="font-semibold mb-2">Search Our Site</h4>
                <p className="text-sm text-muted-foreground mb-3">
                  Use our powerful search feature to find specific channels, guides, or information.
                </p>
                <Link href={`/${locale}`}>
                  <Badge variant="outline" className="cursor-pointer hover:bg-muted">
                    Go to Search
                  </Badge>
                </Link>
              </div>
              <div>
                <h4 className="font-semibold mb-2">Feature Requests</h4>
                <p className="text-sm text-muted-foreground">
                  Have an idea for a new feature? We'd love to hear from you and consider it for future updates.
                </p>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Footer Note */}
        <div className="mt-16 text-center">
          <Card className="bg-muted/50">
            <CardContent className="p-6">
              <h3 className="font-semibold mb-2">About This Sitemap</h3>
              <p className="text-sm text-muted-foreground max-w-2xl mx-auto">
                This sitemap is updated regularly to reflect new content and features. 
                Last updated: January 2024. If you notice any broken links or missing pages, 
                please let us know through our contact form.
              </p>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}
