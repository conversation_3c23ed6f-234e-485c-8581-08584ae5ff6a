'use client';

import { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Loader2, Play, Users, Eye, ThumbsUp, MessageSquare, Calendar, Clock } from 'lucide-react';
import Image from 'next/image';

interface ChannelData {
  id: string;
  title: string;
  description: string;
  customUrl?: string;
  publishedAt: string;
  thumbnails: {
    default: { url: string };
    medium: { url: string };
    high: { url: string };
  };
  country?: string;
  statistics: {
    viewCount: number;
    subscriberCount: number;
    videoCount: number;
    hiddenSubscriberCount: boolean;
  };
  metadata: {
    subscriberCountFormatted: string;
    viewCountFormatted: string;
    videoCountFormatted: string;
    createdYearsAgo: number;
  };
}

interface VideoData {
  id: string;
  title: string;
  description: string;
  publishedAt: string;
  channelId: string;
  channelTitle: string;
  thumbnails: {
    default: { url: string };
    medium: { url: string };
    high: { url: string };
  };
  tags: string[];
  statistics: {
    viewCount: number;
    likeCount: number;
    commentCount: number;
  };
  contentDetails: {
    duration: string;
    definition: string;
    caption: boolean;
  };
  metadata: {
    viewCountFormatted: string;
    likeCountFormatted: string;
    commentCountFormatted: string;
    durationFormatted: string;
    publishedDaysAgo: number;
  };
}

export default function YouTubeAPITestPage() {
  const [channelInput, setChannelInput] = useState('');
  const [videoInput, setVideoInput] = useState('');
  const [channelData, setChannelData] = useState<ChannelData | null>(null);
  const [videoData, setVideoData] = useState<VideoData | null>(null);
  const [channelLoading, setChannelLoading] = useState(false);
  const [videoLoading, setVideoLoading] = useState(false);
  const [channelError, setChannelError] = useState<string | null>(null);
  const [videoError, setVideoError] = useState<string | null>(null);
  const [useMockData, setUseMockData] = useState(false);

  const testChannelAPI = async () => {
    if (!channelInput.trim()) return;

    setChannelLoading(true);
    setChannelError(null);
    setChannelData(null);

    try {
      // 判断输入是频道ID还是用户名
      const isChannelId = channelInput.startsWith('UC') || channelInput.startsWith('@');
      const params = new URLSearchParams();
      
      if (isChannelId && channelInput.startsWith('@')) {
        params.set('username', channelInput.substring(1)); // 移除 @ 符号
      } else if (isChannelId) {
        params.set('id', channelInput);
      } else {
        params.set('username', channelInput);
      }

      const apiEndpoint = useMockData ? '/api/youtube/channels/mock' : '/api/youtube/channels';
      const response = await fetch(`${apiEndpoint}?${params.toString()}`);
      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.message || 'API request failed');
      }

      setChannelData(result.data);
    } catch (error) {
      setChannelError(error instanceof Error ? error.message : 'Unknown error occurred');
    } finally {
      setChannelLoading(false);
    }
  };

  const testVideoAPI = async () => {
    if (!videoInput.trim()) return;

    setVideoLoading(true);
    setVideoError(null);
    setVideoData(null);

    try {
      // 从 YouTube URL 中提取视频ID，或直接使用输入的ID
      let videoId = videoInput;
      const urlMatch = videoInput.match(/(?:youtube\.com\/watch\?v=|youtu\.be\/)([^&\n?#]+)/);
      if (urlMatch) {
        videoId = urlMatch[1];
      }

      const apiEndpoint = useMockData ? '/api/youtube/videos/mock' : '/api/youtube/videos';
      const response = await fetch(`${apiEndpoint}?id=${encodeURIComponent(videoId)}`);
      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.message || 'API request failed');
      }

      setVideoData(result.data);
    } catch (error) {
      setVideoError(error instanceof Error ? error.message : 'Unknown error occurred');
    } finally {
      setVideoLoading(false);
    }
  };

  return (
    <div className="container mx-auto py-8 px-4">
      <div className="max-w-6xl mx-auto">
        <div className="mb-8">
          <h1 className="text-3xl font-bold mb-2">YouTube API 测试页面</h1>
          <p className="text-muted-foreground mb-4">
            测试 Google YouTube Data API v3 的频道和视频信息获取功能
          </p>

          <div className="flex items-center space-x-2 p-4 bg-muted/50 rounded-lg">
            <input
              type="checkbox"
              id="mock-data"
              checked={useMockData}
              onChange={(e) => setUseMockData(e.target.checked)}
              className="rounded"
            />
            <label htmlFor="mock-data" className="text-sm font-medium">
              使用模拟数据 (网络受限时推荐)
            </label>
            {useMockData && (
              <Badge variant="secondary" className="ml-2">
                模拟模式
              </Badge>
            )}
          </div>

          {useMockData && (
            <Alert className="mt-4">
              <AlertDescription>
                <strong>模拟模式已启用</strong> - 使用预设的测试数据，无需网络连接到 YouTube API。
                可用测试数据: GoogleDevelopers, TechCrunch, dQw4w9WgXcQ, jNQXAC9IVRw
              </AlertDescription>
            </Alert>
          )}
        </div>

        <Tabs defaultValue="channels" className="space-y-6">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="channels">频道信息测试</TabsTrigger>
            <TabsTrigger value="videos">视频信息测试</TabsTrigger>
          </TabsList>

          <TabsContent value="channels" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>频道信息获取</CardTitle>
                <CardDescription>
                  输入频道ID (如: UCxxxxxx) 或用户名 (如: @username 或 username) 来获取频道信息
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex gap-4">
                  <div className="flex-1">
                    <Label htmlFor="channel-input">频道ID或用户名</Label>
                    <Input
                      id="channel-input"
                      placeholder="例如: @GoogleDevelopers 或 UCVHFbqXqoYvEWM1Ddxl0QDg"
                      value={channelInput}
                      onChange={(e) => setChannelInput(e.target.value)}
                      onKeyPress={(e) => e.key === 'Enter' && testChannelAPI()}
                    />
                  </div>
                  <div className="flex items-end">
                    <Button onClick={testChannelAPI} disabled={channelLoading || !channelInput.trim()}>
                      {channelLoading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                      测试获取
                    </Button>
                  </div>
                </div>

                {channelError && (
                  <Alert variant="destructive">
                    <AlertDescription>{channelError}</AlertDescription>
                  </Alert>
                )}

                {channelData && (
                  <Card className="mt-6">
                    <CardContent className="pt-6">
                      <div className="flex gap-6">
                        <div className="flex-shrink-0">
                          <Image
                            src={channelData.thumbnails.high.url}
                            alt={channelData.title}
                            width={120}
                            height={120}
                            className="rounded-full"
                          />
                        </div>
                        <div className="flex-1 space-y-4">
                          <div>
                            <h3 className="text-2xl font-bold">{channelData.title}</h3>
                            {channelData.customUrl && (
                              <p className="text-muted-foreground">@{channelData.customUrl}</p>
                            )}
                            <Badge variant="secondary" className="mt-2">
                              ID: {channelData.id}
                            </Badge>
                          </div>

                          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                            <div className="text-center">
                              <div className="flex items-center justify-center gap-1 text-muted-foreground mb-1">
                                <Users className="h-4 w-4" />
                                <span className="text-sm">订阅者</span>
                              </div>
                              <p className="text-xl font-semibold">{channelData.metadata.subscriberCountFormatted}</p>
                            </div>
                            <div className="text-center">
                              <div className="flex items-center justify-center gap-1 text-muted-foreground mb-1">
                                <Eye className="h-4 w-4" />
                                <span className="text-sm">总观看</span>
                              </div>
                              <p className="text-xl font-semibold">{channelData.metadata.viewCountFormatted}</p>
                            </div>
                            <div className="text-center">
                              <div className="flex items-center justify-center gap-1 text-muted-foreground mb-1">
                                <Play className="h-4 w-4" />
                                <span className="text-sm">视频数</span>
                              </div>
                              <p className="text-xl font-semibold">{channelData.metadata.videoCountFormatted}</p>
                            </div>
                            <div className="text-center">
                              <div className="flex items-center justify-center gap-1 text-muted-foreground mb-1">
                                <Calendar className="h-4 w-4" />
                                <span className="text-sm">创建</span>
                              </div>
                              <p className="text-xl font-semibold">{channelData.metadata.createdYearsAgo}年前</p>
                            </div>
                          </div>

                          <Separator />

                          <div>
                            <h4 className="font-semibold mb-2">频道描述</h4>
                            <p className="text-sm text-muted-foreground line-clamp-3">
                              {channelData.description || '暂无描述'}
                            </p>
                          </div>

                          {channelData.country && (
                            <div>
                              <h4 className="font-semibold mb-2">国家/地区</h4>
                              <Badge variant="outline">{channelData.country}</Badge>
                            </div>
                          )}
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                )}
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="videos" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>视频信息获取</CardTitle>
                <CardDescription>
                  输入视频ID或完整的YouTube视频链接来获取视频详细信息
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex gap-4">
                  <div className="flex-1">
                    <Label htmlFor="video-input">视频ID或YouTube链接</Label>
                    <Input
                      id="video-input"
                      placeholder="例如: dQw4w9WgXcQ 或 https://www.youtube.com/watch?v=dQw4w9WgXcQ"
                      value={videoInput}
                      onChange={(e) => setVideoInput(e.target.value)}
                      onKeyPress={(e) => e.key === 'Enter' && testVideoAPI()}
                    />
                  </div>
                  <div className="flex items-end">
                    <Button onClick={testVideoAPI} disabled={videoLoading || !videoInput.trim()}>
                      {videoLoading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                      测试获取
                    </Button>
                  </div>
                </div>

                {videoError && (
                  <Alert variant="destructive">
                    <AlertDescription>{videoError}</AlertDescription>
                  </Alert>
                )}

                {videoData && (
                  <Card className="mt-6">
                    <CardContent className="pt-6">
                      <div className="space-y-6">
                        <div className="flex gap-6">
                          <div className="flex-shrink-0">
                            <Image
                              src={videoData.thumbnails.high.url}
                              alt={videoData.title}
                              width={320}
                              height={180}
                              className="rounded-lg"
                            />
                          </div>
                          <div className="flex-1 space-y-4">
                            <div>
                              <h3 className="text-xl font-bold line-clamp-2">{videoData.title}</h3>
                              <p className="text-muted-foreground mt-1">
                                频道: {videoData.channelTitle}
                              </p>
                              <Badge variant="secondary" className="mt-2">
                                ID: {videoData.id}
                              </Badge>
                            </div>

                            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                              <div className="text-center">
                                <div className="flex items-center justify-center gap-1 text-muted-foreground mb-1">
                                  <Eye className="h-4 w-4" />
                                  <span className="text-sm">观看</span>
                                </div>
                                <p className="text-lg font-semibold">{videoData.metadata.viewCountFormatted}</p>
                              </div>
                              <div className="text-center">
                                <div className="flex items-center justify-center gap-1 text-muted-foreground mb-1">
                                  <ThumbsUp className="h-4 w-4" />
                                  <span className="text-sm">点赞</span>
                                </div>
                                <p className="text-lg font-semibold">{videoData.metadata.likeCountFormatted}</p>
                              </div>
                              <div className="text-center">
                                <div className="flex items-center justify-center gap-1 text-muted-foreground mb-1">
                                  <MessageSquare className="h-4 w-4" />
                                  <span className="text-sm">评论</span>
                                </div>
                                <p className="text-lg font-semibold">{videoData.metadata.commentCountFormatted}</p>
                              </div>
                              <div className="text-center">
                                <div className="flex items-center justify-center gap-1 text-muted-foreground mb-1">
                                  <Clock className="h-4 w-4" />
                                  <span className="text-sm">时长</span>
                                </div>
                                <p className="text-lg font-semibold">{videoData.metadata.durationFormatted}</p>
                              </div>
                            </div>

                            <div className="flex gap-2">
                              <Badge variant="outline">{videoData.contentDetails.definition.toUpperCase()}</Badge>
                              {videoData.contentDetails.caption && (
                                <Badge variant="outline">字幕</Badge>
                              )}
                              <Badge variant="outline">
                                {videoData.metadata.publishedDaysAgo}天前发布
                              </Badge>
                            </div>
                          </div>
                        </div>

                        <Separator />

                        <div>
                          <h4 className="font-semibold mb-2">视频描述</h4>
                          <p className="text-sm text-muted-foreground line-clamp-4 whitespace-pre-line">
                            {videoData.description || '暂无描述'}
                          </p>
                        </div>

                        {videoData.tags.length > 0 && (
                          <div>
                            <h4 className="font-semibold mb-2">标签</h4>
                            <div className="flex flex-wrap gap-2">
                              {videoData.tags.slice(0, 10).map((tag, index) => (
                                <Badge key={index} variant="secondary" className="text-xs">
                                  {tag}
                                </Badge>
                              ))}
                              {videoData.tags.length > 10 && (
                                <Badge variant="outline" className="text-xs">
                                  +{videoData.tags.length - 10} 更多
                                </Badge>
                              )}
                            </div>
                          </div>
                        )}
                      </div>
                    </CardContent>
                  </Card>
                )}
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
}
