"use client"

import { useState } from "react"
import { <PERSON>, Card<PERSON><PERSON>nt, CardHeader, Card<PERSON>itle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { 
  Search,
  BookOpen,
  TrendingUp,
  Users,
  Eye,
  Clock,
  DollarSign,
  BarChart3,
  Target,
  Zap,
  Globe,
  Play
} from "lucide-react"

interface GlossaryTerm {
  id: string
  term: string
  definition: string
  category: string
  importance: "high" | "medium" | "low"
  relatedTerms: string[]
  example?: string
  formula?: string
}

const glossaryTerms: GlossaryTerm[] = [
  {
    id: "1",
    term: "Watch Time",
    definition: "The total amount of time viewers spend watching your videos. This is one of the most important metrics for YouTube's algorithm as it indicates how engaging your content is.",
    category: "Core Metrics",
    importance: "high",
    relatedTerms: ["Average View Duration", "Audience Retention", "Session Duration"],
    example: "If your video is 10 minutes long and 100 people watch it for an average of 6 minutes, your total watch time is 600 minutes (10 hours)."
  },
  {
    id: "2", 
    term: "Click-Through Rate (CTR)",
    definition: "The percentage of people who click on your video after seeing the thumbnail and title. CTR is calculated by dividing clicks by impressions.",
    category: "Discovery Metrics",
    importance: "high",
    relatedTerms: ["Impressions", "Thumbnail", "Title Optimization"],
    formula: "CTR = (Clicks ÷ Impressions) × 100",
    example: "If your video thumbnail is shown 1,000 times and 50 people click on it, your CTR is 5%."
  },
  {
    id: "3",
    term: "Average View Duration (AVD)",
    definition: "The average amount of time viewers spend watching your video. This metric helps you understand how engaging your content is throughout the video.",
    category: "Engagement Metrics",
    importance: "high", 
    relatedTerms: ["Watch Time", "Audience Retention", "Drop-off Points"],
    example: "If your 10-minute video has an average view duration of 4 minutes, viewers on average watch 40% of your content."
  },
  {
    id: "4",
    term: "Audience Retention",
    definition: "A graph showing the percentage of your video that viewers watch at each moment. It helps identify which parts of your video are most and least engaging.",
    category: "Engagement Metrics",
    importance: "high",
    relatedTerms: ["Average View Duration", "Drop-off Points", "Re-watch Segments"],
    example: "If 100 people start watching your video and 60 are still watching at the 2-minute mark, your retention at 2 minutes is 60%."
  },
  {
    id: "5",
    term: "Impressions",
    definition: "The number of times your video thumbnail was shown to viewers on YouTube. This includes appearances in search results, suggested videos, and browse features.",
    category: "Discovery Metrics", 
    importance: "medium",
    relatedTerms: ["Click-Through Rate", "Reach", "Traffic Sources"],
    example: "Your video thumbnail appears in search results 500 times and in suggested videos 300 times, giving you 800 total impressions."
  },
  {
    id: "6",
    term: "RPM (Revenue Per Mille)",
    definition: "The revenue you earn per 1,000 video views. RPM accounts for all revenue sources including ads, channel memberships, and Super Chat.",
    category: "Monetization",
    importance: "high",
    relatedTerms: ["CPM", "Ad Revenue", "Monetization"],
    formula: "RPM = (Total Revenue ÷ Total Views) × 1,000",
    example: "If you earn $50 from 10,000 views, your RPM is $5.00."
  },
  {
    id: "7",
    term: "CPM (Cost Per Mille)",
    definition: "The amount advertisers pay per 1,000 ad impressions on your videos. This is different from RPM as it only accounts for ad revenue before YouTube's revenue share.",
    category: "Monetization",
    importance: "medium",
    relatedTerms: ["RPM", "Ad Revenue", "Monetized Playbacks"],
    example: "If advertisers pay $8 per 1,000 ad views and YouTube takes 45%, your effective CPM is $4.40."
  },
  {
    id: "8",
    term: "Subscriber Conversion Rate",
    definition: "The percentage of viewers who subscribe to your channel after watching your videos. This metric indicates how well your content attracts new subscribers.",
    category: "Growth Metrics",
    importance: "medium",
    relatedTerms: ["Subscribers", "Channel Growth", "Viewer Loyalty"],
    formula: "Conversion Rate = (New Subscribers ÷ Views) × 100",
    example: "If 100 people subscribe after 10,000 views, your subscriber conversion rate is 1%."
  },
  {
    id: "9",
    term: "Session Duration",
    definition: "The total time a viewer spends on YouTube after watching one of your videos. YouTube's algorithm favors videos that lead to longer sessions.",
    category: "Algorithm Metrics",
    importance: "high",
    relatedTerms: ["Watch Time", "Suggested Videos", "Algorithm"],
    example: "A viewer watches your 5-minute video, then watches 3 more videos for 20 minutes total. Your video contributed to a 20-minute session."
  },
  {
    id: "10",
    term: "Traffic Sources",
    definition: "The different ways viewers discover your videos, including YouTube search, suggested videos, external websites, and direct traffic.",
    category: "Discovery Metrics",
    importance: "medium",
    relatedTerms: ["Discovery", "SEO", "External Traffic"],
    example: "40% of your views come from YouTube search, 35% from suggested videos, 15% from external sources, and 10% from direct links."
  }
]

export default function GlossaryPage() {
  const [searchQuery, setSearchQuery] = useState("")
  const [selectedCategory, setSelectedCategory] = useState("all")
  
  const categories = ["all", "core metrics", "discovery metrics", "engagement metrics", "monetization", "growth metrics", "algorithm metrics"]
  
  const filteredTerms = glossaryTerms.filter(term => {
    const matchesSearch = term.term.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         term.definition.toLowerCase().includes(searchQuery.toLowerCase())
    
    const matchesCategory = selectedCategory === "all" || 
                           term.category.toLowerCase() === selectedCategory.toLowerCase()
    
    return matchesSearch && matchesCategory
  })

  const getImportanceColor = (importance: string) => {
    switch (importance) {
      case "high": return "bg-red-100 text-red-800"
      case "medium": return "bg-yellow-100 text-yellow-800"
      case "low": return "bg-green-100 text-green-800"
      default: return "bg-gray-100 text-gray-800"
    }
  }

  const getCategoryIcon = (category: string) => {
    switch (category.toLowerCase()) {
      case "core metrics": return <BarChart3 className="h-4 w-4" />
      case "discovery metrics": return <Search className="h-4 w-4" />
      case "engagement metrics": return <Users className="h-4 w-4" />
      case "monetization": return <DollarSign className="h-4 w-4" />
      case "growth metrics": return <TrendingUp className="h-4 w-4" />
      case "algorithm metrics": return <Zap className="h-4 w-4" />
      default: return <BookOpen className="h-4 w-4" />
    }
  }

  return (
    <div className="min-h-screen bg-background">
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="text-center mb-12">
          <Badge variant="outline" className="mb-4">Knowledge Base</Badge>
          <h1 className="text-4xl md:text-5xl font-bold mb-6">
            YouTube Analytics Glossary
          </h1>
          <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
            Master YouTube analytics terminology with our comprehensive glossary. 
            Understand key metrics, concepts, and terms that drive successful YouTube channels.
          </p>
        </div>

        {/* Stats */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-6 mb-12">
          <Card>
            <CardContent className="p-6 text-center">
              <BookOpen className="h-8 w-8 mx-auto mb-3 text-blue-500" />
              <div className="text-2xl font-bold mb-1">100+</div>
              <div className="text-sm text-muted-foreground">Terms Defined</div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-6 text-center">
              <Target className="h-8 w-8 mx-auto mb-3 text-green-500" />
              <div className="text-2xl font-bold mb-1">6</div>
              <div className="text-sm text-muted-foreground">Categories</div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-6 text-center">
              <Zap className="h-8 w-8 mx-auto mb-3 text-yellow-500" />
              <div className="text-2xl font-bold mb-1">25</div>
              <div className="text-sm text-muted-foreground">High Priority Terms</div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-6 text-center">
              <Globe className="h-8 w-8 mx-auto mb-3 text-purple-500" />
              <div className="text-2xl font-bold mb-1">Daily</div>
              <div className="text-sm text-muted-foreground">Updates</div>
            </CardContent>
          </Card>
        </div>

        {/* Search and Filter */}
        <div className="mb-8">
          <div className="flex flex-col md:flex-row gap-4 mb-6">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
              <Input
                placeholder="Search terms and definitions..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10"
              />
            </div>
          </div>
          
          <div className="flex flex-wrap gap-2">
            <span className="text-sm font-medium text-muted-foreground mr-2">Categories:</span>
            {categories.map((category) => (
              <Button
                key={category}
                variant={selectedCategory === category ? "default" : "outline"}
                size="sm"
                onClick={() => setSelectedCategory(category)}
                className="capitalize"
              >
                {category === "all" ? "All Terms" : category}
              </Button>
            ))}
          </div>
        </div>

        {/* Terms List */}
        <div className="space-y-6">
          {filteredTerms.length === 0 ? (
            <Card>
              <CardContent className="p-12 text-center">
                <Search className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
                <h3 className="text-lg font-semibold mb-2">No terms found</h3>
                <p className="text-muted-foreground">
                  Try adjusting your search query or selecting a different category.
                </p>
              </CardContent>
            </Card>
          ) : (
            filteredTerms.map((term) => (
              <Card key={term.id} className="hover:shadow-lg transition-shadow">
                <CardHeader>
                  <div className="flex items-start justify-between">
                    <div className="flex items-center gap-3">
                      {getCategoryIcon(term.category)}
                      <div>
                        <CardTitle className="text-xl">{term.term}</CardTitle>
                        <div className="flex items-center gap-2 mt-1">
                          <Badge variant="secondary">{term.category}</Badge>
                          <Badge className={getImportanceColor(term.importance)}>
                            {term.importance} priority
                          </Badge>
                        </div>
                      </div>
                    </div>
                  </div>
                </CardHeader>
                <CardContent>
                  <p className="text-muted-foreground mb-4">{term.definition}</p>
                  
                  {term.formula && (
                    <div className="bg-muted/30 p-3 rounded-lg mb-4">
                      <h4 className="font-semibold mb-1">Formula:</h4>
                      <code className="text-sm">{term.formula}</code>
                    </div>
                  )}
                  
                  {term.example && (
                    <div className="bg-blue-50 p-3 rounded-lg mb-4">
                      <h4 className="font-semibold mb-1">Example:</h4>
                      <p className="text-sm text-muted-foreground">{term.example}</p>
                    </div>
                  )}
                  
                  {term.relatedTerms.length > 0 && (
                    <div>
                      <h4 className="font-semibold mb-2">Related Terms:</h4>
                      <div className="flex flex-wrap gap-1">
                        {term.relatedTerms.map((relatedTerm) => (
                          <Badge key={relatedTerm} variant="outline" className="text-xs">
                            {relatedTerm}
                          </Badge>
                        ))}
                      </div>
                    </div>
                  )}
                </CardContent>
              </Card>
            ))
          )}
        </div>

        {/* CTA Section */}
        <div className="mt-16 text-center">
          <Card className="bg-gradient-to-r from-primary/10 to-primary/5 border-primary/20">
            <CardContent className="p-8">
              <h2 className="text-2xl font-bold mb-4">Ready to Apply These Concepts?</h2>
              <p className="text-muted-foreground mb-6 max-w-2xl mx-auto">
                Now that you understand the terminology, start analyzing your channel with our 
                comprehensive analytics platform and put these concepts into practice.
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Button size="lg">
                  Start Free Analysis
                </Button>
                <Button variant="outline" size="lg">
                  View Analytics Guide
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}
