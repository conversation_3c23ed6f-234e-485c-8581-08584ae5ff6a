"use client"

import { useState } from "react"
import { Plus, Trash2, <PERSON><PERSON><PERSON>2, Users, Eye, ThumbsUp, MessageSquare, Share2 } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { <PERSON>, CardHeader, CardTitle, CardContent } from "@/components/ui/card"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Badge } from "@/components/ui/badge"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"

// Mock data structure
interface ChannelData {
  id: string;
  name: string;
  avatar: string;
  subscribers: string;
  views: string;
  likes: string;
  comments: string;
  shares: string;
  category: string;
}

export default function ComparePage() {
  const [channels, setChannels] = useState<ChannelData[]>([
    // Initial mock data
    { id: '1', name: 'Tech Explained', avatar: '/placeholder-user.jpg', subscribers: '1.5M', views: '250M', likes: '12M', comments: '500K', shares: '1M', category: '科技' },
    { id: '2', name: 'Gadget Reviews', avatar: '/placeholder-user.jpg', subscribers: '1.2M', views: '180M', likes: '9M', comments: '350K', shares: '800K', category: '科技' },
  ]);
  const [searchTerm, setSearchTerm] = useState('');

  const addChannel = () => {
    if (channels.length < 4 && searchTerm) { // Limit to 4 channels for comparison
      // In a real app, you'd fetch channel data based on searchTerm
      const newChannel: ChannelData = {
        id: Date.now().toString(),
        name: searchTerm,
        avatar: '/placeholder-user.jpg',
        subscribers: `${(Math.random() * 2 + 0.5).toFixed(1)}M`,
        views: `${Math.floor(Math.random() * 300 + 50)}M`,
        likes: `${Math.floor(Math.random() * 15 + 1)}M`,
        comments: `${Math.floor(Math.random() * 600 + 100)}K`,
        shares: `${(Math.random() * 1.5 + 0.2).toFixed(1)}M`,
        category: '综合'
      };
      setChannels([...channels, newChannel]);
      setSearchTerm('');
    }
  };

  const removeChannel = (id: string) => {
    setChannels(channels.filter(channel => channel.id !== id));
  };

  return (
    // Use bg-background for the main container
    <div className="min-h-screen bg-background">
      {/* Top Bar already uses bg-background from layout, ensure content below respects theme */}
      <main className="container mx-auto px-4 py-8">
        {/* Header Section with Educational Content */}
        <div className="mb-12">
          <h1 className="text-3xl md:text-4xl font-bold mb-4">YouTube Channel Comparison Tool</h1>
          <p className="text-lg text-muted-foreground mb-6 max-w-3xl">
            Compare YouTube channels side-by-side to analyze performance metrics, growth trends, and competitive insights.
            Make data-driven decisions to improve your content strategy and channel performance.
          </p>

          {/* Educational Benefits */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-8">
            <div className="bg-card p-4 rounded-lg border">
              <h3 className="font-semibold mb-2">📊 Performance Analysis</h3>
              <p className="text-sm text-muted-foreground">
                Compare subscriber counts, view metrics, engagement rates, and growth patterns across multiple channels.
              </p>
            </div>
            <div className="bg-card p-4 rounded-lg border">
              <h3 className="font-semibold mb-2">🎯 Competitive Intelligence</h3>
              <p className="text-sm text-muted-foreground">
                Identify successful strategies used by competitors and discover content gaps in your niche.
              </p>
            </div>
            <div className="bg-card p-4 rounded-lg border">
              <h3 className="font-semibold mb-2">📈 Growth Insights</h3>
              <p className="text-sm text-muted-foreground">
                Understand which channels are growing fastest and what factors contribute to their success.
              </p>
            </div>
          </div>
        </div>

        {/* Add Channel Section - Use bg-card */}
        <Card className="mb-8 bg-card">
          <CardContent className="p-6 flex items-center space-x-4">
            <Input
              placeholder="输入频道名称或链接添加对比..."
              className="flex-grow"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              onKeyPress={(e) => e.key === 'Enter' && addChannel()}
            />
            <Button onClick={addChannel} disabled={channels.length >= 4 || !searchTerm}>
              <Plus className="mr-2 h-4 w-4" /> 添加对比
            </Button>
          </CardContent>
        </Card>

        {/* Comparison Grid */}
        <div className={`grid gap-6 ${channels.length > 0 ? `grid-cols-1 md:grid-cols-${Math.min(channels.length, 4)}` : ''}`}>
          {channels.map((channel) => (
            // Use bg-card for each channel card
            <Card key={channel.id} className="relative bg-card overflow-hidden">
              <Button
                variant="ghost"
                size="icon"
                className="absolute top-2 right-2 text-muted-foreground hover:text-destructive hover:bg-destructive/10"
                onClick={() => removeChannel(channel.id)}
              >
                <Trash2 className="h-4 w-4" />
              </Button>
              <CardHeader className="flex flex-row items-center space-x-4 pb-4">
                <Avatar className="h-12 w-12">
                  <AvatarImage src={channel.avatar} alt={channel.name} />
                  <AvatarFallback>{channel.name.charAt(0)}</AvatarFallback>
                </Avatar>
                <div>
                  <CardTitle className="text-lg">{channel.name}</CardTitle>
                  {/* Use text-muted-foreground */}
                  <Badge variant="secondary" className="mt-1">{channel.category}</Badge>
                </div>
              </CardHeader>
              <CardContent className="space-y-3">
                {/* Use text-muted-foreground for labels */}
                <div className="flex items-center justify-between text-sm">
                  <span className="flex items-center text-muted-foreground"><Users className="mr-2 h-4 w-4" /> 订阅数</span>
                  <span className="font-medium">{channel.subscribers}</span>
                </div>
                <div className="flex items-center justify-between text-sm">
                  <span className="flex items-center text-muted-foreground"><Eye className="mr-2 h-4 w-4" /> 总观看</span>
                  <span className="font-medium">{channel.views}</span>
                </div>
                <div className="flex items-center justify-between text-sm">
                  <span className="flex items-center text-muted-foreground"><ThumbsUp className="mr-2 h-4 w-4" /> 点赞数</span>
                  <span className="font-medium">{channel.likes}</span>
                </div>
                <div className="flex items-center justify-between text-sm">
                  <span className="flex items-center text-muted-foreground"><MessageSquare className="mr-2 h-4 w-4" /> 评论数</span>
                  <span className="font-medium">{channel.comments}</span>
                </div>
                <div className="flex items-center justify-between text-sm">
                  <span className="flex items-center text-muted-foreground"><Share2 className="mr-2 h-4 w-4" /> 分享数</span>
                  <span className="font-medium">{channel.shares}</span>
                </div>
              </CardContent>
            </Card>
          ))}

          {channels.length === 0 && (
            // Use text-muted-foreground for placeholder text
            <div className="text-center py-12 text-muted-foreground col-span-full">
              <BarChart2 className="mx-auto h-12 w-12 mb-4" />
              <h3 className="text-lg font-semibold mb-2">Start Your Channel Comparison</h3>
              <p className="mb-4">Add at least one channel to begin comparing performance metrics and insights.</p>
              <div className="text-sm text-left max-w-md mx-auto">
                <h4 className="font-medium mb-2">How to use this tool:</h4>
                <ul className="space-y-1">
                  <li>• Enter a channel name or URL in the search box above</li>
                  <li>• Add up to 4 channels for comprehensive comparison</li>
                  <li>• Analyze metrics like subscribers, views, and engagement</li>
                  <li>• Identify trends and opportunities for growth</li>
                </ul>
              </div>
            </div>
          )}
        </div>

        {/* Educational Content Section */}
        <div className="mt-16">
          <h2 className="text-2xl font-bold mb-6">How to Effectively Compare YouTube Channels</h2>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
            <div>
              <h3 className="text-lg font-semibold mb-3">Key Metrics to Compare</h3>
              <div className="space-y-3">
                <div className="border-l-4 border-blue-500 pl-4">
                  <h4 className="font-medium">Subscriber Count</h4>
                  <p className="text-sm text-muted-foreground">
                    Compare total subscribers to understand channel size and reach potential.
                  </p>
                </div>
                <div className="border-l-4 border-green-500 pl-4">
                  <h4 className="font-medium">View Count & Engagement</h4>
                  <p className="text-sm text-muted-foreground">
                    Analyze total views, average views per video, and engagement rates.
                  </p>
                </div>
                <div className="border-l-4 border-purple-500 pl-4">
                  <h4 className="font-medium">Content Strategy</h4>
                  <p className="text-sm text-muted-foreground">
                    Compare video frequency, content types, and posting schedules.
                  </p>
                </div>
              </div>
            </div>

            <div>
              <h3 className="text-lg font-semibold mb-3">Analysis Best Practices</h3>
              <div className="space-y-3">
                <div className="bg-card p-3 rounded border">
                  <h4 className="font-medium text-sm">Compare Similar Channels</h4>
                  <p className="text-xs text-muted-foreground">
                    Choose channels in the same niche or with similar content for meaningful comparisons.
                  </p>
                </div>
                <div className="bg-card p-3 rounded border">
                  <h4 className="font-medium text-sm">Look for Trends</h4>
                  <p className="text-xs text-muted-foreground">
                    Focus on growth patterns and engagement trends rather than just absolute numbers.
                  </p>
                </div>
                <div className="bg-card p-3 rounded border">
                  <h4 className="font-medium text-sm">Consider Context</h4>
                  <p className="text-xs text-muted-foreground">
                    Factor in channel age, content type, and target audience when analyzing metrics.
                  </p>
                </div>
              </div>
            </div>
          </div>

          <div className="bg-gradient-to-r from-primary/10 to-primary/5 p-6 rounded-lg border border-primary/20">
            <h3 className="text-lg font-semibold mb-2">💡 Pro Tip</h3>
            <p className="text-muted-foreground">
              Use channel comparison data to identify content gaps, optimal posting strategies, and successful
              formats in your niche. Look for patterns in high-performing channels and adapt their strategies
              to your unique brand and audience.
            </p>
          </div>
        </div>
      </main>
    </div>
  )
}
