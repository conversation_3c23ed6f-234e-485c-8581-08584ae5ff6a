"use client"

import { useState } from "react"
import Link from "next/link"
import Image from "next/image"
import { format } from "date-fns"
import { ArrowUp, ArrowDown, Minus, Filter, Star, Calendar, ThumbsUp, BarChart2, Search } from "lucide-react"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Badge } from "@/components/ui/badge"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
  DropdownMenuCheckboxItem,
} from "@/components/ui/dropdown-menu"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>ontent, <PERSON>ltipProvider, TooltipTrigger } from "@/components/ui/tooltip"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { Calendar as CalendarComponent } from "@/components/ui/calendar"
import { ScrollArea } from "@/components/ui/scroll-area"
import { MainNav } from "@/components/main-nav"
import { UserNav } from "@/components/user-nav"
import { cn, formatNumber } from "@/lib/utils"

// 示例数据 - 频道排行榜
const topChannelsData = [
  {
    id: "1",
    rank: 1,
    rankChange: 0,
    name: "MrBeast",
    avatar: "/placeholder.svg?height=40&width=40",
    category: "娱乐",
    subscribers: 245000000,
    subscribersGained: 1200000,
    views: 56000000000,
    viewsGained: 320000000,
    videos: 742,
    isFavorite: true,
    isVerified: true,
    isKidsFriendly: false,
    isMusic: false,
  },
  {
    id: "2",
    rank: 2,
    rankChange: 1,
    name: "T-Series",
    avatar: "/placeholder.svg?height=40&width=40",
    category: "音乐",
    subscribers: 238000000,
    subscribersGained: 800000,
    views: 215000000000,
    viewsGained: 280000000,
    videos: 18542,
    isFavorite: false,
    isVerified: true,
    isKidsFriendly: false,
    isMusic: true,
  },
  {
    id: "3",
    rank: 3,
    rankChange: -1,
    name: "Cocomelon",
    avatar: "/placeholder.svg?height=40&width=40",
    category: "儿童",
    subscribers: 162000000,
    subscribersGained: 600000,
    views: 185000000000,
    viewsGained: 250000000,
    videos: 942,
    isFavorite: false,
    isVerified: true,
    isKidsFriendly: true,
    isMusic: false,
  },
  {
    id: "4",
    rank: 4,
    rankChange: 0,
    name: "SET India",
    avatar: "/placeholder.svg?height=40&width=40",
    category: "娱乐",
    subscribers: 158000000,
    subscribersGained: 550000,
    views: 145000000000,
    viewsGained: 220000000,
    videos: 32654,
    isFavorite: false,
    isVerified: true,
    isKidsFriendly: false,
    isMusic: false,
  },
  {
    id: "5",
    rank: 5,
    rankChange: 0,
    name: "PewDiePie",
    avatar: "/placeholder.svg?height=40&width=40",
    category: "游戏",
    subscribers: 111000000,
    subscribersGained: 320000,
    views: 28000000000,
    viewsGained: 150000000,
    videos: 4582,
    isFavorite: true,
    isVerified: true,
    isKidsFriendly: false,
    isMusic: false,
  },
  {
    id: "6",
    rank: 6,
    rankChange: 2,
    name: "Kids Diana Show",
    avatar: "/placeholder.svg?height=40&width=40",
    category: "儿童",
    subscribers: 109000000,
    subscribersGained: 450000,
    views: 95000000000,
    viewsGained: 180000000,
    videos: 1254,
    isFavorite: false,
    isVerified: true,
    isKidsFriendly: true,
    isMusic: false,
  },
  {
    id: "7",
    rank: 7,
    rankChange: -1,
    name: "Like Nastya",
    avatar: "/placeholder.svg?height=40&width=40",
    category: "儿童",
    subscribers: 105000000,
    subscribersGained: 420000,
    views: 92000000000,
    viewsGained: 170000000,
    videos: 1087,
    isFavorite: false,
    isVerified: true,
    isKidsFriendly: true,
    isMusic: false,
  },
  {
    id: "8",
    rank: 8,
    rankChange: -1,
    name: "WWE",
    avatar: "/placeholder.svg?height=40&width=40",
    category: "体育",
    subscribers: 98000000,
    subscribersGained: 380000,
    views: 78000000000,
    viewsGained: *********,
    videos: 65421,
    isFavorite: false,
    isVerified: true,
    isKidsFriendly: false,
    isMusic: false,
  },
  {
    id: "9",
    rank: 9,
    rankChange: 0,
    name: "Zee Music Company",
    avatar: "/placeholder.svg?height=40&width=40",
    category: "音乐",
    subscribers: 93000000,
    subscribersGained: 350000,
    views: 65000000000,
    viewsGained: *********,
    videos: 8754,
    isFavorite: false,
    isVerified: true,
    isKidsFriendly: false,
    isMusic: true,
  },
  {
    id: "10",
    rank: 10,
    rankChange: 0,
    name: "BLACKPINK",
    avatar: "/placeholder.svg?height=40&width=40",
    category: "音乐",
    subscribers: 89000000,
    subscribersGained: 320000,
    views: 32000000000,
    viewsGained: *********,
    videos: 542,
    isFavorite: true,
    isVerified: true,
    isKidsFriendly: false,
    isMusic: true,
  },
]

// 示例数据 - 视频排行榜
const topVideosData = [
  {
    id: "v1",
    rank: 1,
    rankChange: 0,
    title: "Baby Shark Dance",
    thumbnail: "/placeholder.svg?height=90&width=160",
    channel: "Pinkfong",
    channelAvatar: "/placeholder.svg?height=24&width=24",
    views: 13500000000,
    viewsGained: 15000000,
    likes: 45000000,
    comments: 8500000,
    duration: "2:16",
    publishDate: "2016-06-17",
    isFavorite: true,
    isKidsFriendly: true,
    isMusic: true,
  },
  {
    id: "v2",
    rank: 2,
    rankChange: 0,
    title: "Despacito",
    thumbnail: "/placeholder.svg?height=90&width=160",
    channel: "Luis Fonsi",
    channelAvatar: "/placeholder.svg?height=24&width=24",
    views: 8200000000,
    viewsGained: 8000000,
    likes: 52000000,
    comments: 7200000,
    duration: "4:41",
    publishDate: "2017-01-12",
    isFavorite: false,
    isKidsFriendly: false,
    isMusic: true,
  },
  {
    id: "v3",
    rank: 3,
    rankChange: 1,
    title: "Johny Johny Yes Papa",
    thumbnail: "/placeholder.svg?height=90&width=160",
    channel: "LooLoo Kids",
    channelAvatar: "/placeholder.svg?height=24&width=24",
    views: 6800000000,
    viewsGained: 7500000,
    likes: 18000000,
    comments: 2500000,
    duration: "1:58",
    publishDate: "2016-10-08",
    isFavorite: false,
    isKidsFriendly: true,
    isMusic: false,
  },
  {
    id: "v4",
    rank: 4,
    rankChange: -1,
    title: "Shape of You",
    thumbnail: "/placeholder.svg?height=90&width=160",
    channel: "Ed Sheeran",
    channelAvatar: "/placeholder.svg?height=24&width=24",
    views: 6000000000,
    viewsGained: 6000000,
    likes: 32000000,
    comments: 4800000,
    duration: "4:24",
    publishDate: "2017-01-30",
    isFavorite: true,
    isKidsFriendly: false,
    isMusic: true,
  },
  {
    id: "v5",
    rank: 5,
    rankChange: 0,
    title: "See You Again",
    thumbnail: "/placeholder.svg?height=90&width=160",
    channel: "Wiz Khalifa",
    channelAvatar: "/placeholder.svg?height=24&width=24",
    views: 5800000000,
    viewsGained: 5500000,
    likes: 38000000,
    comments: 5200000,
    duration: "3:58",
    publishDate: "2015-04-06",
    isFavorite: false,
    isKidsFriendly: false,
    isMusic: true,
  },
]

export default function RankingsPage() {
  // 状态管理
  const [activeTab, setActiveTab] = useState("channels")
  const [dateRange, setDateRange] = useState<{
    from: Date | undefined
    to: Date | undefined
  }>({
    from: new Date(new Date().setDate(new Date().getDate() - 7)),
    to: new Date(),
  })
  const [favorites, setFavorites] = useState<string[]>(
    topChannelsData.filter((channel) => channel.isFavorite).map((channel) => channel.id),
  )
  const [videoFavorites, setVideoFavorites] = useState<string[]>(
    topVideosData.filter((video) => video.isFavorite).map((video) => video.id),
  )

  // 筛选状态
  const [filters, setFilters] = useState({
    includeKids: true,
    includeMusic: true,
    includeMovies: true,
  })

  // 排序选项
  const [sortBy, setSortBy] = useState("subscribers")
  const [timeFrame, setTimeFrame] = useState("last7d")

  // 处理收藏切换
  const toggleFavorite = (id: string, type: "channel" | "video") => {
    if (type === "channel") {
      setFavorites((prev) => (prev.includes(id) ? prev.filter((item) => item !== id) : [...prev, id]))
    } else {
      setVideoFavorites((prev) => (prev.includes(id) ? prev.filter((item) => item !== id) : [...prev, id]))
    }
  }

  // 渲染排名变化指示器
  const renderRankChange = (change: number) => {
    if (change > 0) {
      return (
        <div className="flex items-center text-emerald-500">
          <ArrowUp className="h-3 w-3 mr-1" />
          {change}
        </div>
      )
    } else if (change < 0) {
      return (
        <div className="flex items-center text-red-500">
          <ArrowDown className="h-3 w-3 mr-1" />
          {Math.abs(change)}
        </div>
      )
    } else {
      return (
        <div className="flex items-center text-slate-400">
          <Minus className="h-3 w-3" />
        </div>
      )
    }
  }

  return (
    <div className="flex min-h-screen flex-col">
      <header className="sticky top-0 z-40 border-b bg-background">
        <div className="container flex h-16 items-center justify-between py-4">
          <MainNav />
          <div className="flex items-center gap-4">
            <div className="relative w-64">
              <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                type="search"
                placeholder="搜索频道或视频..."
                className="w-full rounded-lg pl-8 md:w-[300px] lg:w-[400px]"
              />
            </div>
            <UserNav />
          </div>
        </div>
      </header>
      <div className="container flex-1 space-y-4 p-8 pt-6">
        {/* Header with Educational Content */}
        <div className="mb-8">
          <h2 className="text-3xl md:text-4xl font-bold tracking-tight mb-4">YouTube Rankings & Leaderboards</h2>
          <p className="text-lg text-muted-foreground mb-6 max-w-3xl">
            Discover the top-performing YouTube channels and videos across different categories.
            Analyze trending content, track performance changes, and gain insights from the most successful creators.
          </p>

          {/* Key Features */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
            <div className="bg-card p-4 rounded-lg border text-center">
              <div className="text-2xl font-bold text-primary">100+</div>
              <div className="text-sm text-muted-foreground">Top Channels Tracked</div>
            </div>
            <div className="bg-card p-4 rounded-lg border text-center">
              <div className="text-2xl font-bold text-primary">Real-time</div>
              <div className="text-sm text-muted-foreground">Data Updates</div>
            </div>
            <div className="bg-card p-4 rounded-lg border text-center">
              <div className="text-2xl font-bold text-primary">Multiple</div>
              <div className="text-sm text-muted-foreground">Categories</div>
            </div>
            <div className="bg-card p-4 rounded-lg border text-center">
              <div className="text-2xl font-bold text-primary">Historical</div>
              <div className="text-sm text-muted-foreground">Trend Analysis</div>
            </div>
          </div>
        </div>

        <div className="flex items-center justify-between space-y-2">
          <div className="flex items-center space-x-2">
            <Popover>
              <PopoverTrigger asChild>
                <Button variant="outline" className="flex items-center gap-2">
                  <Calendar className="h-4 w-4" />
                  {dateRange.from ? (
                    dateRange.to ? (
                      <>
                        {format(dateRange.from, "yyyy-MM-dd")} - {format(dateRange.to, "yyyy-MM-dd")}
                      </>
                    ) : (
                      format(dateRange.from, "yyyy-MM-dd")
                    )
                  ) : (
                    "选择日期范围"
                  )}
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-auto p-0" align="end">
                <CalendarComponent
                  initialFocus
                  mode="range"
                  defaultMonth={dateRange.from}
                  selected={dateRange}
                  onSelect={setDateRange}
                  numberOfMonths={2}
                />
              </PopoverContent>
            </Popover>
          </div>
        </div>
        <Tabs defaultValue="channels" onValueChange={setActiveTab}>
          <div className="flex items-center justify-between">
            <TabsList>
              <TabsTrigger value="channels">频道排行榜</TabsTrigger>
              <TabsTrigger value="videos">视频排行榜</TabsTrigger>
            </TabsList>
            <div className="flex items-center gap-2">
              <Select value={timeFrame} onValueChange={setTimeFrame}>
                <SelectTrigger className="w-[180px]">
                  <SelectValue placeholder="选择时间范围" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="yesterday">昨日</SelectItem>
                  <SelectItem value="last7d">过去7天</SelectItem>
                  <SelectItem value="last28d">过去28天</SelectItem>
                  <SelectItem value="last3m">过去3个月</SelectItem>
                  <SelectItem value="last6m">过去6个月</SelectItem>
                  <SelectItem value="lasty">过去一年</SelectItem>
                  <SelectItem value="alltime">所有时间</SelectItem>
                </SelectContent>
              </Select>
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="outline" className="flex items-center gap-2">
                    <Filter className="h-4 w-4" />
                    筛选
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end" className="w-56">
                  <DropdownMenuLabel>筛选选项</DropdownMenuLabel>
                  <DropdownMenuSeparator />
                  <DropdownMenuCheckboxItem
                    checked={filters.includeKids}
                    onCheckedChange={(checked) => setFilters({ ...filters, includeKids: checked })}
                  >
                    包含儿童频道
                  </DropdownMenuCheckboxItem>
                  <DropdownMenuCheckboxItem
                    checked={filters.includeMusic}
                    onCheckedChange={(checked) => setFilters({ ...filters, includeMusic: checked })}
                  >
                    包含音乐频道
                  </DropdownMenuCheckboxItem>
                  <DropdownMenuCheckboxItem
                    checked={filters.includeMovies}
                    onCheckedChange={(checked) => setFilters({ ...filters, includeMovies: checked })}
                  >
                    包含电影频道
                  </DropdownMenuCheckboxItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          </div>
          <TabsContent value="channels" className="mt-6">
            <Card>
              <CardHeader className="pb-3">
                <div className="flex items-center justify-between">
                  <CardTitle>Top 100 订阅量频道</CardTitle>
                  <div className="flex items-center gap-2">
                    <Select value={sortBy} onValueChange={setSortBy}>
                      <SelectTrigger className="w-[180px]">
                        <SelectValue placeholder="排序方式" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="subscribers">按订阅量</SelectItem>
                        <SelectItem value="subscribersGained">按新增订阅</SelectItem>
                        <SelectItem value="views">按总观看量</SelectItem>
                        <SelectItem value="viewsGained">按新增观看</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
                <div className="flex flex-wrap gap-2 mt-2">
                  {filters.includeKids && (
                    <Badge variant="outline" className="flex items-center gap-1">
                      包含儿童频道
                      <button
                        onClick={() => setFilters({ ...filters, includeKids: false })}
                        className="ml-1 rounded-full hover:bg-slate-100"
                      >
                        ✕
                      </button>
                    </Badge>
                  )}
                  {filters.includeMusic && (
                    <Badge variant="outline" className="flex items-center gap-1">
                      包含音乐频道
                      <button
                        onClick={() => setFilters({ ...filters, includeMusic: false })}
                        className="ml-1 rounded-full hover:bg-slate-100"
                      >
                        ✕
                      </button>
                    </Badge>
                  )}
                  {filters.includeMovies && (
                    <Badge variant="outline" className="flex items-center gap-1">
                      包含电影频道
                      <button
                        onClick={() => setFilters({ ...filters, includeMovies: false })}
                        className="ml-1 rounded-full hover:bg-slate-100"
                      >
                        ✕
                      </button>
                    </Badge>
                  )}
                </div>
              </CardHeader>
              <CardContent>
                <div className="rounded-md border">
                  <div className="grid grid-cols-12 bg-slate-50 p-4 text-sm font-medium text-slate-500">
                    <div className="col-span-1 flex items-center">排名</div>
                    <div className="col-span-4">频道</div>
                    <div className="col-span-2 text-right">订阅量</div>
                    <div className="col-span-2 text-right">新增订阅</div>
                    <div className="col-span-2 text-right">总观看量</div>
                    <div className="col-span-1 text-center">操作</div>
                  </div>
                  <ScrollArea className="h-[600px]">
                    {topChannelsData
                      .filter((channel) => {
                        if (!filters.includeKids && channel.isKidsFriendly) return false
                        if (!filters.includeMusic && channel.isMusic) return false
                        return true
                      })
                      .map((channel) => (
                        <div key={channel.id} className="grid grid-cols-12 items-center border-t p-4 hover:bg-slate-50">
                          <div className="col-span-1 flex items-center gap-2">
                            <span className="font-medium">{channel.rank}</span>
                            {renderRankChange(channel.rankChange)}
                          </div>
                          <div className="col-span-4">
                            <div className="flex items-center gap-3">
                              <Avatar className="h-10 w-10">
                                <AvatarImage src={channel.avatar || "/placeholder.svg"} alt={channel.name} />
                                <AvatarFallback>{channel.name.charAt(0)}</AvatarFallback>
                              </Avatar>
                              <div>
                                <div className="flex items-center gap-1">
                                  <Link href={`/channel/${channel.name}`} className="font-medium hover:underline">
                                    {channel.name}
                                  </Link>
                                  {channel.isVerified && (
                                    <TooltipProvider>
                                      <Tooltip>
                                        <TooltipTrigger>
                                          <Badge
                                            variant="outline"
                                            className="h-5 w-5 p-0 flex items-center justify-center"
                                          >
                                            ✓
                                          </Badge>
                                        </TooltipTrigger>
                                        <TooltipContent>
                                          <p>已验证频道</p>
                                        </TooltipContent>
                                      </Tooltip>
                                    </TooltipProvider>
                                  )}
                                </div>
                                <div className="text-sm text-slate-500">{channel.category}</div>
                              </div>
                            </div>
                          </div>
                          <div className="col-span-2 text-right font-medium">{formatNumber(channel.subscribers)}</div>
                          <div className="col-span-2 text-right font-medium text-emerald-600">
                            +{formatNumber(channel.subscribersGained)}
                          </div>
                          <div className="col-span-2 text-right font-medium">{formatNumber(channel.views)}</div>
                          <div className="col-span-1 flex justify-center gap-2">
                            <Button
                              variant="ghost"
                              size="icon"
                              onClick={() => toggleFavorite(channel.id, "channel")}
                              className={favorites.includes(channel.id) ? "text-yellow-500" : "text-slate-400"}
                            >
                              {favorites.includes(channel.id) ? (
                                <Star className="h-5 w-5 fill-yellow-500" />
                              ) : (
                                <Star className="h-5 w-5" />
                              )}
                            </Button>
                            <Button variant="ghost" size="icon" className="text-slate-400" asChild>
                              <Link href={`/channel/${channel.name}`}>
                                <BarChart2 className="h-5 w-5" />
                              </Link>
                            </Button>
                          </div>
                        </div>
                      ))}
                  </ScrollArea>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
          <TabsContent value="videos" className="mt-6">
            <Card>
              <CardHeader className="pb-3">
                <div className="flex items-center justify-between">
                  <CardTitle>Top 100 观看量视频</CardTitle>
                  <div className="flex items-center gap-2">
                    <Select value={sortBy} onValueChange={setSortBy}>
                      <SelectTrigger className="w-[180px]">
                        <SelectValue placeholder="排序方式" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="views">按总观看量</SelectItem>
                        <SelectItem value="viewsGained">按新增观看</SelectItem>
                        <SelectItem value="likes">按点赞数</SelectItem>
                        <SelectItem value="comments">按评论数</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
                <div className="flex flex-wrap gap-2 mt-2">
                  {filters.includeKids && (
                    <Badge variant="outline" className="flex items-center gap-1">
                      包含儿童视频
                      <button
                        onClick={() => setFilters({ ...filters, includeKids: false })}
                        className="ml-1 rounded-full hover:bg-slate-100"
                      >
                        ✕
                      </button>
                    </Badge>
                  )}
                  {filters.includeMusic && (
                    <Badge variant="outline" className="flex items-center gap-1">
                      包含音乐视频
                      <button
                        onClick={() => setFilters({ ...filters, includeMusic: false })}
                        className="ml-1 rounded-full hover:bg-slate-100"
                      >
                        ✕
                      </button>
                    </Badge>
                  )}
                </div>
              </CardHeader>
              <CardContent>
                <div className="rounded-md border">
                  <div className="grid grid-cols-12 bg-slate-50 p-4 text-sm font-medium text-slate-500">
                    <div className="col-span-1 flex items-center">排名</div>
                    <div className="col-span-5">视频</div>
                    <div className="col-span-2 text-right">观看量</div>
                    <div className="col-span-2 text-right">新增观看</div>
                    <div className="col-span-1 text-right">互动</div>
                    <div className="col-span-1 text-center">操作</div>
                  </div>
                  <ScrollArea className="h-[600px]">
                    {topVideosData
                      .filter((video) => {
                        if (!filters.includeKids && video.isKidsFriendly) return false
                        if (!filters.includeMusic && video.isMusic) return false
                        return true
                      })
                      .map((video) => (
                        <div key={video.id} className="grid grid-cols-12 items-center border-t p-4 hover:bg-slate-50">
                          <div className="col-span-1 flex items-center gap-2">
                            <span className="font-medium">{video.rank}</span>
                            {renderRankChange(video.rankChange)}
                          </div>
                          <div className="col-span-5">
                            <div className="flex gap-3">
                              <div className="relative h-[90px] w-[160px] flex-shrink-0 overflow-hidden rounded-md">
                                <Image
                                  src={video.thumbnail || "/placeholder.svg"}
                                  alt={video.title}
                                  fill
                                  className="object-cover"
                                />
                                <div className="absolute bottom-1 right-1 bg-black/70 text-white text-xs px-1 py-0.5 rounded">
                                  {video.duration}
                                </div>
                              </div>
                              <div className="flex flex-col justify-between">
                                <div>
                                  <div className="font-medium line-clamp-2 hover:underline">{video.title}</div>
                                  <div className="mt-1 flex items-center text-sm text-slate-500">
                                    <Avatar className="h-5 w-5 mr-1">
                                      <AvatarImage
                                        src={video.channelAvatar || "/placeholder.svg"}
                                        alt={video.channel}
                                      />
                                      <AvatarFallback>{video.channel.charAt(0)}</AvatarFallback>
                                    </Avatar>
                                    <span>{video.channel}</span>
                                  </div>
                                </div>
                                <div className="text-xs text-slate-500">发布于 {video.publishDate}</div>
                              </div>
                            </div>
                          </div>
                          <div className="col-span-2 text-right font-medium">{formatNumber(video.views)}</div>
                          <div className="col-span-2 text-right font-medium text-emerald-600">
                            +{formatNumber(video.viewsGained)}
                          </div>
                          <div className="col-span-1 text-right">
                            <div className="flex items-center justify-end gap-1 text-sm">
                              <ThumbsUp className="h-3.5 w-3.5 text-slate-500" />
                              <span>{formatNumber(video.likes)}</span>
                            </div>
                          </div>
                          <div className="col-span-1 flex justify-center gap-2">
                            <Button
                              variant="ghost"
                              size="icon"
                              onClick={() => toggleFavorite(video.id, "video")}
                              className={videoFavorites.includes(video.id) ? "text-yellow-500" : "text-slate-400"}
                            >
                              {videoFavorites.includes(video.id) ? (
                                <Star className="h-5 w-5 fill-yellow-500" />
                              ) : (
                                <Star className="h-5 w-5" />
                              )}
                            </Button>
                            <Button variant="ghost" size="icon" className="text-slate-400">
                              <BarChart2 className="h-5 w-5" />
                            </Button>
                          </div>
                        </div>
                      ))}
                  </ScrollArea>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>

        {/* Educational Content Section */}
        <div className="mt-16 space-y-8">
          <div>
            <h2 className="text-2xl font-bold mb-6">Understanding YouTube Rankings</h2>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
              <div>
                <h3 className="text-lg font-semibold mb-4">How Rankings Are Calculated</h3>
                <div className="space-y-4">
                  <div className="border-l-4 border-blue-500 pl-4">
                    <h4 className="font-medium">Subscriber Count</h4>
                    <p className="text-sm text-muted-foreground">
                      Total number of subscribers, indicating channel reach and audience size.
                    </p>
                  </div>
                  <div className="border-l-4 border-green-500 pl-4">
                    <h4 className="font-medium">Growth Rate</h4>
                    <p className="text-sm text-muted-foreground">
                      Percentage increase in subscribers and views over selected time periods.
                    </p>
                  </div>
                  <div className="border-l-4 border-purple-500 pl-4">
                    <h4 className="font-medium">Engagement Metrics</h4>
                    <p className="text-sm text-muted-foreground">
                      Likes, comments, and shares relative to view count and subscriber base.
                    </p>
                  </div>
                  <div className="border-l-4 border-orange-500 pl-4">
                    <h4 className="font-medium">Content Consistency</h4>
                    <p className="text-sm text-muted-foreground">
                      Regular posting schedule and sustained audience engagement over time.
                    </p>
                  </div>
                </div>
              </div>

              <div>
                <h3 className="text-lg font-semibold mb-4">Using Rankings for Strategy</h3>
                <div className="space-y-3">
                  <div className="bg-card p-4 rounded border">
                    <h4 className="font-medium text-sm mb-2">🎯 Competitive Analysis</h4>
                    <p className="text-xs text-muted-foreground">
                      Study top-ranking channels in your niche to understand successful content strategies and formats.
                    </p>
                  </div>
                  <div className="bg-card p-4 rounded border">
                    <h4 className="font-medium text-sm mb-2">📈 Growth Benchmarking</h4>
                    <p className="text-xs text-muted-foreground">
                      Compare your channel's performance against similar-sized channels to set realistic growth goals.
                    </p>
                  </div>
                  <div className="bg-card p-4 rounded border">
                    <h4 className="font-medium text-sm mb-2">🔍 Trend Identification</h4>
                    <p className="text-xs text-muted-foreground">
                      Identify emerging trends by monitoring which channels are rapidly climbing the rankings.
                    </p>
                  </div>
                  <div className="bg-card p-4 rounded border">
                    <h4 className="font-medium text-sm mb-2">💡 Content Inspiration</h4>
                    <p className="text-xs text-muted-foreground">
                      Discover successful content types and topics by analyzing top-performing videos.
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div className="bg-gradient-to-r from-primary/10 to-primary/5 p-6 rounded-lg border border-primary/20">
            <h3 className="text-lg font-semibold mb-3">📊 Ranking Methodology</h3>
            <p className="text-muted-foreground mb-4">
              Our rankings are updated daily using real-time data from YouTube's API. We track multiple metrics
              including subscriber count, view count, engagement rates, and growth velocity to provide the most
              accurate and comprehensive rankings in the industry.
            </p>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
              <div className="text-center">
                <div className="font-semibold text-primary">Real-time Data</div>
                <div className="text-muted-foreground">Updated every hour</div>
              </div>
              <div className="text-center">
                <div className="font-semibold text-primary">Multiple Metrics</div>
                <div className="text-muted-foreground">Comprehensive analysis</div>
              </div>
              <div className="text-center">
                <div className="font-semibold text-primary">Historical Tracking</div>
                <div className="text-muted-foreground">Trend analysis</div>
              </div>
            </div>
          </div>

          <div>
            <h3 className="text-lg font-semibold mb-4">Frequently Asked Questions</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-4">
                <div>
                  <h4 className="font-medium mb-2">How often are rankings updated?</h4>
                  <p className="text-sm text-muted-foreground">
                    Rankings are updated every hour to ensure you have access to the most current data and trends.
                  </p>
                </div>
                <div>
                  <h4 className="font-medium mb-2">What categories are included?</h4>
                  <p className="text-sm text-muted-foreground">
                    We track channels across all major categories including Gaming, Music, Entertainment, Education,
                    Tech, and more.
                  </p>
                </div>
              </div>
              <div className="space-y-4">
                <div>
                  <h4 className="font-medium mb-2">Can I track my own channel?</h4>
                  <p className="text-sm text-muted-foreground">
                    Yes! Use our channel search feature to find and track your channel's ranking position and
                    performance over time.
                  </p>
                </div>
                <div>
                  <h4 className="font-medium mb-2">How accurate is the data?</h4>
                  <p className="text-sm text-muted-foreground">
                    Our data comes directly from YouTube's official API, ensuring high accuracy and reliability
                    for all metrics and rankings.
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
