"use client"

import { useState } from "react"
import Link from "next/link"
import Image from "next/image"
import { Star, Bar<PERSON>hart2, Search, Trash2, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON> } from "lucide-react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle, CardFooter } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Badge } from "@/components/ui/badge"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { ScrollArea } from "@/components/ui/scroll-area"
import { MainNav } from "@/components/main-nav"
import { UserNav } from "@/components/user-nav"
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog"
import { cn, formatNumber } from "@/lib/utils"

// 示例数据 - 收藏的频道
const favoriteChannelsData = [
  {
    id: "1",
    name: "MrBeast",
    avatar: "/placeholder.svg?height=40&width=40",
    category: "娱乐",
    subscribers: 245000000,
    subscribersGained: 1200000,
    views: 56000000000,
    viewsGained: 320000000,
    videos: 742,
    isVerified: true,
    lastUpdated: "2023-07-15",
  },
  {
    id: "5",
    name: "PewDiePie",
    avatar: "/placeholder.svg?height=40&width=40",
    category: "游戏",
    subscribers: 111000000,
    subscribersGained: 320000,
    views: 28000000000,
    viewsGained: 150000000,
    videos: 4582,
    isVerified: true,
    lastUpdated: "2023-07-12",
  },
  {
    id: "10",
    name: "BLACKPINK",
    avatar: "/placeholder.svg?height=40&width=40",
    category: "音乐",
    subscribers: 89000000,
    subscribersGained: 320000,
    views: 32000000000,
    viewsGained: 130000000,
    videos: 542,
    isVerified: true,
    lastUpdated: "2023-07-10",
  },
]

// 示例数据 - 收藏的视频
const favoriteVideosData = [
  {
    id: "v1",
    title: "Baby Shark Dance",
    thumbnail: "/placeholder.svg?height=90&width=160",
    channel: "Pinkfong",
    channelAvatar: "/placeholder.svg?height=24&width=24",
    views: 13500000000,
    viewsGained: 15000000,
    likes: 45000000,
    comments: 8500000,
    duration: "2:16",
    publishDate: "2016-06-17",
    lastUpdated: "2023-07-15",
  },
  {
    id: "v4",
    title: "Shape of You",
    thumbnail: "/placeholder.svg?height=90&width=160",
    channel: "Ed Sheeran",
    channelAvatar: "/placeholder.svg?height=24&width=24",
    views: 6000000000,
    viewsGained: 6000000,
    likes: 32000000,
    comments: 4800000,
    duration: "4:24",
    publishDate: "2017-01-30",
    lastUpdated: "2023-07-14",
  },
]

// 示例数据 - 收藏的报表
const favoriteReportsData = [
  {
    id: "r1",
    title: "2023年Q2频道表现分析",
    description: "分析了频道在2023年第二季度的表现，包括观看量、订阅增长和收益数据",
    type: "channel",
    channelName: "MrBeast",
    channelAvatar: "/placeholder.svg?height=24&width=24",
    createdAt: "2023-07-01",
    lastUpdated: "2023-07-15",
    chartType: "line",
  },
  {
    id: "r2",
    title: "游戏内容观众分析",
    description: "分析了游戏内容的观众年龄分布、地理位置和设备使用情况",
    type: "audience",
    channelName: "PewDiePie",
    channelAvatar: "/placeholder.svg?height=24&width=24",
    createdAt: "2023-06-15",
    lastUpdated: "2023-07-10",
    chartType: "pie",
  },
  {
    id: "r3",
    title: "音乐视频收益对比",
    description: "比较了不同音乐视频的收益表现和观众互动情况",
    type: "revenue",
    channelName: "BLACKPINK",
    channelAvatar: "/placeholder.svg?height=24&width=24",
    createdAt: "2023-06-20",
    lastUpdated: "2023-07-12",
    chartType: "bar",
  },
]

export default function FavoritesPage() {
  // 状态管理
  const [activeTab, setActiveTab] = useState("channels")
  const [viewMode, setViewMode] = useState<"grid" | "list">("grid")
  const [sortBy, setSortBy] = useState("recent")
  const [searchTerm, setSearchTerm] = useState("")
  const [favorites, setFavorites] = useState(favoriteChannelsData)
  const [favoriteVideos, setFavoriteVideos] = useState(favoriteVideosData)
  const [favoriteReports, setFavoriteReports] = useState(favoriteReportsData)

  // 移除收藏
  const removeFromFavorites = (id: string, type: "channel" | "video" | "report") => {
    if (type === "channel") {
      setFavorites((prev) => prev.filter((item) => item.id !== id))
    } else if (type === "video") {
      setFavoriteVideos((prev) => prev.filter((item) => item.id !== id))
    } else {
      setFavoriteReports((prev) => prev.filter((item) => item.id !== id))
    }
  }

  // 获取图表类型图标
  const getChartIcon = (type: string) => {
    switch (type) {
      case "line":
        return <LineChart className="h-5 w-5" />
      case "bar":
        return <BarChart className="h-5 w-5" />
      case "pie":
        return <PieChart className="h-5 w-5" />
      default:
        return <BarChart2 className="h-5 w-5" />
    }
  }

  // 过滤搜索结果
  const filteredChannels = favorites.filter((channel) => channel.name.toLowerCase().includes(searchTerm.toLowerCase()))

  const filteredVideos = favoriteVideos.filter(
    (video) =>
      video.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
      video.channel.toLowerCase().includes(searchTerm.toLowerCase()),
  )

  const filteredReports = favoriteReports.filter(
    (report) =>
      report.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
      report.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
      report.channelName.toLowerCase().includes(searchTerm.toLowerCase()),
  )

  return (
    <div className="flex min-h-screen flex-col">
      <header className="sticky top-0 z-40 border-b bg-background">
        <div className="container flex h-16 items-center justify-between py-4">
          <MainNav />
          <div className="flex items-center gap-4">
            <div className="relative w-64">
              <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                type="search"
                placeholder="搜索收藏..."
                className="w-full rounded-lg pl-8 md:w-[300px] lg:w-[400px]"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>
            <UserNav />
          </div>
        </div>
      </header>
      <div className="container flex-1 space-y-4 p-8 pt-6">
        <div className="flex items-center justify-between space-y-2">
          <h2 className="text-3xl font-bold tracking-tight">收藏夹</h2>
          <div className="flex items-center space-x-2">
            <div className="flex border rounded-md overflow-hidden">
              <Button
                variant={viewMode === "grid" ? "default" : "ghost"}
                size="sm"
                onClick={() => setViewMode("grid")}
                className="rounded-none"
              >
                <Grid className="h-4 w-4" />
              </Button>
              <Button
                variant={viewMode === "list" ? "default" : "ghost"}
                size="sm"
                onClick={() => setViewMode("list")}
                className="rounded-none"
              >
                <List className="h-4 w-4" />
              </Button>
            </div>
            <Select value={sortBy} onValueChange={setSortBy}>
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder="排序方式" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="recent">最近更新</SelectItem>
                <SelectItem value="name">名称</SelectItem>
                <SelectItem value="subscribers">订阅量</SelectItem>
                <SelectItem value="views">观看量</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>
        <Tabs defaultValue="channels" onValueChange={setActiveTab}>
          <TabsList>
            <TabsTrigger value="channels">收藏的频道</TabsTrigger>
            <TabsTrigger value="videos">收藏的视频</TabsTrigger>
            <TabsTrigger value="reports">收藏的报表</TabsTrigger>
          </TabsList>
          <TabsContent value="channels" className="mt-6">
            {filteredChannels.length > 0 ? (
              viewMode === "grid" ? (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  {filteredChannels.map((channel) => (
                    <Card key={channel.id} className="overflow-hidden">
                      <CardHeader className="p-0">
                        <div className="relative h-32 bg-slate-100">
                          <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent" />
                          <div className="absolute bottom-4 left-4 flex items-center gap-3">
                            <Avatar className="h-12 w-12 border-2 border-white">
                              <AvatarImage src={channel.avatar || "/placeholder.svg"} alt={channel.name} />
                              <AvatarFallback>{channel.name.charAt(0)}</AvatarFallback>
                            </Avatar>
                            <div>
                              <div className="flex items-center gap-1">
                                <h3 className="font-bold text-white">{channel.name}</h3>
                                {channel.isVerified && (
                                  <Badge
                                    variant="outline"
                                    className="h-5 w-5 p-0 flex items-center justify-center bg-white/20 text-white border-none"
                                  >
                                    ✓
                                  </Badge>
                                )}
                              </div>
                              <p className="text-xs text-white/80">{channel.category}</p>
                            </div>
                          </div>
                        </div>
                      </CardHeader>
                      <CardContent className="p-4">
                        <div className="grid grid-cols-2 gap-4 mb-4">
                          <div>
                            <p className="text-xs text-slate-500">订阅量</p>
                            <p className="font-medium">{formatNumber(channel.subscribers)}</p>
                          </div>
                          <div>
                            <p className="text-xs text-slate-500">观看量</p>
                            <p className="font-medium">{formatNumber(channel.views)}</p>
                          </div>
                          <div>
                            <p className="text-xs text-slate-500">视频数</p>
                            <p className="font-medium">{formatNumber(channel.videos)}</p>
                          </div>
                          <div>
                            <p className="text-xs text-slate-500">更新时间</p>
                            <p className="font-medium">{channel.lastUpdated}</p>
                          </div>
                        </div>
                      </CardContent>
                      <CardFooter className="flex justify-between p-4 pt-0 border-t">
                        <Button variant="outline" size="sm" asChild>
                          <Link href={`/channel/${channel.name}`}>查看分析</Link>
                        </Button>
                        <AlertDialog>
                          <AlertDialogTrigger asChild>
                            <Button variant="ghost" size="sm" className="text-red-500">
                              <Trash2 className="h-4 w-4 mr-1" />
                              移除
                            </Button>
                          </AlertDialogTrigger>
                          <AlertDialogContent>
                            <AlertDialogHeader>
                              <AlertDialogTitle>确认移除</AlertDialogTitle>
                              <AlertDialogDescription>
                                确定要将 {channel.name} 从收藏夹中移除吗？此操作无法撤销。
                              </AlertDialogDescription>
                            </AlertDialogHeader>
                            <AlertDialogFooter>
                              <AlertDialogCancel>取消</AlertDialogCancel>
                              <AlertDialogAction
                                onClick={() => removeFromFavorites(channel.id, "channel")}
                                className="bg-red-500 hover:bg-red-600"
                              >
                                移除
                              </AlertDialogAction>
                            </AlertDialogFooter>
                          </AlertDialogContent>
                        </AlertDialog>
                      </CardFooter>
                    </Card>
                  ))}
                </div>
              ) : (
                <Card>
                  <CardContent className="p-0">
                    <div className="rounded-md border">
                      <div className="grid grid-cols-12 bg-slate-50 p-4 text-sm font-medium text-slate-500">
                        <div className="col-span-4">频道</div>
                        <div className="col-span-2 text-right">订阅量</div>
                        <div className="col-span-2 text-right">观看量</div>
                        <div className="col-span-2 text-right">视频数</div>
                        <div className="col-span-2 text-center">操作</div>
                      </div>
                      <ScrollArea className="h-[600px]">
                        {filteredChannels.map((channel) => (
                          <div
                            key={channel.id}
                            className="grid grid-cols-12 items-center border-t p-4 hover:bg-slate-50"
                          >
                            <div className="col-span-4">
                              <div className="flex items-center gap-3">
                                <Avatar className="h-10 w-10">
                                  <AvatarImage src={channel.avatar || "/placeholder.svg"} alt={channel.name} />
                                  <AvatarFallback>{channel.name.charAt(0)}</AvatarFallback>
                                </Avatar>
                                <div>
                                  <div className="flex items-center gap-1">
                                    <Link href={`/channel/${channel.name}`} className="font-medium hover:underline">
                                      {channel.name}
                                    </Link>
                                    {channel.isVerified && (
                                      <Badge variant="outline" className="h-5 w-5 p-0 flex items-center justify-center">
                                        ✓
                                      </Badge>
                                    )}
                                  </div>
                                  <div className="text-sm text-slate-500">{channel.category}</div>
                                </div>
                              </div>
                            </div>
                            <div className="col-span-2 text-right font-medium">
                              {formatNumber(channel.subscribers)}
                            </div>
                            <div className="col-span-2 text-right font-medium">
                              {formatNumber(channel.views)}
                            </div>
                            <div className="col-span-2 text-right font-medium">{formatNumber(channel.videos)}</div>
                            <div className="col-span-2 flex justify-center gap-2">
                              <Button variant="ghost" size="sm" className="h-8" asChild>
                                <Link href={`/channel/${channel.name}`}>查看</Link>
                              </Button>
                              <AlertDialog>
                                <AlertDialogTrigger asChild>
                                  <Button variant="ghost" size="sm" className="h-8 text-red-500">
                                    移除
                                  </Button>
                                </AlertDialogTrigger>
                                <AlertDialogContent>
                                  <AlertDialogHeader>
                                    <AlertDialogTitle>确认移除</AlertDialogTitle>
                                    <AlertDialogDescription>
                                      确定要将 {channel.name} 从收藏夹中移除吗？此操作无法撤销。
                                    </AlertDialogDescription>
                                  </AlertDialogHeader>
                                  <AlertDialogFooter>
                                    <AlertDialogCancel>取消</AlertDialogCancel>
                                    <AlertDialogAction
                                      onClick={() => removeFromFavorites(channel.id, "channel")}
                                      className="bg-red-500 hover:bg-red-600"
                                    >
                                      移除
                                    </AlertDialogAction>
                                  </AlertDialogFooter>
                                </AlertDialogContent>
                              </AlertDialog>
                            </div>
                          </div>
                        ))}
                      </ScrollArea>
                    </div>
                  </CardContent>
                </Card>
              )
            ) : (
              <div className="flex flex-col items-center justify-center py-12 text-center">
                <Star className="h-12 w-12 text-slate-300 mb-4" />
                <h3 className="text-lg font-medium">没有收藏的频道</h3>
                <p className="text-slate-500 mb-4 max-w-md">您还没有收藏任何频道，或者没有符合搜索条件的频道。</p>
                <Button asChild>
                  <Link href="/rankings">浏览排行榜</Link>
                </Button>
              </div>
            )}
          </TabsContent>
          <TabsContent value="videos" className="mt-6">
            {filteredVideos.length > 0 ? (
              viewMode === "grid" ? (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  {filteredVideos.map((video) => (
                    <Card key={video.id} className="overflow-hidden">
                      <CardHeader className="p-0">
                        <div className="relative aspect-video">
                          <Image
                            src={video.thumbnail || "/placeholder.svg"}
                            alt={video.title}
                            fill
                            className="object-cover"
                          />
                          <div className="absolute bottom-2 right-2 bg-black/70 text-white text-xs px-1.5 py-0.5 rounded">
                            {video.duration}
                          </div>
                        </div>
                      </CardHeader>
                      <CardContent className="p-4">
                        <h3 className="font-medium line-clamp-2 mb-2">{video.title}</h3>
                        <div className="flex items-center text-sm text-slate-500 mb-4">
                          <Avatar className="h-5 w-5 mr-1">
                            <AvatarImage src={video.channelAvatar || "/placeholder.svg"} alt={video.channel} />
                            <AvatarFallback>{video.channel.charAt(0)}</AvatarFallback>
                          </Avatar>
                          <span>{video.channel}</span>
                        </div>
                        <div className="grid grid-cols-2 gap-4">
                          <div>
                            <p className="text-xs text-slate-500">观看量</p>
                            <p className="font-medium">{formatNumber(video.views)}</p>
                          </div>
                          <div>
                            <p className="text-xs text-slate-500">点赞数</p>
                            <p className="font-medium">{formatNumber(video.likes)}</p>
                          </div>
                          <div>
                            <p className="text-xs text-slate-500">发布日期</p>
                            <p className="font-medium">{video.publishDate}</p>
                          </div>
                          <div>
                            <p className="text-xs text-slate-500">更新时间</p>
                            <p className="font-medium">{video.lastUpdated}</p>
                          </div>
                        </div>
                      </CardContent>
                      <CardFooter className="flex justify-between p-4 pt-0 border-t">
                        <Button variant="outline" size="sm">
                          查看分析
                        </Button>
                        <AlertDialog>
                          <AlertDialogTrigger asChild>
                            <Button variant="ghost" size="sm" className="text-red-500">
                              <Trash2 className="h-4 w-4 mr-1" />
                              移除
                            </Button>
                          </AlertDialogTrigger>
                          <AlertDialogContent>
                            <AlertDialogHeader>
                              <AlertDialogTitle>确认移除</AlertDialogTitle>
                              <AlertDialogDescription>
                                确定要将此视频从收藏夹中移除吗？此操作无法撤销。
                              </AlertDialogDescription>
                            </AlertDialogHeader>
                            <AlertDialogFooter>
                              <AlertDialogCancel>取消</AlertDialogCancel>
                              <AlertDialogAction
                                onClick={() => removeFromFavorites(video.id, "video")}
                                className="bg-red-500 hover:bg-red-600"
                              >
                                移除
                              </AlertDialogAction>
                            </AlertDialogFooter>
                          </AlertDialogContent>
                        </AlertDialog>
                      </CardFooter>
                    </Card>
                  ))}
                </div>
              ) : (
                <Card>
                  <CardContent className="p-0">
                    <div className="rounded-md border">
                      <div className="grid grid-cols-12 bg-slate-50 p-4 text-sm font-medium text-slate-500">
                        <div className="col-span-5">视频</div>
                        <div className="col-span-2 text-right">观看量</div>
                        <div className="col-span-2 text-right">点赞数</div>
                        <div className="col-span-1 text-right">发布日期</div>
                        <div className="col-span-2 text-center">操作</div>
                      </div>
                      <ScrollArea className="h-[600px]">
                        {filteredVideos.map((video) => (
                          <div key={video.id} className="grid grid-cols-12 items-center border-t p-4 hover:bg-slate-50">
                            <div className="col-span-5">
                              <div className="flex gap-3">
                                <div className="relative h-[90px] w-[160px] flex-shrink-0 overflow-hidden rounded-md">
                                  <Image
                                    src={video.thumbnail || "/placeholder.svg"}
                                    alt={video.title}
                                    fill
                                    className="object-cover"
                                  />
                                  <div className="absolute bottom-1 right-1 bg-black/70 text-white text-xs px-1 py-0.5 rounded">
                                    {video.duration}
                                  </div>
                                </div>
                                <div className="flex flex-col justify-between">
                                  <div>
                                    <div className="font-medium line-clamp-2 hover:underline">{video.title}</div>
                                    <div className="mt-1 flex items-center text-sm text-slate-500">
                                      <Avatar className="h-5 w-5 mr-1">
                                        <AvatarImage
                                          src={video.channelAvatar || "/placeholder.svg"}
                                          alt={video.channel}
                                        />
                                        <AvatarFallback>{video.channel.charAt(0)}</AvatarFallback>
                                      </Avatar>
                                      <span>{video.channel}</span>
                                    </div>
                                  </div>
                                </div>
                              </div>
                            </div>
                            <div className="col-span-2 text-right font-medium">{formatNumber(video.views)}</div>
                            <div className="col-span-2 text-right font-medium">{formatNumber(video.likes)}</div>
                            <div className="col-span-1 text-right text-sm">{video.publishDate}</div>
                            <div className="col-span-2 flex justify-center gap-2">
                              <Button variant="ghost" size="sm" className="h-8">
                                查看
                              </Button>
                              <AlertDialog>
                                <AlertDialogTrigger asChild>
                                  <Button variant="ghost" size="sm" className="h-8 text-red-500">
                                    移除
                                  </Button>
                                </AlertDialogTrigger>
                                <AlertDialogContent>
                                  <AlertDialogHeader>
                                    <AlertDialogTitle>确认移除</AlertDialogTitle>
                                    <AlertDialogDescription>
                                      确定要将此视频从收藏夹中移除吗？此操作无法撤销。
                                    </AlertDialogDescription>
                                  </AlertDialogHeader>
                                  <AlertDialogFooter>
                                    <AlertDialogCancel>取消</AlertDialogCancel>
                                    <AlertDialogAction
                                      onClick={() => removeFromFavorites(video.id, "video")}
                                      className="bg-red-500 hover:bg-red-600"
                                    >
                                      移除
                                    </AlertDialogAction>
                                  </AlertDialogFooter>
                                </AlertDialogContent>
                              </AlertDialog>
                            </div>
                          </div>
                        ))}
                      </ScrollArea>
                    </div>
                  </CardContent>
                </Card>
              )
            ) : (
              <div className="flex flex-col items-center justify-center py-12 text-center">
                <Star className="h-12 w-12 text-slate-300 mb-4" />
                <h3 className="text-lg font-medium">没有收藏的视频</h3>
                <p className="text-slate-500 mb-4 max-w-md">您还没有收藏任何视频，或者没有符合搜索条件的视频。</p>
                <Button asChild>
                  <Link href="/rankings">浏览排行榜</Link>
                </Button>
              </div>
            )}
          </TabsContent>
          <TabsContent value="reports" className="mt-6">
            {filteredReports.length > 0 ? (
              viewMode === "grid" ? (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  {filteredReports.map((report) => (
                    <Card key={report.id} className="overflow-hidden">
                      <CardHeader className="pb-2">
                        <div className="flex justify-between items-start">
                          <div className="flex items-center gap-2">
                            {getChartIcon(report.chartType)}
                            <Badge variant="outline">{report.type}</Badge>
                          </div>
                          <AlertDialog>
                            <AlertDialogTrigger asChild>
                              <Button variant="ghost" size="icon" className="h-8 w-8 text-slate-400">
                                <Trash2 className="h-4 w-4" />
                              </Button>
                            </AlertDialogTrigger>
                            <AlertDialogContent>
                              <AlertDialogHeader>
                                <AlertDialogTitle>确认移除</AlertDialogTitle>
                                <AlertDialogDescription>
                                  确定要将此报表从收藏夹中移除吗？此操作无法撤销。
                                </AlertDialogDescription>
                              </AlertDialogHeader>
                              <AlertDialogFooter>
                                <AlertDialogCancel>取消</AlertDialogCancel>
                                <AlertDialogAction
                                  onClick={() => removeFromFavorites(report.id, "report")}
                                  className="bg-red-500 hover:bg-red-600"
                                >
                                  移除
                                </AlertDialogAction>
                              </AlertDialogFooter>
                            </AlertDialogContent>
                          </AlertDialog>
                        </div>
                        <CardTitle className="text-lg mt-2">{report.title}</CardTitle>
                        <CardDescription className="line-clamp-2">{report.description}</CardDescription>
                      </CardHeader>
                      <CardContent className="pb-2">
                        <div className="flex items-center gap-2 mb-4">
                          <Avatar className="h-6 w-6">
                            <AvatarImage src={report.channelAvatar || "/placeholder.svg"} alt={report.channelName} />
                            <AvatarFallback>{report.channelName.charAt(0)}</AvatarFallback>
                          </Avatar>
                          <span className="text-sm">{report.channelName}</span>
                        </div>
                        <div className="grid grid-cols-2 gap-4">
                          <div>
                            <p className="text-xs text-slate-500">创建日期</p>
                            <p className="text-sm">{report.createdAt}</p>
                          </div>
                          <div>
                            <p className="text-xs text-slate-500">更新时间</p>
                            <p className="text-sm">{report.lastUpdated}</p>
                          </div>
                        </div>
                      </CardContent>
                      <CardFooter className="pt-2">
                        <Button variant="outline" size="sm" className="w-full">
                          查看报表
                        </Button>
                      </CardFooter>
                    </Card>
                  ))}
                </div>
              ) : (
                <Card>
                  <CardContent className="p-0">
                    <div className="rounded-md border">
                      <div className="grid grid-cols-12 bg-slate-50 p-4 text-sm font-medium text-slate-500">
                        <div className="col-span-4">报表</div>
                        <div className="col-span-2">类型</div>
                        <div className="col-span-2">频道</div>
                        <div className="col-span-2">创建日期</div>
                        <div className="col-span-2 text-center">操作</div>
                      </div>
                      <ScrollArea className="h-[600px]">
                        {filteredReports.map((report) => (
                          <div
                            key={report.id}
                            className="grid grid-cols-12 items-center border-t p-4 hover:bg-slate-50"
                          >
                            <div className="col-span-4">
                              <div className="flex items-center gap-2">
                                {getChartIcon(report.chartType)}
                                <div>
                                  <div className="font-medium">{report.title}</div>
                                  <div className="text-xs text-slate-500 line-clamp-1">{report.description}</div>
                                </div>
                              </div>
                            </div>
                            <div className="col-span-2">
                              <Badge variant="outline">{report.type}</Badge>
                            </div>
                            <div className="col-span-2">
                              <div className="flex items-center gap-2">
                                <Avatar className="h-5 w-5">
                                  <AvatarImage
                                    src={report.channelAvatar || "/placeholder.svg"}
                                    alt={report.channelName}
                                  />
                                  <AvatarFallback>{report.channelName.charAt(0)}</AvatarFallback>
                                </Avatar>
                                <span className="text-sm">{report.channelName}</span>
                              </div>
                            </div>
                            <div className="col-span-2 text-sm">{report.createdAt}</div>
                            <div className="col-span-2 flex justify-center gap-2">
                              <Button variant="ghost" size="sm" className="h-8">
                                查看
                              </Button>
                              <AlertDialog>
                                <AlertDialogTrigger asChild>
                                  <Button variant="ghost" size="sm" className="h-8 text-red-500">
                                    移除
                                  </Button>
                                </AlertDialogTrigger>
                                <AlertDialogContent>
                                  <AlertDialogHeader>
                                    <AlertDialogTitle>确认移除</AlertDialogTitle>
                                    <AlertDialogDescription>
                                      确定要将此报表从收藏夹中移除吗？此操作无法撤销。
                                    </AlertDialogDescription>
                                  </AlertDialogHeader>
                                  <AlertDialogFooter>
                                    <AlertDialogCancel>取消</AlertDialogCancel>
                                    <AlertDialogAction
                                      onClick={() => removeFromFavorites(report.id, "report")}
                                      className="bg-red-500 hover:bg-red-600"
                                    >
                                      移除
                                    </AlertDialogAction>
                                  </AlertDialogFooter>
                                </AlertDialogContent>
                              </AlertDialog>
                            </div>
                          </div>
                        ))}
                      </ScrollArea>
                    </div>
                  </CardContent>
                </Card>
              )
            ) : (
              <div className="flex flex-col items-center justify-center py-12 text-center">
                <Star className="h-12 w-12 text-slate-300 mb-4" />
                <h3 className="text-lg font-medium">没有收藏的报表</h3>
                <p className="text-slate-500 mb-4 max-w-md">您还没有收藏任何报表，或者没有符合搜索条件的报表。</p>
                <Button>创建新报表</Button>
              </div>
            )}
          </TabsContent>
        </Tabs>
      </div>
    </div>
  )
}
