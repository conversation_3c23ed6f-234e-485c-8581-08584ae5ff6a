"use client"

import React, { useEffect, useState, useMemo } from "react";
import { useSearch<PERSON><PERSON><PERSON>, useRouter } from "next/navigation";
import { useSession } from "next-auth/react";
import Link from "next/link"
import Image from "next/image"
import {
  Calendar,
  Search,
  ChevronDown,
  ArrowUpRight,
  Clock,
  DollarSign,
  Eye,
  ThumbsUp,
  MessageSquare,
  Download,
  Users,
  Play,
  ChevronRight,
  Filter,
  BarChart,
  TrendingUp,
  ArrowDownRight,
  FileText,
  Share2,
  Map,
  Globe,
  CalendarRange,
  ChevronLeft,
  FileSpreadsheet,
  FileIcon as FilePdf,
  FileJson,
  MoreHorizontal,
  BarChart2,
  LineChart,
  Layers,
  BrainCircuit,
  TrendingDown,
  AlertCircle,
  Database,
  Home,
  BarChart3,
  Settings,
  Bell,
  Menu,
  X,
} from "lucide-react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle, CardFooter } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Ava<PERSON>, Ava<PERSON><PERSON><PERSON>back, AvatarImage } from "@/components/ui/avatar"
import { Ta<PERSON>, Tabs<PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Badge } from "@/components/ui/badge"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { ChartContainer, ChartTooltip, ChartTooltipContent } from "@/components/ui/chart"
import {
  ResponsiveContainer,
  LineChart as RechartsLineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Legend,
  PieChart as RechartsPieChart,
  Pie,
  Cell,
  BarChart as RechartsBarChart,
  Bar,
} from "recharts"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { Calendar as CalendarComponent } from "@/components/ui/calendar"
import { Checkbox } from "@/components/ui/checkbox"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Sheet, SheetContent, SheetTrigger } from "@/components/ui/sheet"
import type { DateRange } from "react-day-picker"
import VideoList from "@/components/channel/VideoList";
import OverviewStats from "@/components/channel/OverviewStats";
import OverviewCharts from "@/components/channel/OverviewCharts";
import AboutTab from "@/components/channel/AboutTab";
import { AudienceAnalytics } from "@/components/channel/audience";
import { RevenueAnalytics } from "@/components/channel/revenue";
import { PredictionAnalytics } from "@/components/channel/prediction";
import { useTranslations } from 'next-intl';

// 默认缩略图路径
const defaultThumbnail = '/images/placeholder.jpg'

// 示例数据
const viewsData = [
  { date: "1月", views: 1200000, watchTime: 45000, revenue: 3200 },
  { date: "2月", views: 1350000, watchTime: 52000, revenue: 3800 },
  { date: "3月", views: 1100000, watchTime: 48000, revenue: 3400 },
  { date: "4月", views: 1450000, watchTime: 56000, revenue: 4100 },
  { date: "5月", views: 1600000, watchTime: 62000, revenue: 4500 },
  { date: "6月", views: 1800000, watchTime: 70000, revenue: 5200 },
  { date: "7月", views: 2100000, watchTime: 85000, revenue: 6100 },
]

// 频道比较数据
const comparisonChannels = [
  { id: "TechChannel", name: "TechChannel", subscribers: 2450000, views: 128500000, videos: 342 },
  { id: "CodeMaster", name: "CodeMaster", subscribers: 1850000, views: 95300000, videos: 278 },
  { id: "WebDevPro", name: "WebDevPro", subscribers: 1250000, views: 68700000, videos: 215 },
  { id: "ProgrammingHub", name: "ProgrammingHub", subscribers: 3200000, views: 156800000, videos: 412 },
  { id: "DevTutorials", name: "DevTutorials", subscribers: 980000, views: 42500000, videos: 186 },
]

const comparisonViewsData = [
  { month: "1月", TechChannel: 1200000, CodeMaster: 950000, WebDevPro: 720000 },
  { month: "2月", TechChannel: 1350000, CodeMaster: 1050000, WebDevPro: 780000 },
  { month: "3月", TechChannel: 1100000, CodeMaster: 980000, WebDevPro: 750000 },
  { month: "4月", TechChannel: 1450000, CodeMaster: 1120000, WebDevPro: 820000 },
  { month: "5月", TechChannel: 1600000, CodeMaster: 1250000, WebDevPro: 880000 },
  { month: "6月", TechChannel: 1800000, CodeMaster: 1380000, WebDevPro: 950000 },
  { month: "7月", TechChannel: 2100000, CodeMaster: 1580000, WebDevPro: 1050000 },
]

// Define an interface for your channel data
interface ChannelData {
  subscriberCount: number | null | undefined; // Allow null/undefined
  videoCount: number | null | undefined; // Allow null/undefined
  viewCount: number | null | undefined; // Allow null/undefined
  thumbnailUrl?: string;
  title?: string;
  isVerified?: boolean;
  publishedAt?: string;
  customUrl?: string;
  // Add other fields fetched from the API as needed
}

// 添加新的 VideoData 接口
interface VideoData {
  id: string;
  title: string;
  thumbnail: string | null;
  duration: string;
  publishedAt: string;
  viewCount: number;
  likeCount: number;
  commentCount: number;
  retention?: number;
  ctr?: number;
}

interface VideoResponse {
  data: VideoData[];
  pagination: {
    total: number;
    page: number;
    limit: number;
    totalPages: number;
  };
}

// API 响应接口
interface ChannelResponse {
  channel: ChannelData;
  videos: VideoData[];
}

// 添加图片加载状态类型
type ImageLoadState = 'loading' | 'loaded' | 'error'

interface ChannelClientProps {
  channelId: string;
}

export default function ChannelClient({ channelId }: ChannelClientProps) {
  const searchParams = useSearchParams();
  const router = useRouter();
  const { data: session, status } = useSession();
  const isAuthenticated = status === "authenticated";
  const [channelData, setChannelData] = useState<ChannelData | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [sidebarOpen, setSidebarOpen] = useState(true);
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);

  // 使用internationalization hooks
  const t = useTranslations('channel');
  const c = useTranslations('channel.client');

  // 检测是否为移动设备
  const [isMobile, setIsMobile] = useState(false);

  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 768);
      if (window.innerWidth < 768) {
        setSidebarOpen(false);
      }
    };

    // 初始检查
    checkMobile();

    // 监听窗口尺寸变化
    window.addEventListener('resize', checkMobile);

    // 清理函数
    return () => window.removeEventListener('resize', checkMobile);
  }, []);

  useEffect(() => {
    fetchChannelData();
  }, [channelId]);

  const fetchChannelData = async () => {
    try {
      setIsLoading(true);
      const response = await fetch(`/api/channels/${channelId}`);

      // 处理API返回的错误
      if (!response.ok) {
        if (response.status === 404) {
          console.error('频道不存在:', channelId);
          setIsLoading(false);
          setChannelData(null);
          return;
        }
        throw new Error(`请求失败: ${response.status} ${response.statusText}`);
      }

      const data: ChannelResponse = await response.json();
      setChannelData(data.channel);
      setIsLoading(false);
    } catch (error) {
      console.error('获取频道数据时出错:', error);
      setIsLoading(false);
      setChannelData(null);
    }
  };

  // 格式化数字为带千位分隔符的字符串
  const formatNumber = (num: number | null | undefined): string => {
    if (num === null || num === undefined) {
      return "0";
    }
    return new Intl.NumberFormat("zh-CN").format(num)
  }

  // 格式化数字为K, M单位
  const formatCompactNumber = (num: number | null | undefined): string => {
    if (num === null || num === undefined) {
      return "0";
    }
    if (num >= 1000000) {
      return (num / 1000000).toFixed(1) + "M"
    }
    if (num >= 1000) {
      return (num / 1000).toFixed(1) + "K"
    }
    return num.toString()
  }

  // Date range state
  const [dateRange, setDateRange] = useState<DateRange | undefined>({
    from: new Date(new Date().setMonth(new Date().getMonth() - 6)),
    to: new Date(),
  });

  // 频道比较状态
  const [showComparison, setShowComparison] = useState(false);
  const [selectedChannels, setSelectedChannels] = useState<string[]>([channelId]);

  const handleChannelSelect = (channelId: string) => {
    const params = new URLSearchParams(searchParams);
    params.set('channelId', channelId);
    router.push(`?${params.toString()}`);
  };

  const handleExport = (format: string) => {
    alert(`数据将以${format}格式导出`)
  }

  // 移动端侧边栏内容组件
  const SidebarContent = () => (
    <>
      <div className="p-4 flex items-center justify-between border-b border-border">
        {/* <div className="flex items-center">
          <Database className="h-6 w-6 text-emerald-400" />
          <span className="ml-2 font-bold">DataFlow</span>
        </div> */}
        {!isMobile && (
          <Button
            variant="ghost"
            size="icon"
            onClick={() => setSidebarOpen(!sidebarOpen)}
            className="text-muted-foreground hover:text-foreground hover:bg-muted"
          >
            <ChevronDown className={`h-5 w-5 transition-transform ${sidebarOpen ? "" : "rotate-180"}`} />
          </Button>
        )}
      </div>
      <div className="flex-1 py-4">
        <nav className="space-y-1 px-2">
          <Link
            href="/"
            className="flex items-center px-3 py-2 text-sm font-medium rounded-md text-slate-300 hover:bg-slate-800 hover:text-white"
          >
            <Home className={`${sidebarOpen || isMobile ? "mr-3" : "mx-auto"} h-5 w-5`} />
            {(sidebarOpen || isMobile) && <span>{t('home')}</span>}
          </Link>
          <Link
            href="/dashboard"
            className="flex items-center px-3 py-2 text-sm font-medium rounded-md text-slate-300 hover:bg-slate-800 hover:text-white"
          >
            <Database className={`${sidebarOpen || isMobile ? "mr-3" : "mx-auto"} h-5 w-5`} />
            {(sidebarOpen || isMobile) && <span>{t('dashboard')}</span>}
          </Link>
          <Link
            href="/tasks"
            className="flex items-center px-3 py-2 text-sm font-medium rounded-md text-slate-300 hover:bg-slate-800 hover:text-white"
          >
            <Database className={`${sidebarOpen || isMobile ? "mr-3" : "mx-auto"} h-5 w-5`} />
            {(sidebarOpen || isMobile) && <span>{c('dataTasks')}</span>}
          </Link>
          <Link
            href="/reports"
            className="flex items-center px-3 py-2 text-sm font-medium rounded-md text-slate-300 hover:bg-slate-800 hover:text-white"
          >
            <BarChart3 className={`${sidebarOpen || isMobile ? "mr-3" : "mx-auto"} h-5 w-5`} />
            {(sidebarOpen || isMobile) && <span>{c('reports')}</span>}
          </Link>
          <Link
            href="/settings"
            className="flex items-center px-3 py-2 text-sm font-medium rounded-md text-slate-300 hover:bg-slate-800 hover:text-white"
          >
            <Settings className={`${sidebarOpen || isMobile ? "mr-3" : "mx-auto"} h-5 w-5`} />
            {(sidebarOpen || isMobile) && <span>{t('settings')}</span>}
          </Link>
        </nav>
      </div>
      <div className="p-4 border-t border-border">
        {(sidebarOpen || isMobile) ? (
          <div className="flex items-center">
            <Avatar className="h-8 w-8">
              <AvatarImage src={channelData?.thumbnailUrl || "/images/placeholder.jpg"} />
              <AvatarFallback>JD</AvatarFallback>
            </Avatar>
            <div className="ml-3">
              <p className="text-sm font-medium">{session?.user?.name || "用户"}</p>
              <p className="text-xs text-muted-foreground">{c('admin')}</p>
            </div>
          </div>
        ) : (
          <Avatar className="h-8 w-8 mx-auto">
            <AvatarImage src={channelData?.thumbnailUrl || "/images/placeholder.jpg"} />
            <AvatarFallback>JD</AvatarFallback>
          </Avatar>
        )}
      </div>
    </>
  );

  // Handle loading and error states before rendering the main content
  if (isLoading) {
    return <div className="flex items-center justify-center h-screen">
      <div className="flex flex-col items-center">
        <div className="animate-spin h-10 w-10 border-4 border-primary border-t-transparent rounded-full mb-3"></div>
        <p className="text-muted-foreground">{c('loadingChannel')}</p>
      </div>
    </div>;
  }

  if (!channelData) {
   // This case might occur if loading finished but data is still null (e.g., API returned empty unexpectedly)
   return <div className="flex items-center justify-center h-screen">
     <div className="flex flex-col items-center text-center px-4">
       <AlertCircle className="h-10 w-10 text-destructive mb-3" />
       <p className="text-xl font-medium mb-2">{c('channelNotFound')}</p>
       <p className="text-muted-foreground mb-4">{c('checkChannelId')}</p>
       <Button onClick={() => router.push('/')} variant="outline">{c('returnHome')}</Button>
     </div>
   </div>;
  }

  return (
    <div className="flex h-screen bg-muted/40">
      {/* 隐藏侧边栏-后期授权油管在展示数据内容 */}
      {/* Desktop Sidebar */}
      {/* {isAuthenticated && !isMobile && (
        <div
          className={`bg-card text-card-foreground ${sidebarOpen ? "w-64" : "w-20"} transition-all duration-300 flex flex-col border-r border-border hidden md:flex`}
          >
          <SidebarContent />
        </div>
      )} */}

      {/* Mobile Sidebar */}
      {/* {isAuthenticated && isMobile && (
        <Sheet open={isMobileMenuOpen} onOpenChange={setIsMobileMenuOpen}>
          <SheetContent side="left" className="p-0 w-[80%] max-w-[300px] bg-card text-card-foreground flex flex-col">
            <SidebarContent />
          </SheetContent>
        </Sheet>
      )} */}

      {/* Main content */}
      <div className={`flex-1 flex flex-col overflow-hidden ${!isAuthenticated ? "ml-0" : ""}`}>
        {/* Mobile Header */}
        {/* {isMobile && (
          <header className="bg-card border-b border-border p-3 flex items-center justify-between sticky top-0 z-10">
            {isAuthenticated && (
              <Button variant="ghost" size="icon" onClick={() => setIsMobileMenuOpen(true)}>
                <Menu className="h-5 w-5" />
              </Button>
            )}
            <div className="flex items-center">
              <Database className="h-5 w-5 text-emerald-400 mr-2" />
              <span className="font-bold">DataFlow</span>
            </div>
            <Avatar className="h-7 w-7">
              <AvatarImage src={channelData?.thumbnailUrl || "/images/placeholder.jpg"} />
              <AvatarFallback>{channelId.charAt(0)}</AvatarFallback>
            </Avatar>
          </header>
        )} */}

        {/* Channel content */}
        <main className="flex-1 overflow-y-auto p-3 md:p-6 bg-background">
          {/* Channel Header */}
          <Card className="bg-card p-4 md:p-6 mb-4 md:mb-6">
            <div className="flex flex-col md:flex-row items-center md:items-center gap-4 md:gap-6">
              <Avatar className="h-16 w-16 md:h-20 md:w-20">
                 {/* Add channel avatar URL if available in channelData */}
                <AvatarImage src={channelData.thumbnailUrl || "/placeholder.svg"} />
                <AvatarFallback className="text-xl md:text-2xl">{channelId.charAt(0)}</AvatarFallback>
              </Avatar>

              <div className="flex-1 text-center md:text-left">
                <div className="flex flex-col md:flex-row md:items-center gap-2 md:gap-4 justify-center md:justify-start">
                  {/* Use channel title from data if available, otherwise fallback to ID */}
                  <Link
                    href={`https://www.youtube.com/${channelData.customUrl || channelId}`}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-xl md:text-2xl font-bold hover:text-primary transition-colors"
                  >
                    {channelData.title || channelId}
                  </Link>
                  {/* Conditionally render verified badge based on data */}
                  {channelData.isVerified && (
                    <Badge className="bg-emerald-100 text-emerald-800 hover:bg-emerald-100 w-fit mx-auto md:mx-0">{t('verified')}</Badge>
                  )}
                </div>
                <div>
                  <span className="text-muted-foreground text-sm ml-1">{channelData.customUrl}</span>
                </div>
                <div className="flex flex-wrap gap-4 mt-2 md:mt-3 justify-center md:justify-start">
                  <div className="flex items-center">
                    <Users className="h-4 w-4 md:h-5 md:w-5 text-muted-foreground mr-1.5" />
                    {/* Access data from state */}
                    <span className="font-medium text-sm md:text-base">{formatCompactNumber(channelData.subscriberCount)}</span>
                    <span className="text-muted-foreground text-sm ml-1">{t('subscribers')}</span>
                  </div>
                  <div className="flex items-center">
                    <Play className="h-4 w-4 md:h-5 md:w-5 text-muted-foreground mr-1.5" />
                     {/* Access data from state */}
                    <span className="font-medium text-sm md:text-base">{formatNumber(channelData.videoCount)}</span>
                    <span className="text-muted-foreground text-sm ml-1">{t('videos')}</span>
                  </div>
                  <div className="flex items-center">
                    <Eye className="h-4 w-4 md:h-5 md:w-5 text-muted-foreground mr-1.5" />
                     {/* Access data from state */}
                    <span className="font-medium text-sm md:text-base">{formatCompactNumber(channelData.viewCount)}</span>
                    <span className="text-muted-foreground text-sm ml-1">{c('totalViews')}</span>
                  </div>
                   {/* Display creation date if available */}
                   {channelData.publishedAt && (
                     <div className="flex items-center w-full md:w-auto justify-center md:justify-start mt-1 md:mt-0">
                       <Calendar className="h-4 w-4 md:h-5 md:w-5 text-muted-foreground mr-1.5" />
                       <span className="text-muted-foreground text-sm">{t('since')} {new Date(channelData.publishedAt).toLocaleDateString('zh-CN', { year: 'numeric', month: 'long' })}</span>
                     </div>
                   )}
                </div>
              </div>

            </div>
          </Card>

          {/* Tabs */}
          <Tabs defaultValue="videos">
            <TabsList className="mb-4 md:mb-6 w-full overflow-x-auto flex justify-start md:justify-left">
              <TabsTrigger value="overview" className="text-sm md:text-base">{t('overview')}</TabsTrigger>
              <TabsTrigger value="videos" className="text-sm md:text-base">{t('videos')}</TabsTrigger>
              <TabsTrigger value="about" className="text-sm md:text-base">{t('about')}</TabsTrigger>
              {/* <TabsTrigger value="audience" className="text-sm md:text-base">{t('audience')}</TabsTrigger> */}
              {/* <TabsTrigger value="revenue" className="text-sm md:text-base">{t('revenue')}</TabsTrigger> */}
              {/* <TabsTrigger value="prediction" className="text-sm md:text-base">{t('prediction')}</TabsTrigger> */}
            </TabsList>

            {/* 概览 */}
            <TabsContent value="overview" className="mt-0">
              <OverviewStats channelId={channelId} />
              <OverviewCharts channelId={channelId} />
            </TabsContent>

            {/* 视频列表 */}
            <TabsContent value="videos" className="mt-0">
              <VideoList channelId={channelId} />
            </TabsContent>

            {/* About Tab */}
            <TabsContent value="about" className="mt-0">
              <AboutTab channelId={channelId} />
            </TabsContent>

            <TabsContent value="audience" className="mt-0">
              <AudienceAnalytics />
            </TabsContent>

            <TabsContent value="revenue" className="mt-0">
              <RevenueAnalytics />
            </TabsContent>

            <TabsContent value="prediction" className="mt-0">
              <PredictionAnalytics />
            </TabsContent>
          </Tabs>
        </main>
      </div>
    </div>
  )
}