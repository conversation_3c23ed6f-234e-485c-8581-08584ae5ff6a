import Link from 'next/link';

/**
 * 频道不存在时显示的组件
 */
export default function NotFound() {
  return (
    <div className="min-h-screen flex flex-col items-center justify-center text-center px-4">
      <h1 className="text-6xl font-bold mb-4">404</h1>
      <h2 className="text-2xl font-semibold mb-6">频道未找到</h2>
      <p className="text-gray-600 mb-8 max-w-md">
        很抱歉，您访问的频道不存在或已被移除。
      </p>
      <Link
        href="/"
        className="px-6 py-3 bg-blue-600 hover:bg-blue-700 text-white rounded-md transition-colors"
      >
        返回首页
      </Link>
    </div>
  );
} 