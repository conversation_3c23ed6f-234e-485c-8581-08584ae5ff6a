import React from "react";
import ChannelClient from "./components/ChannelClient";
import { unstable_setRequestLocale } from 'next-intl/server';
import { notFound } from 'next/navigation';
import { prisma } from '@/lib/prisma';

type Props = {
  params: Promise<{
    channelId: string;
    locale: string;
  }>;
  searchParams: Record<string, string | string[] | undefined>;
}

export default async function ChannelPage({ params }: Props) {
  // 等待params解析完成
  const { channelId, locale } = await params;
  
  // 设置请求的语言环境
  unstable_setRequestLocale(locale);
  
  // 解码channelId
  const decodedChannelId = decodeURIComponent(channelId);
  
  // 在渲染页面之前，检查频道是否存在
  try {
    const channel = await prisma.ytbChannel.findUnique({
      where: {
        channelId: decodedChannelId,
      },
      select: { id: true }
    });
    
    // 如果找不到频道，触发not-found页面
    if (!channel) {
      console.log(`[Page] 找不到频道ID: ${decodedChannelId}，显示not-found页面`);
      notFound();
    }
  } catch (error) {
    console.error(`[Page] 查询频道时出错:`, error);
    // 出错时也显示not-found页面
    notFound();
  }
  
  return (
    <ChannelClient channelId={decodedChannelId} />
  );
}
