import { redirect } from 'next/navigation';
import ExternalChannelClient from './ExternalChannelClient';

interface ExternalChannelPageProps {
  params: {
    locale: string;
  };
  searchParams: {
    username?: string;
    hasExternalData?: string;
  };
}

export default async function ExternalChannelPage({ 
  params, 
  searchParams 
}: ExternalChannelPageProps) {
  // 使用await获取参数，避免Next.js错误
  const awaitedParams = await params;
  const awaitedSearchParams = await searchParams;
  
  const username = awaitedSearchParams.username;
  const hasExternalData = awaitedSearchParams.hasExternalData;
  const locale = awaitedParams.locale;
  
  // 如果URL中没有必要的参数，重定向到首页
  if (!username || hasExternalData !== 'true') {
    console.log('[页面] 缺少必要参数，重定向到首页');
    redirect(`/${locale}`);
  }

  // 渲染客户端组件，用于获取外部API数据并展示
  return (
    <div className="container mx-auto px-4 py-8">
      <ExternalChannelClient username={username} locale={locale} />
    </div>
  );
} 