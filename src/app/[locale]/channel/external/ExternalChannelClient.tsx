'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { Button } from "@/components/ui/button";
import { Skeleton } from "@/components/ui/skeleton";
import { ExclamationTriangleIcon, CheckCircledIcon, ClockIcon } from "@radix-ui/react-icons";
import { useTranslations } from 'next-intl';

interface ExternalChannelClientProps {
  username: string;
  locale: string;
}

interface Video {
  id: string;
  youtubeId: string;
  title: string;
  publishedAt: string;
  thumbnailUrl?: string;
  viewCount?: number;
}

interface ExternalChannelData {
  channelInfo?: {
    id?: string;
    title?: string;
    customUrl?: string;
    thumbnailUrl?: string;
    subscriberCount?: number;
  };
  videos?: Video[];
  error?: string;
  status?: string;
  task_id?: string;
  message?: string;
  channel_id?: string;
}

export default function ExternalChannelClient({ username, locale }: ExternalChannelClientProps) {
  const router = useRouter();
  const t = useTranslations('channel.externalChannel');
  const [channelData, setChannelData] = useState<ExternalChannelData>({});
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    async function fetchExternalData() {
      try {
        setLoading(true);
        setError(null);

        // 构建查询参数
        const channelUrl = username.startsWith('@') ? username : `@${username}`;
        console.log(`[客户端] 开始获取频道数据: ${channelUrl}`);
        
        // 调用内部API获取频道数据
        console.log(`[客户端] 请求 /api/external-channel-data 接口`);
        const response = await fetch('/api/external-channel-data', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ channel_url: channelUrl })
        });

        console.log(`[客户端] API 响应状态: ${response.status} ${response.statusText}`);

        if (!response.ok) {
          const errorData = await response.json();
          console.error(`[客户端] API 响应错误:`, errorData);
          throw new Error(errorData.error || `HTTP error! status: ${response.status}`);
        }

        const data = await response.json();
        console.log(`[客户端] 内部API数据获取成功`, { 
          channel_name: data.channel_name, 
          channel_id: data.channel_id,
          status: data.status,
          videos_count: data.videos?.length 
        });

        // 检查返回的状态是否为pending
        if (data.status === 'pending') {
          // 处理pending状态，保存关键信息供UI展示
          setChannelData({
            status: 'pending',
            task_id: data.task_id,
            message: data.message,
            channel_id: data.channel_id,
            channelInfo: {
              customUrl: channelUrl,
              title: channelUrl
            }
          });
          setLoading(false);
          return;
        }

        // 处理正常数据
        const formattedData: ExternalChannelData = {
          channelInfo: {
            id: data.channel_id || 'unknown',
            title: data.channel_name || channelUrl,
            customUrl: channelUrl,
            thumbnailUrl: data.channel_thumbnail,
            subscriberCount: data.subscriber_count
          },
          videos: data.videos?.map((video: any) => ({
            id: video.video_id,
            youtubeId: video.video_id,
            title: video.title,
            publishedAt: video.published_at,
            thumbnailUrl: video.thumbnail_url,
            viewCount: video.view_count
          })) || []
        };

        setChannelData(formattedData);
      } catch (err) {
        console.error('[客户端] 获取内部数据出错:', err);
        setError(err instanceof Error ? err.message : '未知错误');
        setChannelData({ error: '无法获取频道数据' });
      } finally {
        setLoading(false);
      }
    }

    fetchExternalData();
  }, [username]);

  const goToHomePage = () => {
    router.push(`/${locale}`);
  };

  // 检查频道状态并在数据可用时重定向
  const checkChannelStatus = async () => {
    try {
      setLoading(true);
      
      // 构建查询参数
      const channelUrl = username.startsWith('@') ? username : `@${username}`;
      console.log(`[客户端] 检查频道状态: ${channelUrl}`);
      
      // 查询频道是否已有数据
      const response = await fetch(`/api/channels/handle/${username.replace('@', '')}`);
      
      // 如果响应是重定向，表示频道数据已经准备好
      if (response.redirected) {
        // 跟随重定向到频道详情页
        console.log(`[客户端] 频道数据已就绪，重定向到: ${response.url}`);
        window.location.href = response.url;
        return;
      }
      
      // 否则重新获取当前状态
      console.log(`[客户端] 频道数据仍在处理中，刷新状态`);
      
      // 重新调用内部API获取当前状态
      const statusResponse = await fetch('/api/external-channel-data', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ channel_url: channelUrl })
      });
      
      if (!statusResponse.ok) {
        const errorData = await statusResponse.json();
        throw new Error(errorData.error || `HTTP error! status: ${statusResponse.status}`);
      }
      
      const data = await statusResponse.json();
      
      // 更新组件状态
      if (data.status === 'pending') {
        setChannelData({
          status: 'pending',
          task_id: data.task_id,
          message: data.message,
          channel_id: data.channel_id,
          channelInfo: {
            customUrl: channelUrl,
            title: channelUrl
          }
        });
      } else {
        // 处理正常数据
        const formattedData: ExternalChannelData = {
          channelInfo: {
            id: data.channel_id || 'unknown',
            title: data.channel_name || channelUrl,
            customUrl: channelUrl,
            thumbnailUrl: data.channel_thumbnail,
            subscriberCount: data.subscriber_count
          },
          videos: data.videos?.map((video: any) => ({
            id: video.video_id,
            youtubeId: video.video_id,
            title: video.title,
            publishedAt: video.published_at,
            thumbnailUrl: video.thumbnail_url,
            viewCount: video.view_count
          })) || []
        };

        setChannelData(formattedData);
      }
    } catch (err) {
      console.error('[客户端] 检查频道状态出错:', err);
      setError(err instanceof Error ? err.message : '未知错误');
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="space-y-4">
        <Card>
          <CardHeader>
            <Skeleton className="h-8 w-[250px]" />
            <Skeleton className="h-4 w-[200px]" />
          </CardHeader>
          <CardContent>
            <Skeleton className="h-4 w-full my-2" />
            <Skeleton className="h-4 w-full my-2" />
            <Skeleton className="h-4 w-3/4 my-2" />
          </CardContent>
        </Card>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mt-8">
          {[1, 2, 3, 4, 5, 6].map((item) => (
            <Card key={item}>
              <CardHeader className="p-0">
                <Skeleton className="h-[180px] w-full rounded-t-lg" />
              </CardHeader>
              <CardContent className="mt-4">
                <Skeleton className="h-4 w-full my-2" />
                <Skeleton className="h-4 w-2/3 my-2" />
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  // 处理API返回pending状态
  if (channelData.status === 'pending') {
    return (
      <Alert className="mb-6 bg-blue-50 border-blue-200">
        <ClockIcon className="h-5 w-5 text-blue-500" />
        <AlertTitle className="text-blue-700 text-lg">{t('status.processing')}</AlertTitle>
        <AlertDescription className="mt-4">
          <div className="space-y-4">
            <p className="text-blue-500">{t('messages.channelAdded', { channel: `@${username}` })}</p>
            <p className="text-sm text-gray-500">{t('status.taskId')}: {channelData.task_id}</p>
            {/* <p className="text-sm text-gray-500">{channelData.message}</p> */}
            <p className="text-sm text-gray-500">{t('status.message')}</p>
            <div className="mt-6 flex flex-col space-y-3 sm:flex-row sm:space-y-0 sm:space-x-4">
              <Button 
                onClick={goToHomePage} 
                variant="outline"
                className="border-blue-300 hover:bg-blue-50"
              >
                {t('buttons.backToHome')}
              </Button>
              <Button 
                onClick={checkChannelStatus} 
                className="bg-blue-600 hover:bg-blue-700"
              >
                {t('buttons.refresh')}
              </Button>
            </div>
            <p className="text-xs mt-4 text-gray-500">
              {t('status.processingTip')}
            </p>
          </div>
        </AlertDescription>
      </Alert>
    );
  }

  if (error || channelData.error) {
    return (
      <Alert variant="destructive" className="mb-6">
        <ExclamationTriangleIcon className="h-4 w-4" />
        <AlertTitle>{t('errors.fetchFailed')}</AlertTitle>
        <AlertDescription>
          <p>{t('errors.fetchFailedDesc', { channel: username })}</p>
          <p className="mt-2">{error || channelData.error}</p>
          <Button onClick={goToHomePage} className="mt-4">{t('buttons.backToHome')}</Button>
        </AlertDescription>
      </Alert>
    );
  }

  return (
    <div>
      <Alert className="mb-6">
        <CheckCircledIcon className="h-4 w-4" />
        <AlertTitle>{t('messages.internalData')}</AlertTitle>
        <AlertDescription>
          {t('messages.internalDataDesc')}
        </AlertDescription>
      </Alert>

      {/* 频道信息卡片 */}
      <Card className="mb-8">
        <CardHeader>
          <div className="flex items-center gap-4">
            {channelData.channelInfo?.thumbnailUrl ? (
              <img 
                src={channelData.channelInfo.thumbnailUrl} 
                alt={channelData.channelInfo.title || username}
                className="w-16 h-16 rounded-full object-cover"
              />
            ) : (
              <div className="w-16 h-16 rounded-full bg-gray-200 flex items-center justify-center">
                {username.charAt(0).toUpperCase()}
              </div>
            )}
            <div>
              <CardTitle>{channelData.channelInfo?.title || username}</CardTitle>
              <CardDescription>
                {channelData.channelInfo?.customUrl || `@${username}`}
                {channelData.channelInfo?.subscriberCount && (
                  <span className="ml-2">· {formatNumber(channelData.channelInfo.subscriberCount)} {t('subscribers')}</span>
                )}
              </CardDescription>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <p>{t('messages.internalDataDesc')}</p>
        </CardContent>
        <CardFooter>
          <Button variant="outline" onClick={goToHomePage}>{t('buttons.backToHome')}</Button>
        </CardFooter>
      </Card>

      {/* 视频网格 */}
      <h2 className="text-2xl font-bold mb-4">{t('messages.recentVideos')}</h2>
      {channelData.videos && channelData.videos.length > 0 ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {channelData.videos.map((video) => (
            <Card key={video.id} className="overflow-hidden">
              <div className="relative pb-[56.25%]">
                {video.thumbnailUrl ? (
                  <img 
                    src={video.thumbnailUrl} 
                    alt={video.title}
                    className="absolute top-0 left-0 w-full h-full object-cover"
                  />
                ) : (
                  <div className="absolute top-0 left-0 w-full h-full bg-gray-200 flex items-center justify-center">
                    {t('messages.noThumbnail')}
                  </div>
                )}
              </div>
              <CardContent className="p-4">
                <h3 className="font-medium line-clamp-2 h-12" title={video.title}>
                  <a 
                    href={`https://www.youtube.com/watch?v=${video.youtubeId}`}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="hover:text-blue-600 transition-colors"
                  >
                    {video.title}
                  </a>
                </h3>
                <div className="text-sm text-gray-500 mt-2 flex justify-between">
                  <span>{formatDate(video.publishedAt, locale)}</span>
                  {video.viewCount !== undefined && (
                    <span>{t('messages.views', { count: formatNumber(video.viewCount) })}</span>
                  )}
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      ) : (
        <Alert>
          <AlertTitle>{t('noVideos')}</AlertTitle>
          <AlertDescription>
            {t('videoNotFound')}
          </AlertDescription>
        </Alert>
      )}
    </div>
  );
}

// 格式化数字显示
function formatNumber(num: number): string {
  if (num >= 1000000) {
    return (num / 1000000).toFixed(1) + 'M';
  }
  if (num >= 1000) {
    return (num / 1000).toFixed(1) + 'K';
  }
  return num.toString();
}

// 格式化日期显示
function formatDate(dateString: string, locale: string = 'zh-CN'): string {
  try {
    const date = new Date(dateString);
    return date.toLocaleDateString(locale, {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  } catch (e) {
    return dateString;
  }
} 