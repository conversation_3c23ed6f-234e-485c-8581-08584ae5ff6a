"use client"

import { useState, useEffect } from "react"
import { useParams } from "next/navigation"
import Link from "next/link"
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { Separator } from "@/components/ui/separator"
import { 
  ArrowLeft, 
  Clock, 
  Download, 
  Star, 
  CheckCircle, 
  PlayCircle,
  FileText,
  Users,
  BarChart3,
  TrendingUp,
  Target,
  Lightbulb,
  AlertCircle,
  BookOpen
} from "lucide-react"

// Guide data structure
const guides = {
  "youtube-analytics-fundamentals": {
    id: "1",
    title: "YouTube Analytics Fundamentals: A Complete Beginner's Guide",
    description: "Master the basics of YouTube analytics with this comprehensive guide. Learn how to navigate YouTube Studio, understand key metrics, and make data-driven decisions for your channel growth.",
    category: "Analytics",
    difficulty: "Beginner",
    readTime: "25 min read",
    rating: 4.9,
    downloads: 15420,
    lastUpdated: "2024-01-15",
    topics: ["YouTube Studio", "Key Metrics", "Data Interpretation", "Performance Tracking"],
    content: {
      introduction: `Welcome to the complete beginner's guide to YouTube Analytics! Whether you're just starting your YouTube journey or looking to better understand your channel's performance, this guide will walk you through everything you need to know about YouTube's powerful analytics platform.

YouTube Analytics is your window into understanding how your content performs, who your audience is, and how you can improve your channel's growth. By the end of this guide, you'll be confident in navigating YouTube Studio and making data-driven decisions for your content strategy.`,
      
      sections: [
        {
          title: "Getting Started with YouTube Studio",
          content: `YouTube Studio is your command center for managing your channel and accessing analytics. Here's how to get started:

### Accessing YouTube Studio
1. Sign in to your YouTube account
2. Click on your profile picture in the top right corner
3. Select "YouTube Studio" from the dropdown menu
4. Navigate to the "Analytics" tab in the left sidebar

### Understanding the Dashboard
The Analytics dashboard provides an overview of your channel's performance with key metrics displayed prominently:
- **Overview**: Quick snapshot of recent performance
- **Reach**: How people discover your content
- **Engagement**: How viewers interact with your videos
- **Audience**: Who watches your content
- **Revenue**: Monetization performance (for eligible channels)

### Customizing Your View
You can customize the analytics view by:
- Selecting different time periods (last 7 days, 28 days, 90 days, etc.)
- Comparing periods to track growth
- Filtering by specific videos or playlists
- Switching between different metric views`,
          tips: [
            "Bookmark YouTube Studio for quick access",
            "Check your analytics at least weekly",
            "Use the mobile app for on-the-go monitoring"
          ]
        },
        {
          title: "Essential YouTube Metrics Explained",
          content: `Understanding key metrics is crucial for channel growth. Here are the most important ones:

### Views and Impressions
- **Views**: Number of times your videos have been watched
- **Impressions**: How often your thumbnails were shown to viewers
- **Click-through rate (CTR)**: Percentage of impressions that became views

### Watch Time Metrics
- **Watch time**: Total time viewers spent watching your videos
- **Average view duration**: How long viewers watch on average
- **Audience retention**: Percentage of video watched at each moment

### Engagement Metrics
- **Likes and dislikes**: Viewer feedback on your content
- **Comments**: Viewer engagement and community building
- **Shares**: How often viewers share your content
- **Subscribers gained/lost**: Channel growth tracking

### Traffic Sources
- **YouTube search**: Viewers finding you through search
- **Suggested videos**: YouTube recommending your content
- **External sources**: Traffic from other websites
- **Direct or unknown**: Viewers typing your URL directly`,
          examples: [
            {
              metric: "Click-through Rate",
              good: "4-10% for most channels",
              explanation: "Higher CTR means your thumbnails and titles are compelling"
            },
            {
              metric: "Average View Duration",
              good: "50%+ of total video length",
              explanation: "Shows your content keeps viewers engaged"
            }
          ]
        },
        {
          title: "Reading Your Analytics Reports",
          content: `Learning to interpret your analytics data is key to making informed decisions:

### The Overview Tab
Your overview provides a quick health check of your channel:
- Recent performance compared to previous periods
- Top-performing videos
- Key metrics at a glance
- Subscriber growth trends

### Reach Analytics
This section shows how people discover your content:
- **Impressions and CTR**: How appealing your thumbnails are
- **Traffic sources**: Where your views come from
- **Search terms**: What people search to find you

### Engagement Analytics
Understand how viewers interact with your content:
- **Watch time and retention**: How engaging your videos are
- **Likes, comments, shares**: Community engagement levels
- **End screen and card performance**: How well you guide viewers to more content

### Audience Analytics
Learn about your viewers:
- **Demographics**: Age, gender, location of your audience
- **When your viewers are online**: Best times to publish
- **Subscription source**: Which videos drive subscriptions
- **Returning vs new viewers**: Audience loyalty metrics`,
          actionItems: [
            "Check your top traffic sources weekly",
            "Identify your best-performing content themes",
            "Monitor audience retention graphs for drop-off points",
            "Track subscriber growth patterns"
          ]
        },
        {
          title: "Making Data-Driven Decisions",
          content: `Analytics are only valuable when you act on the insights:

### Content Strategy Optimization
Use your data to improve your content:
- **Analyze top performers**: What topics, formats, and lengths work best?
- **Study audience retention**: Where do viewers drop off?
- **Review traffic sources**: How can you optimize for your best sources?
- **Monitor trends**: What patterns do you see over time?

### Publishing Strategy
Optimize when and how you publish:
- **Best posting times**: When is your audience most active?
- **Frequency**: How often should you upload?
- **Seasonal patterns**: Are there times of year that perform better?

### Thumbnail and Title Testing
Use CTR data to improve discoverability:
- **A/B test thumbnails**: Try different styles and colors
- **Optimize titles**: Include keywords while staying compelling
- **Monitor performance**: Track how changes affect CTR

### Community Building
Use engagement data to build your community:
- **Respond to comments**: High engagement signals quality to YouTube
- **Create community posts**: Keep your audience engaged between videos
- **Collaborate**: Partner with creators who share your audience`,
          bestPractices: [
            "Set specific, measurable goals based on your analytics",
            "Make one change at a time to measure impact",
            "Document what works and what doesn't",
            "Be patient - changes take time to show results"
          ]
        }
      ],
      
      conclusion: `Congratulations! You now have a solid foundation in YouTube Analytics. Remember, analytics are a tool to guide your decisions, not dictate them. Use the data to understand your audience better and create content that serves them while achieving your goals.

The key to success is consistent monitoring, testing, and optimization. Start by focusing on one or two key metrics, then gradually expand your analysis as you become more comfortable with the platform.

Keep learning, keep experimenting, and most importantly, keep creating great content for your audience!`
    }
  },
  
  "advanced-youtube-seo-strategies": {
    id: "2",
    title: "Advanced YouTube SEO Strategies for 2024",
    description: "Take your YouTube SEO to the next level with advanced optimization techniques, keyword research strategies, and algorithm insights that top creators use to dominate search results.",
    category: "SEO",
    difficulty: "Advanced",
    readTime: "35 min read",
    rating: 4.8,
    downloads: 12350,
    lastUpdated: "2024-01-12",
    topics: ["Keyword Research", "Video Optimization", "Algorithm Updates", "Competitive Analysis"],
    content: {
      introduction: `Ready to master YouTube SEO? This advanced guide will teach you the sophisticated strategies that top creators use to dominate search results and grow their channels exponentially.

We'll dive deep into advanced keyword research, algorithm optimization, competitive analysis, and cutting-edge techniques that most creators don't know about. This isn't basic SEO - this is the advanced playbook for YouTube success.`,
      
      sections: [
        {
          title: "Advanced Keyword Research Strategies",
          content: `Beyond basic keyword tools, advanced SEO requires sophisticated research techniques:

### Semantic Keyword Mapping
Modern YouTube SEO isn't just about exact keywords - it's about semantic relationships:
- **Topic clusters**: Group related keywords around main themes
- **Intent mapping**: Understand what viewers really want
- **Latent semantic indexing**: Use related terms YouTube associates with your topic
- **Long-tail opportunities**: Find specific, low-competition phrases

### Competitive Keyword Analysis
Study your competitors' keyword strategies:
- **Reverse engineering**: Analyze top-ranking videos in your niche
- **Gap analysis**: Find keywords your competitors miss
- **Trend monitoring**: Spot emerging keywords before they become competitive
- **SERP analysis**: Understand what YouTube rewards for specific searches

### Advanced Keyword Tools and Techniques
Professional-level keyword research requires the right tools:
- **VidIQ and TubeBuddy**: For YouTube-specific data
- **Ahrefs Keywords Explorer**: For comprehensive keyword analysis
- **Google Trends**: For seasonal and trending insights
- **Answer The Public**: For question-based keywords
- **YouTube's own data**: Search suggestions, related videos, and comments`,
          
          strategies: [
            "Use keyword difficulty scores to find opportunities",
            "Analyze search volume trends over time",
            "Study competitor video performance patterns",
            "Map keywords to different stages of the viewer journey"
          ]
        },
        {
          title: "Algorithm Optimization Techniques",
          content: `Understanding and optimizing for YouTube's algorithm is crucial for advanced SEO:

### The YouTube Algorithm Factors
YouTube's algorithm considers multiple ranking factors:
- **Relevance**: How well your content matches search intent
- **Authority**: Your channel's expertise and trustworthiness
- **User signals**: Click-through rates, watch time, engagement
- **Freshness**: How recent and updated your content is
- **Personalization**: Individual viewer preferences and history

### Advanced On-Page Optimization
Optimize every element of your video for maximum SEO impact:
- **Title optimization**: Front-load keywords while maintaining appeal
- **Description strategy**: Use the first 125 characters effectively
- **Tag hierarchy**: Primary, secondary, and long-tail tag strategies
- **Thumbnail psychology**: Design for both CTR and brand consistency
- **End screens and cards**: Guide viewers to related content strategically

### Technical SEO Considerations
Advanced technical optimizations that most creators ignore:
- **Video file naming**: Use keyword-rich file names before upload
- **Closed captions**: Upload accurate transcripts for better indexing
- **Video chapters**: Structure content for better user experience
- **Playlist optimization**: Create themed playlists for topic authority
- **Channel structure**: Organize content for maximum discoverability`,
          
          tips: [
            "Test different title formulas for your niche",
            "Use analytics to identify your best-performing keywords",
            "Optimize for voice search with natural language",
            "Create content clusters around pillar topics"
          ]
        }
      ],
      
      conclusion: `Advanced YouTube SEO is about understanding the platform deeply and optimizing systematically. The strategies in this guide will help you compete with the biggest channels in your niche.

Remember, SEO is a long-term game. Implement these strategies consistently, measure your results, and continuously refine your approach. The creators who master these advanced techniques are the ones who dominate their niches.`
    }
  }
}

export default function GuideDetailPage() {
  const params = useParams()
  const slug = params.slug as string
  const [guide, setGuide] = useState<any>(null)
  const [currentSection, setCurrentSection] = useState(0)
  const [progress, setProgress] = useState(0)

  useEffect(() => {
    const foundGuide = guides[slug as keyof typeof guides]
    setGuide(foundGuide)
  }, [slug])

  useEffect(() => {
    if (guide) {
      const totalSections = guide.content.sections.length
      const progressPercentage = ((currentSection + 1) / totalSections) * 100
      setProgress(progressPercentage)
    }
  }, [currentSection, guide])

  if (!guide) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold mb-4">Guide Not Found</h1>
          <p className="text-muted-foreground mb-6">The guide you're looking for doesn't exist.</p>
          <Button asChild>
            <Link href="/guides">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Guides
            </Link>
          </Button>
        </div>
      </div>
    )
  }

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty.toLowerCase()) {
      case "beginner": return "bg-green-100 text-green-800"
      case "intermediate": return "bg-yellow-100 text-yellow-800"
      case "advanced": return "bg-red-100 text-red-800"
      default: return "bg-gray-100 text-gray-800"
    }
  }

  return (
    <div className="min-h-screen bg-background">
      <div className="container mx-auto px-4 py-8">
        {/* Back Button */}
        <div className="mb-8">
          <Button variant="ghost" asChild>
            <Link href="/guides">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Guides
            </Link>
          </Button>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
          {/* Main Content */}
          <div className="lg:col-span-3">
            {/* Guide Header */}
            <div className="mb-8">
              <div className="flex items-center gap-2 mb-4">
                <Badge variant="secondary">{guide.category}</Badge>
                <Badge className={getDifficultyColor(guide.difficulty)}>
                  {guide.difficulty}
                </Badge>
              </div>
              
              <h1 className="text-4xl md:text-5xl font-bold mb-4">{guide.title}</h1>
              <p className="text-xl text-muted-foreground mb-6">{guide.description}</p>
              
              <div className="flex items-center gap-6 text-sm text-muted-foreground mb-6">
                <div className="flex items-center gap-1">
                  <Clock className="h-4 w-4" />
                  {guide.readTime}
                </div>
                <div className="flex items-center gap-1">
                  <Star className="h-4 w-4 fill-yellow-400 text-yellow-400" />
                  {guide.rating}
                </div>
                <div className="flex items-center gap-1">
                  <Download className="h-4 w-4" />
                  {guide.downloads.toLocaleString()} downloads
                </div>
                <div className="flex items-center gap-1">
                  <Users className="h-4 w-4" />
                  Last updated: {guide.lastUpdated}
                </div>
              </div>

              {/* Progress Bar */}
              <div className="mb-6">
                <div className="flex items-center justify-between mb-2">
                  <span className="text-sm font-medium">Progress</span>
                  <span className="text-sm text-muted-foreground">{Math.round(progress)}% complete</span>
                </div>
                <Progress value={progress} className="h-2" />
              </div>
            </div>

            {/* Introduction */}
            <Card className="mb-8">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <BookOpen className="h-5 w-5" />
                  Introduction
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-muted-foreground leading-relaxed">{guide.content.introduction}</p>
              </CardContent>
            </Card>

            {/* Current Section */}
            {guide.content.sections[currentSection] && (
              <Card className="mb-8">
                <CardHeader>
                  <CardTitle className="flex items-center justify-between">
                    <span className="flex items-center gap-2">
                      <FileText className="h-5 w-5" />
                      {guide.content.sections[currentSection].title}
                    </span>
                    <Badge variant="outline">
                      Section {currentSection + 1} of {guide.content.sections.length}
                    </Badge>
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div className="prose prose-lg max-w-none">
                    {guide.content.sections[currentSection].content.split('\n\n').map((paragraph: string, index: number) => {
                      if (paragraph.startsWith('### ')) {
                        return <h3 key={index} className="text-xl font-semibold mt-6 mb-3">{paragraph.replace('### ', '')}</h3>
                      } else if (paragraph.startsWith('- ')) {
                        const listItems = paragraph.split('\n').filter(item => item.startsWith('- '))
                        return (
                          <ul key={index} className="list-disc pl-6 mb-4 space-y-1">
                            {listItems.map((item, i) => (
                              <li key={i}>{item.replace('- ', '')}</li>
                            ))}
                          </ul>
                        )
                      } else if (paragraph.trim()) {
                        return <p key={index} className="mb-4 text-muted-foreground leading-relaxed">{paragraph}</p>
                      }
                      return null
                    })}
                  </div>

                  {/* Tips Section */}
                  {guide.content.sections[currentSection].tips && (
                    <div className="bg-blue-50 p-4 rounded-lg border border-blue-200">
                      <div className="flex items-center gap-2 mb-3">
                        <Lightbulb className="h-5 w-5 text-blue-600" />
                        <h4 className="font-semibold text-blue-900">Pro Tips</h4>
                      </div>
                      <ul className="space-y-2">
                        {guide.content.sections[currentSection].tips.map((tip: string, index: number) => (
                          <li key={index} className="flex items-start gap-2 text-sm text-blue-800">
                            <CheckCircle className="h-4 w-4 mt-0.5 text-blue-600 flex-shrink-0" />
                            {tip}
                          </li>
                        ))}
                      </ul>
                    </div>
                  )}

                  {/* Examples Section */}
                  {guide.content.sections[currentSection].examples && (
                    <div className="space-y-4">
                      <h4 className="font-semibold flex items-center gap-2">
                        <Target className="h-5 w-5" />
                        Examples
                      </h4>
                      {guide.content.sections[currentSection].examples.map((example: any, index: number) => (
                        <div key={index} className="border rounded-lg p-4">
                          <div className="font-medium mb-2">{example.metric}</div>
                          <div className="text-sm text-green-600 mb-1">Good: {example.good}</div>
                          <div className="text-sm text-muted-foreground">{example.explanation}</div>
                        </div>
                      ))}
                    </div>
                  )}

                  {/* Navigation */}
                  <div className="flex items-center justify-between pt-6 border-t">
                    <Button 
                      variant="outline" 
                      onClick={() => setCurrentSection(Math.max(0, currentSection - 1))}
                      disabled={currentSection === 0}
                    >
                      Previous Section
                    </Button>
                    <Button 
                      onClick={() => setCurrentSection(Math.min(guide.content.sections.length - 1, currentSection + 1))}
                      disabled={currentSection === guide.content.sections.length - 1}
                    >
                      Next Section
                    </Button>
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Conclusion (shown when all sections are complete) */}
            {currentSection === guide.content.sections.length - 1 && (
              <Card className="mb-8">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <CheckCircle className="h-5 w-5 text-green-500" />
                    Conclusion
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-muted-foreground leading-relaxed mb-6">{guide.content.conclusion}</p>
                  <div className="flex items-center gap-4">
                    <Button>
                      <Download className="h-4 w-4 mr-2" />
                      Download PDF
                    </Button>
                    <Button variant="outline">
                      Share Guide
                    </Button>
                  </div>
                </CardContent>
              </Card>
            )}
          </div>

          {/* Sidebar */}
          <div className="lg:col-span-1">
            <div className="sticky top-8 space-y-6">
              {/* Table of Contents */}
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Table of Contents</CardTitle>
                </CardHeader>
                <CardContent className="space-y-2">
                  <div 
                    className={`p-2 rounded cursor-pointer text-sm ${currentSection === -1 ? 'bg-primary text-primary-foreground' : 'hover:bg-muted'}`}
                    onClick={() => setCurrentSection(-1)}
                  >
                    Introduction
                  </div>
                  {guide.content.sections.map((section: any, index: number) => (
                    <div 
                      key={index}
                      className={`p-2 rounded cursor-pointer text-sm ${currentSection === index ? 'bg-primary text-primary-foreground' : 'hover:bg-muted'}`}
                      onClick={() => setCurrentSection(index)}
                    >
                      {index + 1}. {section.title}
                    </div>
                  ))}
                </CardContent>
              </Card>

              {/* Topics Covered */}
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Topics Covered</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="flex flex-wrap gap-2">
                    {guide.topics.map((topic: string) => (
                      <Badge key={topic} variant="outline" className="text-xs">
                        {topic}
                      </Badge>
                    ))}
                  </div>
                </CardContent>
              </Card>

              {/* Quick Actions */}
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Quick Actions</CardTitle>
                </CardHeader>
                <CardContent className="space-y-3">
                  <Button className="w-full" size="sm">
                    <Download className="h-4 w-4 mr-2" />
                    Download PDF
                  </Button>
                  <Button variant="outline" className="w-full" size="sm">
                    <PlayCircle className="h-4 w-4 mr-2" />
                    Watch Video
                  </Button>
                  <Button variant="outline" className="w-full" size="sm">
                    Share Guide
                  </Button>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
