"use client"

import { useState } from "react"
import Link from "next/link"
import { <PERSON><PERSON><PERSON>, Clock, Users, Star, ArrowRight, Search, Filter, Download } from "lucide-react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"

// Guide data structure
interface Guide {
  id: string
  title: string
  description: string
  category: string
  difficulty: "Beginner" | "Intermediate" | "Advanced"
  readTime: string
  rating: number
  downloads: number
  lastUpdated: string
  slug: string
  topics: string[]
}

// Comprehensive guides data
const guides: Guide[] = [
  {
    id: "1",
    title: "YouTube Analytics Fundamentals: A Complete Beginner's Guide",
    description: "Master the basics of YouTube analytics with this comprehensive guide. Learn how to navigate YouTube Studio, understand key metrics, and make data-driven decisions for your channel growth.",
    category: "Analytics",
    difficulty: "Beginner",
    readTime: "25 min read",
    rating: 4.9,
    downloads: 15420,
    lastUpdated: "2024-01-15",
    slug: "youtube-analytics-fundamentals",
    topics: ["YouTube Studio", "Key Metrics", "Data Interpretation", "Performance Tracking"]
  },
  {
    id: "2",
    title: "Advanced YouTube SEO Strategies for 2024",
    description: "Take your YouTube SEO to the next level with advanced optimization techniques, keyword research strategies, and algorithm insights that top creators use to dominate search results.",
    category: "SEO",
    difficulty: "Advanced",
    readTime: "35 min read",
    rating: 4.8,
    downloads: 12350,
    lastUpdated: "2024-01-12",
    slug: "advanced-youtube-seo-strategies",
    topics: ["Keyword Research", "Video Optimization", "Algorithm Updates", "Competitive Analysis"]
  },
  {
    id: "3",
    title: "YouTube Monetization Mastery: From $0 to $10K/Month",
    description: "Comprehensive guide to YouTube monetization covering ad revenue, sponsorships, merchandise, memberships, and alternative income streams for content creators.",
    category: "Monetization",
    difficulty: "Intermediate",
    readTime: "40 min read",
    rating: 4.7,
    downloads: 18750,
    lastUpdated: "2024-01-10",
    slug: "youtube-monetization-mastery",
    topics: ["Ad Revenue", "Sponsorships", "Merchandise", "Channel Memberships", "Super Chat"]
  },
  {
    id: "4",
    title: "Content Strategy Blueprint for YouTube Success",
    description: "Develop a winning content strategy with proven frameworks for content planning, audience research, trend analysis, and long-term channel growth planning.",
    category: "Content Strategy",
    difficulty: "Intermediate",
    readTime: "30 min read",
    rating: 4.8,
    downloads: 14200,
    lastUpdated: "2024-01-08",
    slug: "content-strategy-blueprint",
    topics: ["Content Planning", "Audience Research", "Trend Analysis", "Editorial Calendar"]
  },
  {
    id: "5",
    title: "YouTube Audience Analytics Deep Dive",
    description: "Understand your audience like never before with advanced audience analytics techniques, demographic analysis, and behavioral insights for better content targeting.",
    category: "Analytics",
    difficulty: "Advanced",
    readTime: "28 min read",
    rating: 4.9,
    downloads: 9850,
    lastUpdated: "2024-01-05",
    slug: "audience-analytics-deep-dive",
    topics: ["Demographics", "Behavioral Analysis", "Audience Retention", "Geographic Insights"]
  },
  {
    id: "6",
    title: "YouTube Thumbnail and Title Optimization Guide",
    description: "Create compelling thumbnails and titles that drive clicks and views. Learn design principles, A/B testing strategies, and psychological triggers that work.",
    category: "Optimization",
    difficulty: "Beginner",
    readTime: "20 min read",
    rating: 4.6,
    downloads: 22100,
    lastUpdated: "2024-01-03",
    slug: "thumbnail-title-optimization",
    topics: ["Thumbnail Design", "Title Writing", "A/B Testing", "Click-Through Rate"]
  },
  {
    id: "7",
    title: "YouTube Live Streaming Analytics and Growth",
    description: "Maximize your live streaming potential with analytics insights, audience engagement strategies, and monetization techniques specific to live content.",
    category: "Live Streaming",
    difficulty: "Intermediate",
    readTime: "32 min read",
    rating: 4.5,
    downloads: 7650,
    lastUpdated: "2024-01-01",
    slug: "live-streaming-analytics",
    topics: ["Live Metrics", "Real-time Engagement", "Super Chat Analytics", "Stream Optimization"]
  },
  {
    id: "8",
    title: "YouTube Shorts Analytics and Strategy",
    description: "Dominate YouTube Shorts with data-driven strategies. Learn how to analyze Shorts performance, optimize for the algorithm, and scale your short-form content.",
    category: "Shorts",
    difficulty: "Intermediate",
    readTime: "22 min read",
    rating: 4.7,
    downloads: 16800,
    lastUpdated: "2023-12-28",
    slug: "youtube-shorts-analytics",
    topics: ["Shorts Algorithm", "Performance Metrics", "Content Optimization", "Viral Strategies"]
  }
]

export default function GuidesPage() {
  const [searchQuery, setSearchQuery] = useState("")
  const [selectedCategory, setSelectedCategory] = useState("all")
  const [selectedDifficulty, setSelectedDifficulty] = useState("all")
  const [filteredGuides, setFilteredGuides] = useState(guides)

  // Filter guides based on search and filters
  const filterGuides = () => {
    let filtered = guides

    if (selectedCategory !== "all") {
      filtered = filtered.filter(guide => guide.category.toLowerCase() === selectedCategory.toLowerCase())
    }

    if (selectedDifficulty !== "all") {
      filtered = filtered.filter(guide => guide.difficulty.toLowerCase() === selectedDifficulty.toLowerCase())
    }

    if (searchQuery) {
      filtered = filtered.filter(guide =>
        guide.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
        guide.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
        guide.topics.some(topic => topic.toLowerCase().includes(searchQuery.toLowerCase()))
      )
    }

    setFilteredGuides(filtered)
  }

  // Update filters when search or category changes
  useState(() => {
    filterGuides()
  }, [searchQuery, selectedCategory, selectedDifficulty])

  const categories = ["all", "analytics", "seo", "monetization", "content strategy", "optimization", "live streaming", "shorts"]
  const difficulties = ["all", "beginner", "intermediate", "advanced"]

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty.toLowerCase()) {
      case "beginner": return "bg-green-100 text-green-800"
      case "intermediate": return "bg-yellow-100 text-yellow-800"
      case "advanced": return "bg-red-100 text-red-800"
      default: return "bg-gray-100 text-gray-800"
    }
  }

  return (
    <div className="min-h-screen bg-background">
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="text-center mb-12">
          <h1 className="text-4xl md:text-5xl font-bold mb-4">YouTube Analytics Guides</h1>
          <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
            Comprehensive, step-by-step guides to help you master YouTube analytics, 
            optimize your content, and grow your channel with data-driven strategies.
          </p>
        </div>

        {/* Search and Filters */}
        <div className="flex flex-col lg:flex-row gap-4 mb-8 max-w-4xl mx-auto">
          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
            <Input
              placeholder="Search guides..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10"
            />
          </div>
          <Select value={selectedCategory} onValueChange={setSelectedCategory}>
            <SelectTrigger className="w-full lg:w-48">
              <SelectValue placeholder="Category" />
            </SelectTrigger>
            <SelectContent>
              {categories.map((category) => (
                <SelectItem key={category} value={category}>
                  {category.charAt(0).toUpperCase() + category.slice(1)}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          <Select value={selectedDifficulty} onValueChange={setSelectedDifficulty}>
            <SelectTrigger className="w-full lg:w-48">
              <SelectValue placeholder="Difficulty" />
            </SelectTrigger>
            <SelectContent>
              {difficulties.map((difficulty) => (
                <SelectItem key={difficulty} value={difficulty}>
                  {difficulty.charAt(0).toUpperCase() + difficulty.slice(1)}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        {/* Stats */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-8">
          <Card>
            <CardContent className="p-4 text-center">
              <BookOpen className="h-8 w-8 mx-auto mb-2 text-primary" />
              <div className="text-2xl font-bold">{guides.length}</div>
              <div className="text-sm text-muted-foreground">Total Guides</div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-4 text-center">
              <Download className="h-8 w-8 mx-auto mb-2 text-primary" />
              <div className="text-2xl font-bold">116K+</div>
              <div className="text-sm text-muted-foreground">Downloads</div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-4 text-center">
              <Star className="h-8 w-8 mx-auto mb-2 text-primary" />
              <div className="text-2xl font-bold">4.7</div>
              <div className="text-sm text-muted-foreground">Avg Rating</div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-4 text-center">
              <Users className="h-8 w-8 mx-auto mb-2 text-primary" />
              <div className="text-2xl font-bold">25K+</div>
              <div className="text-sm text-muted-foreground">Active Users</div>
            </CardContent>
          </Card>
        </div>

        {/* Featured Guide */}
        {filteredGuides.length > 0 && (
          <div className="mb-12">
            <h2 className="text-2xl font-bold mb-6">Featured Guide</h2>
            <Card className="overflow-hidden border-2 border-primary/20">
              <div className="md:flex">
                <div className="md:w-1/3 bg-gradient-to-br from-primary/10 to-primary/5 p-8 flex items-center justify-center">
                  <div className="text-center">
                    <BookOpen className="h-16 w-16 mx-auto mb-4 text-primary" />
                    <Badge className={getDifficultyColor(filteredGuides[0].difficulty)}>
                      {filteredGuides[0].difficulty}
                    </Badge>
                  </div>
                </div>
                <div className="md:w-2/3 p-6">
                  <div className="flex items-center gap-2 mb-3">
                    <Badge variant="secondary">{filteredGuides[0].category}</Badge>
                    <span className="text-sm text-muted-foreground">•</span>
                    <span className="text-sm text-muted-foreground">{filteredGuides[0].readTime}</span>
                    <span className="text-sm text-muted-foreground">•</span>
                    <div className="flex items-center gap-1">
                      <Star className="h-4 w-4 fill-yellow-400 text-yellow-400" />
                      <span className="text-sm">{filteredGuides[0].rating}</span>
                    </div>
                  </div>
                  <h3 className="text-2xl font-bold mb-3">{filteredGuides[0].title}</h3>
                  <p className="text-muted-foreground mb-4">{filteredGuides[0].description}</p>
                  <div className="flex flex-wrap gap-2 mb-4">
                    {filteredGuides[0].topics.slice(0, 3).map((topic) => (
                      <Badge key={topic} variant="outline" className="text-xs">
                        {topic}
                      </Badge>
                    ))}
                  </div>
                  <div className="flex items-center justify-between">
                    <div className="text-sm text-muted-foreground">
                      {filteredGuides[0].downloads.toLocaleString()} downloads
                    </div>
                    <Button asChild>
                      <Link href={`/guides/${filteredGuides[0].slug}`}>
                        Read Guide <ArrowRight className="ml-2 h-4 w-4" />
                      </Link>
                    </Button>
                  </div>
                </div>
              </div>
            </Card>
          </div>
        )}

        {/* Guides Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredGuides.slice(1).map((guide) => (
            <Card key={guide.id} className="overflow-hidden hover:shadow-lg transition-shadow">
              <CardHeader className="pb-3">
                <div className="flex items-center justify-between mb-2">
                  <Badge variant="outline">{guide.category}</Badge>
                  <Badge className={getDifficultyColor(guide.difficulty)}>
                    {guide.difficulty}
                  </Badge>
                </div>
                <CardTitle className="text-lg line-clamp-2">{guide.title}</CardTitle>
                <CardDescription className="line-clamp-3">{guide.description}</CardDescription>
              </CardHeader>
              <CardContent className="pt-0">
                <div className="flex items-center gap-4 text-sm text-muted-foreground mb-3">
                  <div className="flex items-center gap-1">
                    <Clock className="h-4 w-4" />
                    {guide.readTime}
                  </div>
                  <div className="flex items-center gap-1">
                    <Star className="h-4 w-4 fill-yellow-400 text-yellow-400" />
                    {guide.rating}
                  </div>
                  <div className="flex items-center gap-1">
                    <Download className="h-4 w-4" />
                    {(guide.downloads / 1000).toFixed(1)}K
                  </div>
                </div>
                <div className="flex flex-wrap gap-1 mb-4">
                  {guide.topics.slice(0, 2).map((topic) => (
                    <Badge key={topic} variant="outline" className="text-xs">
                      {topic}
                    </Badge>
                  ))}
                  {guide.topics.length > 2 && (
                    <Badge variant="outline" className="text-xs">
                      +{guide.topics.length - 2} more
                    </Badge>
                  )}
                </div>
                <Button asChild className="w-full">
                  <Link href={`/guides/${guide.slug}`}>
                    Read Guide
                  </Link>
                </Button>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* No Results */}
        {filteredGuides.length === 0 && (
          <div className="text-center py-12">
            <BookOpen className="h-16 w-16 mx-auto mb-4 text-muted-foreground" />
            <h3 className="text-lg font-semibold mb-2">No guides found</h3>
            <p className="text-muted-foreground">Try adjusting your search or filter criteria.</p>
          </div>
        )}

        {/* CTA Section */}
        <div className="mt-16 text-center">
          <Card className="bg-gradient-to-r from-primary/10 to-primary/5 border-primary/20">
            <CardContent className="p-8">
              <h2 className="text-2xl font-bold mb-4">Need a Custom Guide?</h2>
              <p className="text-muted-foreground mb-6 max-w-2xl mx-auto">
                Can't find what you're looking for? Our team of YouTube analytics experts can create 
                custom guides tailored to your specific needs and goals.
              </p>
              <Button size="lg">
                Request Custom Guide
              </Button>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}
