"use client"

import Link from "next/link"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { AnimatedThumbnailGrid } from "@/components/auth/AnimatedThumbnailGrid"
import { Github, Chrome } from 'lucide-react' // Assuming you might use these icons

export default function LoginPage() {
  return (
    // Use bg-background for the main container
    <div className="w-full lg:grid lg:min-h-[calc(100vh-4rem)] lg:grid-cols-2 xl:min-h-[calc(100vh-4rem)] bg-background">
      {/* Left Panel - Use bg-muted */}
      <div className="hidden bg-muted lg:block relative">
        <AnimatedThumbnailGrid />
      </div>
      {/* Right Panel (Login Form) */}
      <div className="flex items-center justify-center py-12">
        <div className="mx-auto grid w-[350px] gap-6">
          <div className="grid gap-2 text-center">
            <h1 className="text-3xl font-bold text-foreground">登录</h1>
            <p className="text-balance text-muted-foreground">
              输入您的邮箱以登录您的账户
            </p>
          </div>
          <div className="grid gap-4">
            <div className="grid gap-2">
              <Label htmlFor="email">邮箱</Label>
              <Input
                id="email"
                type="email"
                placeholder="<EMAIL>"
                required
              />
            </div>
            <div className="grid gap-2">
              <div className="flex items-center">
                <Label htmlFor="password">密码</Label>
                <Link
                  href="/forgot-password"
                  className="ml-auto inline-block text-sm text-muted-foreground underline"
                >
                  忘记密码?
                </Link>
              </div>
              <Input id="password" type="password" required />
            </div>
            <Button type="submit" className="w-full">
              登录
            </Button>
            <div className="relative my-4">
              <div className="absolute inset-0 flex items-center">
                <span className="w-full border-t border-border" />
              </div>
              <div className="relative flex justify-center text-xs uppercase">
                <span className="bg-background px-2 text-muted-foreground">
                  或者使用以下方式登录
                </span>
              </div>
            </div>
            <Button variant="outline" className="w-full">
              <Github className="mr-2 h-4 w-4" /> 使用 GitHub 登录
            </Button>
            <Button variant="outline" className="w-full">
              <Chrome className="mr-2 h-4 w-4" /> 使用 Google 登录
            </Button>
          </div>
          <div className="mt-4 text-center text-sm">
            还没有账户?{" "}
            <Link href="/signup" className="underline text-foreground">
              注册
            </Link>
          </div>
        </div>
      </div>
    </div>
  )
}
