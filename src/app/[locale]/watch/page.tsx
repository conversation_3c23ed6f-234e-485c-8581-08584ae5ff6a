import { redirect } from 'next/navigation';
import WatchVideoClient from './WatchVideoClient';
import { Suspense } from 'react';

interface WatchVideoPageProps {
  params: {
    locale: string;
  };
  searchParams: {
    videoUrl?: string;
    hasExternalVideo?: string;
  };
}

// 设置页面缓存选项，减少页面重新渲染和请求次数
export const dynamic = 'force-dynamic';
export const revalidate = 60; // 1分钟缓存

export default async function WatchVideoPage({ 
  params, 
  searchParams 
}: WatchVideoPageProps) {
  // 使用await获取参数，避免Next.js错误
  const awaitedParams = await params;
  const awaitedSearchParams = await searchParams;
  
  const videoUrl = awaitedSearchParams.videoUrl;
  const hasExternalVideo = awaitedSearchParams.hasExternalVideo;
  const locale = awaitedParams.locale;
  
  // 如果URL中没有必要的参数，重定向到首页
  if (!videoUrl || hasExternalVideo !== 'true') {
    console.log('[页面] 缺少必要参数，重定向到首页');
    redirect(`/${locale}`);
  }

  // 渲染客户端组件，用于获取外部视频数据并展示
  return (
    <div className="container mx-auto px-4 py-8">
      <Suspense fallback={<div className="h-screen flex items-center justify-center">加载中...</div>}>
        <WatchVideoClient videoUrl={videoUrl} locale={locale} />
      </Suspense>
    </div>
  );
} 