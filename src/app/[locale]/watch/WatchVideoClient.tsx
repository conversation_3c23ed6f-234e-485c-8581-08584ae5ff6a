'use client';

import { useState, useEffect } from 'react';
import { useTranslations } from 'next-intl';
import { Card, CardContent, CardDescription, CardHeader, CardTitle, CardFooter } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Skeleton } from '@/components/ui/skeleton';
import { Separator } from '@/components/ui/separator';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { ArrowLeft, ExternalLink, ThumbsUp, Eye, MessageSquare, Clock as ClockIcon, Calendar } from 'lucide-react';
import { ExclamationTriangleIcon, CheckCircledIcon } from "@radix-ui/react-icons";
import { useRouter } from 'next/navigation';
import React from 'react';

interface WatchVideoClientProps {
  videoUrl: string;
  locale: string;
}

export default function WatchVideoClient({ videoUrl, locale }: WatchVideoClientProps) {
  const t = useTranslations('Watch');
  const router = useRouter();
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [videoData, setVideoData] = useState<any>(null);

  // 通过useRef防止重复请求
  const isRequestInProgress = React.useRef(false);

  // 解析视频 ID
  const getVideoId = (url: string): string | null => {
    try {
      const urlObj = new URL(url);
      return urlObj.searchParams.get('v');
    } catch (error) {
      console.error('解析视频 URL 错误:', error);
      return null;
    }
  };

  // 提取fetchVideoData到组件级别以便复用
  useEffect(() => {
    // 防止重复请求
    if (isRequestInProgress.current) return;
    
    // 先检查sessionStorage缓存
    const cacheKey = `video_data_${videoUrl}`;
    const cachedData = sessionStorage.getItem(cacheKey);
    
    if (cachedData) {
      try {
        const parsedData = JSON.parse(cachedData);
        const cacheTime = parsedData._cacheTime || 0;
        const now = Date.now();
        
        // 如果缓存不超过5分钟，直接使用缓存数据
        if (now - cacheTime < 5 * 60 * 1000) {
          console.log('[客户端] 使用缓存的视频数据');
          setVideoData(parsedData);
          setLoading(false);
          return;
        }
      } catch (e) {
        console.error('[客户端] 解析缓存数据出错:', e);
      }
    }
    
    // 没有缓存或缓存过期，发起请求
    isRequestInProgress.current = true;
    fetchVideoData().finally(() => {
      isRequestInProgress.current = false;
    });
  }, [videoUrl]);

  // 修改fetchVideoData函数以支持缓存
  const fetchVideoData = async () => {
    try {
      setLoading(true);
      setError(null);

      const videoId = getVideoId(videoUrl);
      if (!videoId) {
        throw new Error('无效的视频 URL');
      }

      // 构建 API 请求
      const response = await fetch(`/api/videos?videoId=${videoId}`);

      if (!response.ok) {
        throw new Error('获取视频数据失败');
      }

      const data = await response.json();
      console.log('[客户端] 获取到视频数据:', data);
      
      // 存入缓存
      if (data && !data.status) { // 不缓存pending状态
        const cacheData = {
          ...data,
          _cacheTime: Date.now()
        };
        sessionStorage.setItem(`video_data_${videoUrl}`, JSON.stringify(cacheData));
      }
      
      setVideoData(data);
    } catch (error: any) {
      console.error('获取视频数据错误:', error);
      setError(error.message || '获取视频数据时出错');
    } finally {
      setLoading(false);
    }
  };

  // 返回到首页
  const handleBack = () => {
    router.push(`/${locale}`);
  };

  // 打开外部链接
  const handleOpenOriginal = () => {
    window.open(videoUrl, '_blank');
  };

  // 加载状态渲染
  if (loading) {
    return (
      <div className="space-y-4">
        <Button variant="outline" onClick={handleBack} className="mb-4">
          <ArrowLeft className="mr-2 h-4 w-4" />
          {t('back')}
        </Button>
        <Card>
          <CardHeader>
            <Skeleton className="h-8 w-3/4" />
            <Skeleton className="h-6 w-1/2 mt-2" />
          </CardHeader>
          <CardContent>
            <Skeleton className="aspect-video w-full rounded-md" />
            <div className="mt-4 space-y-4">
              <Skeleton className="h-4 w-full" />
              <Skeleton className="h-4 w-full" />
              <Skeleton className="h-4 w-3/4" />
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  // 错误状态渲染
  if (error) {
    return (
      <div className="space-y-4">
        <Button variant="outline" onClick={handleBack} className="mb-4">
          <ArrowLeft className="mr-2 h-4 w-4" />
          {t('back')}
        </Button>
        <Alert variant="destructive" className="mb-6">
          <ExclamationTriangleIcon className="h-4 w-4" />
          <AlertTitle>{t('error')}</AlertTitle>
          <AlertDescription>
            <p>{t('errorDescription')}</p>
            <p className="mt-2">{error}</p>
            <Button onClick={handleOpenOriginal} className="mt-4" variant="outline">
              <ExternalLink className="mr-2 h-4 w-4" />
              {t('openOriginal')}
            </Button>
          </AlertDescription>
        </Alert>
      </div>
    );
  }

  // 处理pending状态
  if (videoData?.status === 'pending') {
    return (
      <div className="space-y-4">
        <Button variant="outline" onClick={handleBack} className="mb-4">
          <ArrowLeft className="mr-2 h-4 w-4" />
          {t('back')}
        </Button>
        <Alert className="mb-6 bg-blue-50 border-blue-200">
          <ClockIcon className="h-5 w-5 text-blue-500" />
          <AlertTitle className="text-blue-700 text-lg">{t('processing') || '处理中'}</AlertTitle>
          <AlertDescription className="mt-4">
            <div className="space-y-4">
              <p className="text-blue-500">
                {videoData.message || '频道数据正在处理中，请稍后再试'}
              </p>
              {videoData.task_id && (
                <p className="text-sm text-gray-500">任务ID: {videoData.task_id}</p>
              )}
              <div className="mt-6 flex flex-col space-y-3 sm:flex-row sm:space-y-0 sm:space-x-4">
                <Button 
                  onClick={handleBack}
                  variant="outline"
                  className="border-blue-300 hover:bg-blue-50"
                >
                  {t('backToHome') || '返回首页'}
                </Button>
                <Button 
                  onClick={fetchVideoData}
                  className="bg-blue-600 hover:bg-blue-700"
                >
                  {t('refresh') || '刷新状态'}
                </Button>
              </div>
              <p className="text-xs mt-4 text-gray-500">
                {t('processingTip') || '处理可能需要一些时间，请稍候再试'}
              </p>
            </div>
          </AlertDescription>
        </Alert>
      </div>
    );
  }

  // 成功状态渲染
  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center mb-4">
        <Button variant="outline" onClick={handleBack}>
          <ArrowLeft className="mr-2 h-4 w-4" />
          {t('back')}
        </Button>
        <Button variant="outline" onClick={handleOpenOriginal}>
          <ExternalLink className="mr-2 h-4 w-4" />
          {t('openOriginal')}
        </Button>
      </div>

      {/* 频道信息区域 */}
      {videoData?.channel && (
        <div className="mt-8">
          <h3 className="text-lg font-medium mb-4">{t('channel') || '频道信息'}</h3>
          <Card className="overflow-hidden">
            <CardContent className="p-0">
              <a 
                href={`/@https://www.reyoutube.com/${locale}/channel/${videoData.channel.id}`}
                className="block hover:bg-muted/10 transition-colors"
              >
                <div className="flex p-4 items-center gap-4">
                  {videoData.channel.thumbnailUrl ? (
                    <img 
                      src={videoData.channel.thumbnailUrl} 
                      alt={videoData.channel.title}
                      className="w-16 h-16 rounded-full object-cover border"
                    />
                  ) : (
                    <div className="w-16 h-16 rounded-full bg-muted flex items-center justify-center text-lg font-bold">
                      {videoData.channel.title?.charAt(0).toUpperCase() || '?'}
                    </div>
                  )}
                  <div className="flex-1">
                    <h4 className="text-lg font-medium">{videoData.channel.title}</h4>
                    {videoData.channel.customUrl && (
                      <p className="text-sm text-muted-foreground">{videoData.channel.customUrl}</p>
                    )}
                    <div className="flex items-center gap-3 mt-1 text-sm text-muted-foreground">
                      {videoData.channel.subscriberCount !== undefined && (
                        <span>{formatNumber(videoData.channel.subscriberCount)} {t('subscribers') || '订阅者'}</span>
                      )}
                      {videoData.channel.videoCount !== undefined && (
                        <span>{formatNumber(videoData.channel.videoCount)} {t('videos') || '视频'}</span>
                      )}
                    </div>
                  </div>
                  <ExternalLink className="h-4 w-4 text-muted-foreground" />
                </div>
                {videoData.channel.description && (
                  <div className="px-4 pb-4 pt-0 text-sm text-muted-foreground line-clamp-2">
                    {videoData.channel.description}
                  </div>
                )}
              </a>
            </CardContent>
          </Card>
        </div>
      )}

      {/* 成功消息 */}
      {/* <Alert className="mb-6 bg-green-50 border-green-200">
        <CheckCircledIcon className="h-4 w-4 text-green-600" />
        <AlertTitle className="text-green-700">{t('videoFound')}</AlertTitle>
        <AlertDescription className="text-green-600">
          {t('videoFoundDesc')}
        </AlertDescription>
      </Alert> */}

      <Card className="overflow-hidden">
        {/* 视频播放器 */}
        {videoData?.videoId && (
          <div className="w-full">
            <iframe
              className="w-full aspect-video"
              src={`https://www.youtube.com/embed/${videoData.videoId}`}
              title={videoData.title}
              frameBorder="0"
              allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
              allowFullScreen
            ></iframe>
          </div>
        )}
        
        <CardHeader>
          <CardTitle className="text-2xl">{videoData?.title || t('videoTitle')}</CardTitle>
          <div className="flex items-center gap-2 flex-wrap">
            <CardDescription className="text-lg">{videoData?.channelTitle || t('channelName')}</CardDescription>
            {videoData?.publishedAt && (
              <Badge variant="outline" className="flex items-center gap-1">
                <Calendar className="h-3 w-3" />
                {formatDate(videoData.publishedAt, locale)}
              </Badge>
            )}
          </div>
        </CardHeader>
        
        <CardContent className="space-y-6">
          {/* 视频统计信息 */}
          {videoData?.statistics && (
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 p-4 bg-muted/20 rounded-lg">
              <div className="flex flex-col items-center">
                <div className="flex items-center gap-1 text-muted-foreground mb-1">
                  <Eye className="h-4 w-4" />
                  <span className="text-sm">{t('views')}</span>
                </div>
                <p className="text-xl font-medium">
                  {formatNumber(parseInt(videoData.statistics.viewCount))}
                </p>
              </div>
              <div className="flex flex-col items-center">
                <div className="flex items-center gap-1 text-muted-foreground mb-1">
                  <ThumbsUp className="h-4 w-4" />
                  <span className="text-sm">{t('likes')}</span>
                </div>
                <p className="text-xl font-medium">
                  {formatNumber(parseInt(videoData.statistics.likeCount))}
                </p>
              </div>
              <div className="flex flex-col items-center">
                <div className="flex items-center gap-1 text-muted-foreground mb-1">
                  <MessageSquare className="h-4 w-4" />
                  <span className="text-sm">{t('comments')}</span>
                </div>
                <p className="text-xl font-medium">
                  {formatNumber(parseInt(videoData.statistics.commentCount))}
                </p>
              </div>
              <div className="flex flex-col items-center">
                <div className="flex items-center gap-1 text-muted-foreground mb-1">
                  <ClockIcon className="h-4 w-4" />
                  <span className="text-sm">{t('duration')}</span>
                </div>
                <p className="text-xl font-medium">{videoData.duration || '-'}</p>
              </div>
            </div>
          )}

          {/* 视频描述 */}
          {videoData?.description && (
            <div className="mt-4">
              <h3 className="text-lg font-medium mb-2">{t('description')}</h3>
              <Card className="bg-muted/10">
                <CardContent className="pt-4">
                  <p className="whitespace-pre-line text-muted-foreground">
                    {videoData.description}
                  </p>
                </CardContent>
              </Card>
            </div>
          )}
          
          
        </CardContent>
        
        <CardFooter className="flex justify-between pt-2 pb-4">
          <Button variant="ghost" onClick={handleBack}>
            <ArrowLeft className="mr-2 h-4 w-4" />
            {t('back')}
          </Button>
          <Button variant="outline" onClick={handleOpenOriginal}>
            <ExternalLink className="mr-2 h-4 w-4" />
            {t('openOriginal')}
          </Button>
        </CardFooter>
      </Card>
    </div>
  );
}

// 格式化数字显示
function formatNumber(num: number): string {
  if (num >= 1000000) {
    return (num / 1000000).toFixed(1) + 'M';
  }
  if (num >= 1000) {
    return (num / 1000).toFixed(1) + 'K';
  }
  return num.toString();
}

// 格式化日期显示
function formatDate(dateString: string, locale: string = 'zh-CN'): string {
  try {
    const date = new Date(dateString);
    return date.toLocaleDateString(locale, {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  } catch (e) {
    return dateString;
  }
} 