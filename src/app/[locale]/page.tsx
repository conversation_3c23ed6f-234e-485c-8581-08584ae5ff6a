import { getLandingPage } from "@/app/actions";
import { unstable_setRequestLocale } from 'next-intl/server';
import { routing } from '@/i18n/routing';
import { Footer } from "@/components/ui/footer-section";
import GoogleOneTapWrapper from "@/components/GoogleOneTapWrapper";
import HomePage from "./home";
import { Metadata } from 'next';
type Locale = (typeof routing.locales)[number];

export async function generateMetadata({
  params,
}: {
  params: Promise<{ locale: string }>;
}): Promise<Metadata> {
  const { locale } = await params;

  return {
    title: 'ReYoutube - YouTube Analytics & Channel Growth Platform',
    description: 'The most comprehensive YouTube analytics platform for creators, marketers, and analysts. Analyze channels, track performance, and grow your YouTube presence with data-driven insights.',
    keywords: 'YouTube analytics, channel analytics, YouTube SEO, video analytics, creator tools, YouTube growth, social media analytics',
    authors: [{ name: 'ReYoutube Team' }],
    creator: 'ReYoutube',
    publisher: 'ReYoutube',
    formatDetection: {
      email: false,
      address: false,
      telephone: false,
    },
    metadataBase: new URL(process.env.NEXT_PUBLIC_WEB_URL || 'https://www.reyoutube.com'),
    alternates: {
      canonical: `/${locale}`,
      languages: {
        'en': '/en',
        'zh': '/zh',
        'es': '/es',
        'fr': '/fr',
        'de': '/de',
        'ja': '/ja',
        'ko': '/ko',
      },
    },
    openGraph: {
      title: 'ReYoutube - YouTube Analytics & Channel Growth Platform',
      description: 'The most comprehensive YouTube analytics platform for creators, marketers, and analysts. Analyze channels, track performance, and grow your YouTube presence with data-driven insights.',
      url: `/${locale}`,
      siteName: 'ReYoutube',
      images: [
        {
          url: '/images/og-home.jpg',
          width: 1200,
          height: 630,
          alt: 'ReYoutube - YouTube Analytics Platform',
        },
      ],
      locale: locale,
      type: 'website',
    },
    twitter: {
      card: 'summary_large_image',
      title: 'ReYoutube - YouTube Analytics & Channel Growth Platform',
      description: 'The most comprehensive YouTube analytics platform for creators, marketers, and analysts. Analyze channels, track performance, and grow your YouTube presence with data-driven insights.',
      images: ['/images/og-home.jpg'],
      creator: '@reyoutube',
      site: '@reyoutube',
    },
    robots: {
      index: true,
      follow: true,
      googleBot: {
        index: true,
        follow: true,
        'max-video-preview': -1,
        'max-image-preview': 'large',
        'max-snippet': -1,
      },
    },
    verification: {
      google: 'your-google-verification-code',
      yandex: 'your-yandex-verification-code',
      yahoo: 'your-yahoo-verification-code',
    },
  };
}

export default async function LandingPage({
  params,
}: {
  params: Promise<{ locale: string }>;
}) {
  // 使用 await 获取 locale
  const { locale } = await params;

  // 设置请求的 locale
  unstable_setRequestLocale(locale);

  // 获取页面数据
  const page = await getLandingPage(locale);

  return (
    <>
      {/* Google One Tap组件 */}
      <GoogleOneTapWrapper />
      
      <HomePage />
      {/* {page.hero && <Hero hero={page.hero} />} */}
      {/* {page.branding && <Branding section={page.branding} />} */}
      {/* {page.introduce && <Feature1 section={page.introduce} />} */}
      {/* {page.benefit && <Feature2 section={page.benefit} />} */}
      {/* {page.usage && <Feature3 section={page.usage} />} */}
      {/* {page.feature && <Features features={page.feature} />} */}
      {/* {page.showcase && <Showcase section={page.showcase} />} */}
      {/* {page.stats && <Stats section={page.stats} />}
      {page.pricing && <Pricing pricing={page.pricing} />}
      {page.testimonial && <Testimonial section={page.testimonial} />}
      {page.faq && <FAQ section={page.faq} />}
      {page.cta && <CTA section={page.cta} />} */}
      
      {/* {page.footer && <Footerdemo footer={page.footer} />} */}
      <Footer />
    </>
  );
}
