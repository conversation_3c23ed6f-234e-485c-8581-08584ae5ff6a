"use client"

import { useState } from "react"
import Link from "next/link"
import {
  Home,
  Database,
  BarChart3,
  Settings,
  Bell,
  Search,
  Plus,
  ChevronDown,
  Download,
  Share2,
  Clock,
  LayoutGrid,
  LayoutList,
  Save,
  PlusCircle,
  Trash,
  Copy,
  Map,
} from "lucide-react"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { ChartContainer, ChartTooltip, ChartTooltipContent } from "@/components/ui/chart"
import {
  <PERSON><PERSON>ons<PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON> as <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>,
  XAxis,
  YAxis,
  CartesianGrid,
  <PERSON>,
  <PERSON><PERSON><PERSON> as RechartsBarChart,
  Bar,
  PieChart as RechartsPieChart,
  Pie,
  Cell,
} from "recharts"

// Sample data for charts
const viewsData = [
  { name: "Jan", value: 4000 },
  { name: "Feb", value: 3000 },
  { name: "Mar", value: 2000 },
  { name: "Apr", value: 2780 },
  { name: "May", value: 1890 },
  { name: "Jun", value: 2390 },
  { name: "Jul", value: 3490 },
]

const engagementData = [
  { name: "Mon", likes: 240, comments: 120, shares: 80 },
  { name: "Tue", likes: 300, comments: 130, shares: 90 },
  { name: "Wed", likes: 200, comments: 80, shares: 60 },
  { name: "Thu", likes: 278, comments: 140, shares: 95 },
  { name: "Fri", likes: 189, comments: 100, shares: 70 },
  { name: "Sat", likes: 239, comments: 115, shares: 85 },
  { name: "Sun", likes: 349, comments: 170, shares: 110 },
]

const demographicsData = [
  { name: "18-24", value: 25 },
  { name: "25-34", value: 35 },
  { name: "35-44", value: 20 },
  { name: "45-54", value: 10 },
  { name: "55+", value: 10 },
]

const COLORS = ["#0088FE", "#00C49F", "#FFBB28", "#FF8042", "#8884D8"]

// Sample report templates
const reportTemplates = [
  {
    id: "template-1",
    name: "Channel Overview",
    description: "Key metrics for your channel performance",
    lastUpdated: "2 hours ago",
  },
  {
    id: "template-2",
    name: "Audience Insights",
    description: "Detailed breakdown of your audience demographics",
    lastUpdated: "Yesterday",
  },
  {
    id: "template-3",
    name: "Content Performance",
    description: "Analytics for your top performing content",
    lastUpdated: "3 days ago",
  },
  {
    id: "template-4",
    name: "Revenue Report",
    description: "Financial metrics and revenue streams",
    lastUpdated: "1 week ago",
  },
]

export default function ReportsPage() {
  const [sidebarOpen, setSidebarOpen] = useState(true)
  const [viewMode, setViewMode] = useState<"grid" | "list">("grid")

  return (
    <div className="flex h-screen bg-slate-50">
      {/* Sidebar */}
      <div
        className={`bg-slate-900 text-white ${sidebarOpen ? "w-64" : "w-20"} transition-all duration-300 flex flex-col`}
      >
        <div className="p-4 flex items-center justify-between border-b border-slate-700">
          {/* <div className="flex items-center">
            <Database className="h-6 w-6 text-emerald-400" />
            {sidebarOpen && <span className="ml-2 font-bold">DataFlow</span>}
          </div> */}
          <Button
            variant="ghost"
            size="icon"
            onClick={() => setSidebarOpen(!sidebarOpen)}
            className="text-slate-400 hover:text-white hover:bg-slate-800"
          >
            <ChevronDown className={`h-5 w-5 transition-transform ${sidebarOpen ? "" : "rotate-180"}`} />
          </Button>
        </div>
        <div className="flex-1 py-4">
          <nav className="space-y-1 px-2">
            <Link
              href="/dashboard"
              className="flex items-center px-3 py-2 text-sm font-medium rounded-md text-slate-300 hover:bg-slate-800 hover:text-white"
            >
              <Home className={`${sidebarOpen ? "mr-3" : "mx-auto"} h-5 w-5`} />
              {sidebarOpen && <span>Overview</span>}
            </Link>
            <Link
              href="/tasks"
              className="flex items-center px-3 py-2 text-sm font-medium rounded-md text-slate-300 hover:bg-slate-800 hover:text-white"
            >
              <Database className={`${sidebarOpen ? "mr-3" : "mx-auto"} h-5 w-5`} />
              {sidebarOpen && <span>Ingestion Tasks</span>}
            </Link>
            <Link
              href="/reports"
              className="flex items-center px-3 py-2 text-sm font-medium rounded-md bg-slate-800 text-white"
            >
              <BarChart3 className={`${sidebarOpen ? "mr-3" : "mx-auto"} h-5 w-5`} />
              {sidebarOpen && <span>Reports</span>}
            </Link>
            <Link
              href="/settings"
              className="flex items-center px-3 py-2 text-sm font-medium rounded-md text-slate-300 hover:bg-slate-800 hover:text-white"
            >
              <Settings className={`${sidebarOpen ? "mr-3" : "mx-auto"} h-5 w-5`} />
              {sidebarOpen && <span>Settings</span>}
            </Link>
          </nav>
        </div>
        <div className="p-4 border-t border-slate-700">
          {sidebarOpen ? (
            <div className="flex items-center">
              <Avatar className="h-8 w-8">
                <AvatarImage src="/placeholder.svg" />
                <AvatarFallback>JD</AvatarFallback>
              </Avatar>
              <div className="ml-3">
                <p className="text-sm font-medium">John Doe</p>
                <p className="text-xs text-slate-400">Admin</p>
              </div>
            </div>
          ) : (
            <Avatar className="h-8 w-8 mx-auto">
              <AvatarImage src="/placeholder.svg" />
              <AvatarFallback>JD</AvatarFallback>
            </Avatar>
          )}
        </div>
      </div>

      {/* Main content */}
      <div className="flex-1 flex flex-col overflow-hidden">
        {/* Top navigation */}
        <header className="bg-white shadow-sm z-10">
          <div className="flex items-center justify-between p-4">
            <div className="flex items-center space-x-4">
              <h1 className="text-xl font-semibold">Reports</h1>
            </div>
            <div className="flex items-center space-x-4">
              <div className="relative">
                <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-slate-400" />
                <Input type="search" placeholder="Search reports..." className="w-64 pl-8 rounded-full bg-slate-50" />
              </div>
              <Button variant="ghost" size="icon" className="relative">
                <Bell className="h-5 w-5" />
                <span className="absolute top-0 right-0 h-2 w-2 rounded-full bg-red-500"></span>
              </Button>
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="ghost" className="relative h-8 w-8 rounded-full">
                    <Avatar className="h-8 w-8">
                      <AvatarImage src="/placeholder.svg" alt="User" />
                      <AvatarFallback>JD</AvatarFallback>
                    </Avatar>
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  <DropdownMenuLabel>My Account</DropdownMenuLabel>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem>Profile</DropdownMenuItem>
                  <DropdownMenuItem>Settings</DropdownMenuItem>
                  <DropdownMenuItem>Support</DropdownMenuItem>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem>Log out</DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          </div>
        </header>

        {/* Reports content */}
        <main className="flex-1 overflow-y-auto p-6">
          <Tabs defaultValue="dashboard">
            <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4 mb-6">
              <TabsList>
                <TabsTrigger value="dashboard">Dashboard</TabsTrigger>
                <TabsTrigger value="templates">Report Templates</TabsTrigger>
                <TabsTrigger value="saved">Saved Reports</TabsTrigger>
              </TabsList>

              <div className="flex items-center gap-2">
                <Button variant="outline" className="flex items-center gap-2">
                  <Download className="h-4 w-4" />
                  Export
                </Button>
                <Button variant="outline" className="flex items-center gap-2">
                  <Share2 className="h-4 w-4" />
                  Share
                </Button>
                <Button className="flex items-center gap-2">
                  <Save className="h-4 w-4" />
                  Save
                </Button>
              </div>
            </div>

            <TabsContent value="dashboard" className="mt-0">
              {/* Dashboard view */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-sm font-medium">Total Views</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">2.7M</div>
                    <p className="text-xs text-muted-foreground">+12.5% from last month</p>
                    <div className="h-[80px] mt-4">
                      <ChartContainer
                        config={{
                          value: {
                            label: "Views",
                            color: "hsl(var(--chart-1))",
                          },
                        }}
                        className="h-[80px]"
                      >
                        <ResponsiveContainer width="100%" height="100%">
                          <RechartsLineChart data={viewsData} margin={{ top: 5, right: 10, left: 10, bottom: 0 }}>
                            <Line
                              type="monotone"
                              dataKey="value"
                              stroke="var(--color-value)"
                              strokeWidth={2}
                              dot={false}
                            />
                          </RechartsLineChart>
                        </ResponsiveContainer>
                      </ChartContainer>
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-sm font-medium">Watch Time</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">157.3K hours</div>
                    <p className="text-xs text-muted-foreground">+8.2% from last month</p>
                    <div className="h-[80px] mt-4">
                      <ChartContainer
                        config={{
                          value: {
                            label: "Hours",
                            color: "hsl(var(--chart-2))",
                          },
                        }}
                        className="h-[80px]"
                      >
                        <ResponsiveContainer width="100%" height="100%">
                          <RechartsLineChart data={viewsData} margin={{ top: 5, right: 10, left: 10, bottom: 0 }}>
                            <Line
                              type="monotone"
                              dataKey="value"
                              stroke="var(--color-value)"
                              strokeWidth={2}
                              dot={false}
                            />
                          </RechartsLineChart>
                        </ResponsiveContainer>
                      </ChartContainer>
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-sm font-medium">Subscribers</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">124.5K</div>
                    <p className="text-xs text-muted-foreground">+5.1% from last month</p>
                    <div className="h-[80px] mt-4">
                      <ChartContainer
                        config={{
                          value: {
                            label: "Subscribers",
                            color: "hsl(var(--chart-3))",
                          },
                        }}
                        className="h-[80px]"
                      >
                        <ResponsiveContainer width="100%" height="100%">
                          <RechartsLineChart data={viewsData} margin={{ top: 5, right: 10, left: 10, bottom: 0 }}>
                            <Line
                              type="monotone"
                              dataKey="value"
                              stroke="var(--color-value)"
                              strokeWidth={2}
                              dot={false}
                            />
                          </RechartsLineChart>
                        </ResponsiveContainer>
                      </ChartContainer>
                    </div>
                  </CardContent>
                </Card>
              </div>

              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
                <Card>
                  <CardHeader>
                    <CardTitle>Views Trend</CardTitle>
                    <CardDescription>Daily view count over time</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <ChartContainer
                      config={{
                        value: {
                          label: "Views",
                          color: "hsl(var(--chart-1))",
                        },
                      }}
                      className="h-80"
                    >
                      <ResponsiveContainer width="100%" height="100%">
                        <RechartsLineChart data={viewsData} margin={{ top: 5, right: 30, left: 20, bottom: 5 }}>
                          <CartesianGrid strokeDasharray="3 3" />
                          <XAxis dataKey="name" />
                          <YAxis />
                          <ChartTooltip content={<ChartTooltipContent />} />
                          <Legend />
                          <Line
                            type="monotone"
                            dataKey="value"
                            stroke="var(--color-value)"
                            strokeWidth={2}
                            activeDot={{ r: 8 }}
                          />
                        </RechartsLineChart>
                      </ResponsiveContainer>
                    </ChartContainer>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle>Engagement Metrics</CardTitle>
                    <CardDescription>Likes, comments and shares</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <ChartContainer
                      config={{
                        likes: {
                          label: "Likes",
                          color: "hsl(var(--chart-1))",
                        },
                        comments: {
                          label: "Comments",
                          color: "hsl(var(--chart-2))",
                        },
                        shares: {
                          label: "Shares",
                          color: "hsl(var(--chart-3))",
                        },
                      }}
                      className="h-80"
                    >
                      <ResponsiveContainer width="100%" height="100%">
                        <RechartsBarChart data={engagementData} margin={{ top: 5, right: 30, left: 20, bottom: 5 }}>
                          <CartesianGrid strokeDasharray="3 3" />
                          <XAxis dataKey="name" />
                          <YAxis />
                          <ChartTooltip content={<ChartTooltipContent />} />
                          <Legend />
                          <Bar dataKey="likes" fill="var(--color-likes)" />
                          <Bar dataKey="comments" fill="var(--color-comments)" />
                          <Bar dataKey="shares" fill="var(--color-shares)" />
                        </RechartsBarChart>
                      </ResponsiveContainer>
                    </ChartContainer>
                  </CardContent>
                </Card>
              </div>

              <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                <Card className="lg:col-span-2">
                  <CardHeader>
                    <CardTitle>Geographic Distribution</CardTitle>
                    <CardDescription>Viewer location by country</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="h-[400px] flex items-center justify-center bg-slate-100 rounded-md">
                      <div className="text-center">
                        <Map className="h-16 w-16 text-slate-400 mx-auto mb-4" />
                        <h3 className="text-lg font-medium">Interactive Map</h3>
                        <p className="text-sm text-slate-500">Geographic distribution visualization</p>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle>Age Demographics</CardTitle>
                    <CardDescription>Viewer age distribution</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <ChartContainer
                      config={{
                        value: {
                          label: "Percentage",
                          color: "hsl(var(--chart-1))",
                        },
                      }}
                      className="h-[300px]"
                    >
                      <ResponsiveContainer width="100%" height="100%">
                        <RechartsPieChart>
                          <Pie
                            data={demographicsData}
                            cx="50%"
                            cy="50%"
                            labelLine={false}
                            outerRadius={80}
                            fill="#8884d8"
                            dataKey="value"
                            label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                          >
                            {demographicsData.map((entry, index) => (
                              <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                            ))}
                          </Pie>
                          <ChartTooltip content={<ChartTooltipContent />} />
                        </RechartsPieChart>
                      </ResponsiveContainer>
                    </ChartContainer>
                  </CardContent>
                </Card>
              </div>
            </TabsContent>

            <TabsContent value="templates" className="mt-0">
              {/* Templates view */}
              <div className="flex items-center justify-between mb-6">
                <div>
                  <h2 className="text-xl font-semibold">Report Templates</h2>
                  <p className="text-sm text-slate-500">Create and manage report templates</p>
                </div>
                <div className="flex items-center gap-2">
                  <div className="flex border rounded-md overflow-hidden">
                    <Button
                      variant={viewMode === "grid" ? "default" : "ghost"}
                      size="sm"
                      onClick={() => setViewMode("grid")}
                      className="rounded-none"
                    >
                      <LayoutGrid className="h-4 w-4" />
                    </Button>
                    <Button
                      variant={viewMode === "list" ? "default" : "ghost"}
                      size="sm"
                      onClick={() => setViewMode("list")}
                      className="rounded-none"
                    >
                      <LayoutList className="h-4 w-4" />
                    </Button>
                  </div>
                  <Button className="flex items-center gap-2">
                    <PlusCircle className="h-4 w-4" />
                    New Template
                  </Button>
                </div>
              </div>

              {viewMode === "grid" ? (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  {reportTemplates.map((template) => (
                    <Card key={template.id} className="overflow-hidden">
                      <div className="h-40 bg-slate-100 flex items-center justify-center">
                        <div className="grid grid-cols-2 gap-2 p-4 w-full">
                          <div className="bg-slate-200 rounded h-12"></div>
                          <div className="bg-slate-200 rounded h-12"></div>
                          <div className="bg-slate-200 rounded h-12 col-span-2"></div>
                          <div className="bg-slate-200 rounded h-12 col-span-2"></div>
                        </div>
                      </div>
                      <CardContent className="p-6">
                        <h3 className="font-semibold text-lg">{template.name}</h3>
                        <p className="text-sm text-slate-500 mt-1">{template.description}</p>
                      </CardContent>
                      <CardFooter className="flex items-center justify-between border-t p-4 bg-slate-50">
                        <div className="flex items-center text-sm text-slate-500">
                          <Clock className="h-4 w-4 mr-1" />
                          {template.lastUpdated}
                        </div>
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" size="sm">
                              <ChevronDown className="h-4 w-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuItem>
                              <Copy className="h-4 w-4 mr-2" />
                              Duplicate
                            </DropdownMenuItem>
                            <DropdownMenuItem>
                              <Share2 className="h-4 w-4 mr-2" />
                              Share
                            </DropdownMenuItem>
                            <DropdownMenuSeparator />
                            <DropdownMenuItem className="text-red-600">
                              <Trash className="h-4 w-4 mr-2" />
                              Delete
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </CardFooter>
                    </Card>
                  ))}
                </div>
              ) : (
                <Card>
                  <CardContent className="p-0">
                    <table className="w-full">
                      <thead>
                        <tr className="border-b">
                          <th className="text-left py-3 px-4 font-medium text-slate-500">Name</th>
                          <th className="text-left py-3 px-4 font-medium text-slate-500">Description</th>
                          <th className="text-left py-3 px-4 font-medium text-slate-500">Last Updated</th>
                          <th className="text-left py-3 px-4 font-medium text-slate-500">Actions</th>
                        </tr>
                      </thead>
                      <tbody>
                        {reportTemplates.map((template) => (
                          <tr key={template.id} className="border-b hover:bg-slate-50">
                            <td className="py-3 px-4 font-medium">{template.name}</td>
                            <td className="py-3 px-4 text-slate-500">{template.description}</td>
                            <td className="py-3 px-4 text-slate-500">{template.lastUpdated}</td>
                            <td className="py-3 px-4">
                              <div className="flex space-x-2">
                                <Button variant="ghost" size="sm" className="h-8 px-2">
                                  <Copy className="h-4 w-4 mr-1" />
                                  Duplicate
                                </Button>
                                <Button variant="ghost" size="sm" className="h-8 px-2">
                                  <Share2 className="h-4 w-4 mr-1" />
                                  Share
                                </Button>
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  className="h-8 px-2 text-red-600 hover:text-red-700 hover:bg-red-50"
                                >
                                  <Trash className="h-4 w-4 mr-1" />
                                  Delete
                                </Button>
                              </div>
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </CardContent>
                </Card>
              )}
            </TabsContent>

            <TabsContent value="saved" className="mt-0">
              <div className="flex flex-col items-center justify-center h-64 bg-slate-100 rounded-lg border border-dashed border-slate-300">
                <div className="text-center">
                  <BarChart3 className="h-12 w-12 text-slate-400 mx-auto mb-4" />
                  <h3 className="text-lg font-medium">No Saved Reports</h3>
                  <p className="text-sm text-slate-500 mb-4">You haven't saved any reports yet</p>
                  <Button>Create Your First Report</Button>
                </div>
              </div>
            </TabsContent>
          </Tabs>
        </main>
      </div>

      {/* Floating action button */}
      <div className="fixed bottom-6 right-6">
        <Button size="lg" className="rounded-full h-14 w-14 shadow-lg">
          <Plus className="h-6 w-6" />
        </Button>
      </div>
    </div>
  )
}
