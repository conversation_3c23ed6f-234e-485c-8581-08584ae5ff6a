"use client"

import { useState, useEffect } from "react"
import { use<PERSON>ara<PERSON> } from "next/navigation"
import Link from "next/link"
import Image from "next/image"
import { Calendar, Clock, User, ArrowLeft, Share2, Bookmark, ThumbsUp } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Card, CardContent } from "@/components/ui/card"
import { Separator } from "@/components/ui/separator"

// Blog post data (in a real app, this would come from a CMS or API)
const blogPosts = {
  "complete-guide-youtube-analytics": {
    id: "1",
    title: "The Complete Guide to YouTube Analytics: Understanding Your Channel's Performance",
    content: `YouTube analytics can seem overwhelming at first, but understanding your channel's performance data is crucial for growth and success. In this comprehensive guide, we'll break down everything you need to know about YouTube analytics, from basic metrics to advanced insights that can transform your content strategy.

## Understanding Core YouTube Metrics

### 1. Watch Time and Average View Duration
Watch time is arguably the most important metric on YouTube. It represents the total amount of time viewers spend watching your videos. YouTube's algorithm heavily favors videos with high watch time because it indicates engaging content that keeps viewers on the platform.

Average view duration shows how long, on average, viewers watch your videos. A higher average view duration suggests that your content is engaging and relevant to your audience. To improve this metric:

- Hook viewers in the first 15 seconds
- Create compelling thumbnails and titles
- Structure your content with clear segments
- Use pattern interrupts to maintain attention

### 2. Click-Through Rate (CTR)
Your click-through rate measures how often people click on your video after seeing the thumbnail and title. A good CTR varies by niche, but generally:
- 2-10% is considered good for most channels
- New channels often see lower CTRs initially
- CTR typically decreases as your video reaches broader audiences

### 3. Subscriber Growth and Retention
Monitor not just how many subscribers you gain, but also:
- Subscriber retention rates
- Where subscribers are coming from
- Which videos drive the most subscriptions
- Subscriber engagement levels

## Advanced Analytics Strategies

### Audience Retention Analysis
The audience retention graph shows you exactly when viewers drop off during your videos. Use this data to:
- Identify weak points in your content
- Understand which topics resonate most
- Optimize video pacing and structure
- Create more engaging introductions

### Traffic Source Analysis
Understanding where your views come from helps optimize your promotion strategy:
- YouTube Search: Focus on SEO optimization
- Suggested Videos: Create content similar to high-performing videos
- External Sources: Leverage social media and websites
- Direct Traffic: Build a loyal audience base

### Revenue Analytics
For monetized channels, revenue analytics provide insights into:
- RPM (Revenue Per Mille) trends
- Ad performance by video type
- Channel membership growth
- Super Chat and Super Thanks revenue

## Implementing Data-Driven Content Strategy

### Content Performance Analysis
Regularly analyze your top-performing content to identify patterns:
- Common topics or themes
- Optimal video lengths
- Best posting times
- Successful thumbnail styles

### Competitive Analysis
Use analytics to understand your competitive landscape:
- Compare growth rates with similar channels
- Analyze successful content in your niche
- Identify content gaps and opportunities
- Monitor trending topics and formats

### Seasonal Trends and Planning
YouTube analytics can help you identify seasonal patterns:
- Plan content around peak engagement periods
- Prepare for seasonal dips in viewership
- Capitalize on holiday and event-driven content
- Adjust posting schedules based on audience behavior

## Tools and Resources for Enhanced Analytics

### Third-Party Analytics Tools
While YouTube's native analytics are comprehensive, additional tools can provide deeper insights:
- VidIQ for keyword research and competitor analysis
- TubeBuddy for optimization and A/B testing
- Social Blade for historical data and projections
- Google Analytics for website traffic from YouTube

### Setting Up Custom Tracking
Implement advanced tracking for better insights:
- UTM parameters for external traffic
- Google Analytics goals for conversions
- Custom audiences for retargeting
- Conversion tracking for business objectives

## Common Analytics Mistakes to Avoid

### 1. Focusing Only on Vanity Metrics
Views and subscribers are important, but don't ignore:
- Engagement rates
- Watch time quality
- Conversion metrics
- Revenue per view

### 2. Not Acting on Data
Analytics are only valuable if you use them to make informed decisions:
- Set regular review schedules
- Create action plans based on insights
- Test and iterate based on data
- Document what works and what doesn't

### 3. Comparing Apples to Oranges
Ensure fair comparisons by considering:
- Video age and promotion levels
- Seasonal factors
- Algorithm changes
- Content type differences

## Future-Proofing Your Analytics Strategy

### Staying Updated with Platform Changes
YouTube regularly updates its analytics features and algorithm:
- Follow YouTube Creator Insider for updates
- Join creator communities for shared insights
- Attend YouTube events and workshops
- Experiment with new features early

### Building Long-Term Analytics Habits
Develop sustainable analytics practices:
- Create weekly and monthly review routines
- Set realistic goals and benchmarks
- Focus on trends rather than daily fluctuations
- Build a data-driven content calendar

## Conclusion

Mastering YouTube analytics is an ongoing process that requires consistent attention and analysis. By understanding your metrics, implementing data-driven strategies, and staying updated with platform changes, you can significantly improve your channel's performance and achieve your content goals.

Remember, analytics should inform your creative decisions, not replace your creative instincts. Use data as a guide to create better content that serves your audience while achieving your business objectives.`,
    author: {
      name: "Sarah Chen",
      avatar: "/images/authors/sarah-chen.jpg",
      bio: "YouTube Analytics Expert and Content Strategy Consultant with 8+ years of experience helping creators grow their channels."
    },
    publishDate: "2024-01-15",
    readTime: "12 min read",
    category: "Analytics",
    tags: ["YouTube Analytics", "Content Strategy", "Channel Growth", "Data Analysis"],
    featuredImage: "/images/blog/youtube-analytics-guide.jpg"
  },
  "youtube-seo-mastery-guide": {
    id: "2",
    title: "YouTube SEO Mastery: How to Rank Your Videos Higher in Search Results",
    content: `YouTube is the world's second-largest search engine, making SEO optimization crucial for video discovery and channel growth. This comprehensive guide will teach you everything you need to know about YouTube SEO, from basic optimization techniques to advanced strategies used by top creators.

## Understanding YouTube's Search Algorithm

### How YouTube Search Works
YouTube's search algorithm considers multiple factors when ranking videos:
- Relevance to the search query
- Video engagement metrics
- Video quality and freshness
- Channel authority and consistency
- User behavior and preferences

### The Importance of Keywords
Keywords are the foundation of YouTube SEO. They help YouTube understand what your video is about and when to show it to users. Effective keyword research involves:
- Understanding your audience's search behavior
- Identifying high-volume, low-competition keywords
- Using long-tail keywords for specific topics
- Analyzing competitor keyword strategies

## Comprehensive Keyword Research Strategy

### Tools for YouTube Keyword Research
1. **YouTube Search Suggest**: Start typing in YouTube's search bar to see autocomplete suggestions
2. **Google Keyword Planner**: Find search volumes and related keywords
3. **VidIQ**: Comprehensive YouTube SEO tool with keyword research features
4. **TubeBuddy**: Browser extension for YouTube optimization
5. **Ahrefs Keywords Explorer**: Advanced keyword research with YouTube-specific data

### Keyword Research Process
1. **Brainstorm seed keywords** related to your niche
2. **Expand your list** using keyword research tools
3. **Analyze search volume and competition** for each keyword
4. **Identify long-tail opportunities** with lower competition
5. **Group keywords by topic** for content planning

## Optimizing Video Elements for SEO

### Title Optimization
Your video title is one of the most important ranking factors:
- Include your primary keyword near the beginning
- Keep titles under 60 characters for full visibility
- Make titles compelling and click-worthy
- Use emotional triggers and power words
- Test different title variations

### Description Optimization
Video descriptions provide context and additional ranking opportunities:
- Write detailed descriptions (200+ words)
- Include primary and secondary keywords naturally
- Add timestamps for longer videos
- Include relevant links and calls-to-action
- Use the first 125 characters effectively (visible in search)

### Tags Strategy
While less important than before, tags still help with discoverability:
- Use 5-8 relevant tags per video
- Include your primary keyword as the first tag
- Mix broad and specific tags
- Use variations and synonyms of your main keywords
- Research competitor tags for inspiration

### Thumbnail Optimization
Thumbnails significantly impact click-through rates:
- Use high-contrast colors and clear imagery
- Include text overlays for context
- Maintain consistent branding across thumbnails
- Test different thumbnail styles
- Ensure thumbnails are readable on mobile devices

## Advanced YouTube SEO Techniques

### Video Content Optimization
The content itself plays a crucial role in SEO:
- Mention your target keywords in the video
- Create comprehensive content that fully covers the topic
- Encourage engagement through questions and calls-to-action
- Use closed captions and transcripts
- Structure content with clear sections and topics

### Playlist Optimization
Playlists can boost your SEO efforts:
- Create keyword-optimized playlist titles
- Write detailed playlist descriptions
- Organize videos logically within playlists
- Use playlists to target additional keywords
- Cross-promote playlists in video descriptions

### End Screens and Cards
These features can improve session duration:
- Promote related videos to keep viewers on your channel
- Use cards to highlight relevant content during the video
- Design end screens that encourage further viewing
- Link to playlists and subscribe buttons
- Analyze performance and optimize placement

## Technical SEO Considerations

### Video File Optimization
Optimize your video files before uploading:
- Use descriptive file names with keywords
- Choose appropriate video formats (MP4 recommended)
- Optimize video quality and file size
- Include metadata in video files
- Use consistent naming conventions

### Channel SEO
Your channel itself needs optimization:
- Create a keyword-rich channel description
- Use channel keywords in the about section
- Organize content into themed playlists
- Maintain consistent branding and messaging
- Optimize channel trailer for new visitors

### Closed Captions and Transcripts
These improve accessibility and SEO:
- Upload accurate closed captions
- Include keywords naturally in transcripts
- Use captions to improve user experience
- Consider multiple language captions for global reach
- Leverage auto-generated captions as a starting point

## Measuring and Improving SEO Performance

### Key SEO Metrics to Track
Monitor these metrics to gauge SEO success:
- Organic search traffic from YouTube
- Keyword rankings for target terms
- Click-through rates from search results
- Average view duration from search traffic
- Subscriber growth from search discovery

### SEO Analysis and Optimization
Regular analysis helps improve performance:
- Review YouTube Analytics search traffic data
- Identify top-performing keywords and content
- Analyze competitor SEO strategies
- Test and iterate on optimization techniques
- Document successful SEO practices

### Long-Term SEO Strategy
Build sustainable SEO practices:
- Create content clusters around main topics
- Develop topical authority in your niche
- Maintain consistent publishing schedules
- Build relationships with other creators
- Stay updated with YouTube algorithm changes

## Conclusion

YouTube SEO is a powerful tool for growing your channel and reaching new audiences. By implementing these strategies consistently and staying updated with best practices, you can significantly improve your video rankings and channel visibility.

Remember that SEO is a long-term strategy that requires patience and persistence. Focus on creating valuable content for your audience while optimizing for search engines, and you'll see sustainable growth over time.`,
    author: {
      name: "Marcus Rodriguez",
      avatar: "/images/authors/marcus-rodriguez.jpg",
      bio: "SEO Specialist and YouTube Growth Expert with over 10 years of experience in digital marketing and content optimization."
    },
    publishDate: "2024-01-10",
    readTime: "15 min read",
    category: "SEO",
    tags: ["YouTube SEO", "Video Optimization", "Keyword Research", "Search Rankings"],
    featuredImage: "/images/blog/youtube-seo-mastery.jpg"
  }
}

export default function BlogPostPage() {
  const params = useParams()
  const slug = params.slug as string
  const [post, setPost] = useState<any>(null)
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    // In a real app, you'd fetch the post from an API
    const foundPost = blogPosts[slug as keyof typeof blogPosts]
    setPost(foundPost)
    setIsLoading(false)
  }, [slug])

  if (isLoading) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
          <p>Loading article...</p>
        </div>
      </div>
    )
  }

  if (!post) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold mb-4">Article Not Found</h1>
          <p className="text-muted-foreground mb-6">The article you're looking for doesn't exist.</p>
          <Button asChild>
            <Link href="/blog">
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to Blog
            </Link>
          </Button>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-background">
      <div className="container mx-auto px-4 py-8 max-w-4xl">
        {/* Back Button */}
        <Button variant="ghost" asChild className="mb-6">
          <Link href="/blog">
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to Blog
          </Link>
        </Button>

        {/* Article Header */}
        <div className="mb-8">
          <div className="flex items-center gap-2 mb-4">
            <Badge variant="secondary">{post.category}</Badge>
            <span className="text-sm text-muted-foreground">•</span>
            <span className="text-sm text-muted-foreground">{post.readTime}</span>
          </div>
          
          <h1 className="text-3xl md:text-4xl font-bold mb-6">{post.title}</h1>
          
          <div className="flex items-center justify-between mb-6">
            <div className="flex items-center gap-4">
              <Avatar className="h-12 w-12">
                <AvatarImage src={post.author.avatar} />
                <AvatarFallback>{post.author.name.charAt(0)}</AvatarFallback>
              </Avatar>
              <div>
                <p className="font-medium">{post.author.name}</p>
                <p className="text-sm text-muted-foreground">{post.publishDate}</p>
              </div>
            </div>
            
            <div className="flex items-center gap-2">
              <Button variant="outline" size="sm">
                <Share2 className="h-4 w-4 mr-2" />
                Share
              </Button>
              <Button variant="outline" size="sm">
                <Bookmark className="h-4 w-4 mr-2" />
                Save
              </Button>
            </div>
          </div>

          {/* Featured Image */}
          <div className="mb-8">
            <Image
              src={post.featuredImage}
              alt={post.title}
              width={800}
              height={400}
              className="w-full h-64 md:h-96 object-cover rounded-lg"
              onError={(e) => {
                e.currentTarget.src = "/images/placeholder-blog.jpg"
              }}
            />
          </div>
        </div>

        {/* Article Content */}
        <div className="prose prose-lg max-w-none mb-12">
          {post.content.split('\n\n').map((paragraph: string, index: number) => {
            if (paragraph.startsWith('## ')) {
              return <h2 key={index} className="text-2xl font-bold mt-8 mb-4">{paragraph.replace('## ', '')}</h2>
            } else if (paragraph.startsWith('### ')) {
              return <h3 key={index} className="text-xl font-semibold mt-6 mb-3">{paragraph.replace('### ', '')}</h3>
            } else if (paragraph.startsWith('- ')) {
              const listItems = paragraph.split('\n').filter(item => item.startsWith('- '))
              return (
                <ul key={index} className="list-disc pl-6 mb-4">
                  {listItems.map((item, itemIndex) => (
                    <li key={itemIndex} className="mb-1">{item.replace('- ', '')}</li>
                  ))}
                </ul>
              )
            } else if (paragraph.trim()) {
              return <p key={index} className="mb-4 leading-relaxed">{paragraph}</p>
            }
            return null
          })}
        </div>

        <Separator className="mb-8" />

        {/* Author Bio */}
        <Card className="mb-8">
          <CardContent className="p-6">
            <div className="flex items-start gap-4">
              <Avatar className="h-16 w-16">
                <AvatarImage src={post.author.avatar} />
                <AvatarFallback>{post.author.name.charAt(0)}</AvatarFallback>
              </Avatar>
              <div className="flex-1">
                <h3 className="text-lg font-semibold mb-2">About {post.author.name}</h3>
                <p className="text-muted-foreground">{post.author.bio}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Tags */}
        <div className="mb-8">
          <h3 className="text-lg font-semibold mb-3">Tags</h3>
          <div className="flex flex-wrap gap-2">
            {post.tags.map((tag: string) => (
              <Badge key={tag} variant="outline">{tag}</Badge>
            ))}
          </div>
        </div>

        {/* Related Articles */}
        <div>
          <h3 className="text-lg font-semibold mb-4">Related Articles</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Card>
              <CardContent className="p-4">
                <h4 className="font-medium mb-2">YouTube Monetization Strategies</h4>
                <p className="text-sm text-muted-foreground">Learn how to maximize your revenue potential on YouTube.</p>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="p-4">
                <h4 className="font-medium mb-2">Content Creation Best Practices</h4>
                <p className="text-sm text-muted-foreground">Essential tips for creating engaging YouTube content.</p>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  )
}
