"use client"

import { useState } from "react"
import Link from "next/link"
import {
  Home,
  Database,
  BarChart3,
  Settings,
  Bell,
  Search,
  Plus,
  ChevronDown,
  Filter,
  RefreshCw,
  FileText,
  Calendar,
  ChevronLeft,
  ChevronRight,
} from "lucide-react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { Badge } from "@/components/ui/badge"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { Calendar as CalendarComponent } from "@/components/ui/calendar"
import { Checkbox } from "@/components/ui/checkbox"

// Sample task data
const tasks = [
  {
    id: "TASK-1234",
    type: "Full Sync",
    status: "Completed",
    started: "2023-06-15T14:30:00",
    duration: "45m 12s",
    source: "YouTube",
    destination: "BigQuery",
    records: 125000,
  },
  {
    id: "TASK-1233",
    type: "Incremental",
    status: "Running",
    started: "2023-06-15T13:15:00",
    duration: "--",
    source: "YouTube",
    destination: "BigQuery",
    records: 12500,
  },
  {
    id: "TASK-1232",
    type: "Incremental",
    status: "Failed",
    started: "2023-06-15T11:45:00",
    duration: "2m 18s",
    source: "YouTube",
    destination: "BigQuery",
    records: 0,
  },
  {
    id: "TASK-1231",
    type: "Full Sync",
    status: "Completed",
    started: "2023-06-14T22:10:00",
    duration: "1h 12m 33s",
    source: "YouTube",
    destination: "BigQuery",
    records: 245000,
  },
  {
    id: "TASK-1230",
    type: "Incremental",
    status: "Completed",
    started: "2023-06-14T18:45:00",
    duration: "32m 45s",
    source: "YouTube",
    destination: "BigQuery",
    records: 35000,
  },
  {
    id: "TASK-1229",
    type: "Incremental",
    status: "Pending",
    started: "2023-06-14T16:30:00",
    duration: "--",
    source: "YouTube",
    destination: "BigQuery",
    records: 0,
  },
  {
    id: "TASK-1228",
    type: "Full Sync",
    status: "Completed",
    started: "2023-06-14T12:15:00",
    duration: "58m 22s",
    source: "YouTube",
    destination: "BigQuery",
    records: 198000,
  },
  {
    id: "TASK-1227",
    type: "Incremental",
    status: "Completed",
    started: "2023-06-14T08:30:00",
    duration: "25m 10s",
    source: "YouTube",
    destination: "BigQuery",
    records: 28500,
  },
  {
    id: "TASK-1226",
    type: "Incremental",
    status: "Completed",
    started: "2023-06-14T04:15:00",
    duration: "28m 42s",
    source: "YouTube",
    destination: "BigQuery",
    records: 31200,
  },
]

export default function TasksPage() {
  const [sidebarOpen, setSidebarOpen] = useState(true)
  const [selectedDate, setSelectedDate] = useState<Date | undefined>(new Date())

  return (
    <div className="flex h-screen bg-slate-50">
      {/* Sidebar */}
      <div
        className={`bg-slate-900 text-white ${sidebarOpen ? "w-64" : "w-20"} transition-all duration-300 flex flex-col`}
      >
        <div className="p-4 flex items-center justify-between border-b border-slate-700">
          {/* <div className="flex items-center">
            <Database className="h-6 w-6 text-emerald-400" />
            {sidebarOpen && <span className="ml-2 font-bold">DataFlow</span>}
          </div> */}
          <Button
            variant="ghost"
            size="icon"
            onClick={() => setSidebarOpen(!sidebarOpen)}
            className="text-slate-400 hover:text-white hover:bg-slate-800"
          >
            <ChevronDown className={`h-5 w-5 transition-transform ${sidebarOpen ? "" : "rotate-180"}`} />
          </Button>
        </div>
        <div className="flex-1 py-4">
          <nav className="space-y-1 px-2">
            <Link
              href="/dashboard"
              className="flex items-center px-3 py-2 text-sm font-medium rounded-md text-slate-300 hover:bg-slate-800 hover:text-white"
            >
              <Home className={`${sidebarOpen ? "mr-3" : "mx-auto"} h-5 w-5`} />
              {sidebarOpen && <span>Overview</span>}
            </Link>
            <Link
              href="/tasks"
              className="flex items-center px-3 py-2 text-sm font-medium rounded-md bg-slate-800 text-white"
            >
              <Database className={`${sidebarOpen ? "mr-3" : "mx-auto"} h-5 w-5`} />
              {sidebarOpen && <span>Ingestion Tasks</span>}
            </Link>
            <Link
              href="/reports"
              className="flex items-center px-3 py-2 text-sm font-medium rounded-md text-slate-300 hover:bg-slate-800 hover:text-white"
            >
              <BarChart3 className={`${sidebarOpen ? "mr-3" : "mx-auto"} h-5 w-5`} />
              {sidebarOpen && <span>Reports</span>}
            </Link>
            <Link
              href="/settings"
              className="flex items-center px-3 py-2 text-sm font-medium rounded-md text-slate-300 hover:bg-slate-800 hover:text-white"
            >
              <Settings className={`${sidebarOpen ? "mr-3" : "mx-auto"} h-5 w-5`} />
              {sidebarOpen && <span>Settings</span>}
            </Link>
          </nav>
        </div>
        <div className="p-4 border-t border-slate-700">
          {sidebarOpen ? (
            <div className="flex items-center">
              <Avatar className="h-8 w-8">
                <AvatarImage src="/placeholder.svg" />
                <AvatarFallback>JD</AvatarFallback>
              </Avatar>
              <div className="ml-3">
                <p className="text-sm font-medium">John Doe</p>
                <p className="text-xs text-slate-400">Admin</p>
              </div>
            </div>
          ) : (
            <Avatar className="h-8 w-8 mx-auto">
              <AvatarImage src="/placeholder.svg" />
              <AvatarFallback>JD</AvatarFallback>
            </Avatar>
          )}
        </div>
      </div>

      {/* Main content */}
      <div className="flex-1 flex flex-col overflow-hidden">
        {/* Top navigation */}
        <header className="bg-white shadow-sm z-10">
          <div className="flex items-center justify-between p-4">
            <div className="flex items-center space-x-4">
              <h1 className="text-xl font-semibold">Ingestion Tasks</h1>
            </div>
            <div className="flex items-center space-x-4">
              <div className="relative">
                <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-slate-400" />
                <Input type="search" placeholder="Search tasks..." className="w-64 pl-8 rounded-full bg-slate-50" />
              </div>
              <Button variant="ghost" size="icon" className="relative">
                <Bell className="h-5 w-5" />
                <span className="absolute top-0 right-0 h-2 w-2 rounded-full bg-red-500"></span>
              </Button>
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="ghost" className="relative h-8 w-8 rounded-full">
                    <Avatar className="h-8 w-8">
                      <AvatarImage src="/placeholder.svg" alt="User" />
                      <AvatarFallback>JD</AvatarFallback>
                    </Avatar>
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  <DropdownMenuLabel>My Account</DropdownMenuLabel>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem>Profile</DropdownMenuItem>
                  <DropdownMenuItem>Settings</DropdownMenuItem>
                  <DropdownMenuItem>Support</DropdownMenuItem>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem>Log out</DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          </div>
        </header>

        {/* Task management content */}
        <main className="flex-1 overflow-y-auto p-6">
          <Card>
            <CardHeader>
              <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
                <div>
                  <CardTitle>Data Ingestion Tasks</CardTitle>
                  <CardDescription>Manage and monitor your data collection tasks</CardDescription>
                </div>
                <div className="flex flex-col sm:flex-row gap-2">
                  <Button variant="outline" className="flex items-center gap-2">
                    <RefreshCw className="h-4 w-4" />
                    Refresh
                  </Button>
                  <Button className="flex items-center gap-2">
                    <Plus className="h-4 w-4" />
                    New Task
                  </Button>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              {/* Filters */}
              <div className="flex flex-col md:flex-row gap-4 mb-6">
                <div className="flex items-center gap-2">
                  <Select defaultValue="all">
                    <SelectTrigger className="w-[180px]">
                      <SelectValue placeholder="Status" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Statuses</SelectItem>
                      <SelectItem value="completed">Completed</SelectItem>
                      <SelectItem value="running">Running</SelectItem>
                      <SelectItem value="failed">Failed</SelectItem>
                      <SelectItem value="pending">Pending</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="flex items-center gap-2">
                  <Select defaultValue="all">
                    <SelectTrigger className="w-[180px]">
                      <SelectValue placeholder="Task Type" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Types</SelectItem>
                      <SelectItem value="full">Full Sync</SelectItem>
                      <SelectItem value="incremental">Incremental</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="flex items-center gap-2">
                  <Popover>
                    <PopoverTrigger asChild>
                      <Button variant="outline" className="w-[240px] justify-start text-left font-normal">
                        <Calendar className="mr-2 h-4 w-4" />
                        {selectedDate ? selectedDate.toDateString() : "Pick a date"}
                      </Button>
                    </PopoverTrigger>
                    <PopoverContent className="w-auto p-0">
                      <CalendarComponent
                        mode="single"
                        selected={selectedDate}
                        onSelect={setSelectedDate}
                        initialFocus
                      />
                    </PopoverContent>
                  </Popover>
                </div>

                <div className="flex-1 flex justify-end">
                  <Button variant="outline" className="flex items-center gap-2">
                    <Filter className="h-4 w-4" />
                    More Filters
                  </Button>
                </div>
              </div>

              {/* Tasks table */}
              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead>
                    <tr className="border-b">
                      <th className="text-left py-3 px-4 font-medium text-slate-500">
                        <div className="flex items-center gap-2">
                          <Checkbox id="select-all" />
                          <label
                            htmlFor="select-all"
                            className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                          >
                            Task ID
                          </label>
                        </div>
                      </th>
                      <th className="text-left py-3 px-4 font-medium text-slate-500">Type</th>
                      <th className="text-left py-3 px-4 font-medium text-slate-500">Status</th>
                      <th className="text-left py-3 px-4 font-medium text-slate-500">Started</th>
                      <th className="text-left py-3 px-4 font-medium text-slate-500">Duration</th>
                      <th className="text-left py-3 px-4 font-medium text-slate-500">Source</th>
                      <th className="text-left py-3 px-4 font-medium text-slate-500">Destination</th>
                      <th className="text-left py-3 px-4 font-medium text-slate-500">Records</th>
                      <th className="text-left py-3 px-4 font-medium text-slate-500">Actions</th>
                    </tr>
                  </thead>
                  <tbody>
                    {tasks.map((task) => (
                      <tr key={task.id} className="border-b hover:bg-slate-50">
                        <td className="py-3 px-4">
                          <div className="flex items-center gap-2">
                            <Checkbox id={`select-${task.id}`} />
                            <label
                              htmlFor={`select-${task.id}`}
                              className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                            >
                              {task.id}
                            </label>
                          </div>
                        </td>
                        <td className="py-3 px-4">{task.type}</td>
                        <td className="py-3 px-4">
                          <Badge
                            className={`
                            ${task.status === "Completed" ? "bg-emerald-100 text-emerald-800 hover:bg-emerald-100" : ""}
                            ${task.status === "Running" ? "bg-blue-100 text-blue-800 hover:bg-blue-100" : ""}
                            ${task.status === "Failed" ? "bg-red-100 text-red-800 hover:bg-red-100" : ""}
                            ${task.status === "Pending" ? "bg-amber-100 text-amber-800 hover:bg-amber-100" : ""}
                          `}
                          >
                            {task.status}
                          </Badge>
                        </td>
                        <td className="py-3 px-4">{new Date(task.started).toLocaleString()}</td>
                        <td className="py-3 px-4">{task.duration}</td>
                        <td className="py-3 px-4">{task.source}</td>
                        <td className="py-3 px-4">{task.destination}</td>
                        <td className="py-3 px-4">{task.records.toLocaleString()}</td>
                        <td className="py-3 px-4">
                          <div className="flex space-x-2">
                            {task.status === "Failed" && (
                              <Button variant="ghost" size="sm" className="h-8 px-2">
                                <RefreshCw className="h-4 w-4 mr-1" />
                                Retry
                              </Button>
                            )}
                            <Button variant="ghost" size="sm" className="h-8 px-2">
                              <FileText className="h-4 w-4 mr-1" />
                              Logs
                            </Button>
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>

              {/* Pagination */}
              <div className="flex items-center justify-between mt-6">
                <div className="text-sm text-slate-500">
                  Showing <span className="font-medium">1</span> to <span className="font-medium">10</span> of{" "}
                  <span className="font-medium">42</span> results
                </div>
                <div className="flex items-center space-x-2">
                  <Button variant="outline" size="sm" className="h-8 w-8 p-0">
                    <ChevronLeft className="h-4 w-4" />
                  </Button>
                  <Button variant="outline" size="sm" className="h-8 w-8 p-0 bg-slate-100">
                    1
                  </Button>
                  <Button variant="outline" size="sm" className="h-8 w-8 p-0">
                    2
                  </Button>
                  <Button variant="outline" size="sm" className="h-8 w-8 p-0">
                    3
                  </Button>
                  <Button variant="outline" size="sm" className="h-8 w-8 p-0">
                    <ChevronRight className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        </main>
      </div>

      {/* Floating action button */}
      <div className="fixed bottom-6 right-6">
        <Button size="lg" className="rounded-full h-14 w-14 shadow-lg">
          <Plus className="h-6 w-6" />
        </Button>
      </div>
    </div>
  )
}
