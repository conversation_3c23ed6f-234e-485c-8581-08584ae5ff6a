"use client"

import Link from "next/link"
import { BookOpen, Download, ExternalLink, FileText, Video, Headphones, Users, Star, ArrowRight } from "lucide-react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs"

// Resource data structure
interface Resource {
  id: string
  title: string
  description: string
  type: "guide" | "template" | "tool" | "video" | "podcast" | "ebook"
  category: string
  difficulty: "Beginner" | "Intermediate" | "Advanced"
  rating: number
  downloads: number
  url: string
  isExternal?: boolean
}

// Comprehensive resources data with extensive YouTube analytics resources
const resources: Resource[] = [
  {
    id: "1",
    title: "YouTube Analytics Dashboard Template",
    description: "Professional Excel template for tracking and analyzing your YouTube channel performance with automated charts, KPI calculations, and growth projections. Includes 15+ key metrics tracking.",
    type: "template",
    category: "Analytics",
    difficulty: "Intermediate",
    rating: 4.8,
    downloads: 12500,
    url: "/downloads/youtube-analytics-template.xlsx"
  },
  {
    id: "2",
    title: "Content Calendar Planning Guide",
    description: "Comprehensive 50-page guide to planning and organizing your YouTube content strategy with templates, seasonal planning, and performance optimization best practices.",
    type: "guide",
    category: "Content Strategy",
    difficulty: "Beginner",
    rating: 4.9,
    downloads: 18200,
    url: "/guides/content-calendar-planning"
  },
  {
    id: "3",
    title: "YouTube SEO Optimization Checklist",
    description: "Complete checklist covering all aspects of YouTube SEO optimization from keyword research to video optimization.",
    type: "template",
    category: "SEO",
    difficulty: "Intermediate",
    rating: 4.7,
    downloads: 15600,
    url: "/downloads/youtube-seo-checklist.pdf"
  },
  {
    id: "4",
    title: "Channel Growth Masterclass",
    description: "In-depth video course covering advanced strategies for growing your YouTube channel and increasing engagement.",
    type: "video",
    category: "Growth",
    difficulty: "Advanced",
    rating: 4.9,
    downloads: 8900,
    url: "/courses/channel-growth-masterclass"
  },
  {
    id: "5",
    title: "YouTube Analytics Podcast Series",
    description: "Weekly podcast featuring interviews with successful YouTubers and analytics experts sharing their insights and strategies.",
    type: "podcast",
    category: "Analytics",
    difficulty: "Intermediate",
    rating: 4.6,
    downloads: 25000,
    url: "/podcast/youtube-analytics-series"
  },
  {
    id: "6",
    title: "Monetization Strategy Ebook",
    description: "Complete 80-page guide to YouTube monetization covering ad revenue optimization, sponsorship negotiations, merchandise strategies, and 15+ alternative income streams.",
    type: "ebook",
    category: "Monetization",
    difficulty: "Intermediate",
    rating: 4.8,
    downloads: 11200,
    url: "/downloads/monetization-strategy-ebook.pdf"
  },
  {
    id: "7",
    title: "Thumbnail Design Templates Pack",
    description: "Professional Photoshop and Canva templates for creating high-converting YouTube thumbnails. Includes 50+ designs across different niches and styles.",
    type: "template",
    category: "Design",
    difficulty: "Beginner",
    rating: 4.7,
    downloads: 9800,
    url: "/downloads/thumbnail-templates-pack.zip"
  },
  {
    id: "8",
    title: "YouTube Algorithm Optimization Guide",
    description: "Comprehensive 60-page guide explaining how YouTube's algorithm works and proven strategies to increase your video's reach and recommendations.",
    type: "guide",
    category: "Growth",
    difficulty: "Advanced",
    rating: 4.9,
    downloads: 14500,
    url: "/guides/algorithm-optimization"
  },
  {
    id: "9",
    title: "Channel Audit Checklist",
    description: "Professional 40-point checklist for conducting comprehensive YouTube channel audits. Identify optimization opportunities and growth bottlenecks.",
    type: "template",
    category: "Analytics",
    difficulty: "Intermediate",
    rating: 4.6,
    downloads: 7300,
    url: "/downloads/channel-audit-checklist.pdf"
  },
  {
    id: "10",
    title: "Live Streaming Setup Guide",
    description: "Complete technical guide for setting up professional live streams on YouTube. Covers equipment, software, and optimization techniques.",
    type: "guide",
    category: "Technical",
    difficulty: "Advanced",
    rating: 4.8,
    downloads: 6900,
    url: "/guides/live-streaming-setup"
  },
  {
    id: "11",
    title: "Audience Retention Analysis Tool",
    description: "Interactive web tool for analyzing audience retention patterns and identifying content optimization opportunities based on your YouTube Analytics data.",
    type: "tool",
    category: "Analytics",
    difficulty: "Intermediate",
    rating: 4.7,
    downloads: 5200,
    url: "/tools/retention-analyzer",
    isExternal: true
  },
  {
    id: "12",
    title: "YouTube Shorts Strategy Masterclass",
    description: "Video course covering everything about YouTube Shorts: creation, optimization, algorithm understanding, and integration with long-form content strategy.",
    type: "video",
    category: "Content Strategy",
    difficulty: "Intermediate",
    rating: 4.8,
    downloads: 8100,
    url: "/courses/youtube-shorts-masterclass"
  }
]

const getTypeIcon = (type: string) => {
  switch (type) {
    case "guide": return <BookOpen className="h-5 w-5" />
    case "template": return <FileText className="h-5 w-5" />
    case "tool": return <ExternalLink className="h-5 w-5" />
    case "video": return <Video className="h-5 w-5" />
    case "podcast": return <Headphones className="h-5 w-5" />
    case "ebook": return <Download className="h-5 w-5" />
    default: return <FileText className="h-5 w-5" />
  }
}

const getDifficultyColor = (difficulty: string) => {
  switch (difficulty.toLowerCase()) {
    case "beginner": return "bg-green-100 text-green-800"
    case "intermediate": return "bg-yellow-100 text-yellow-800"
    case "advanced": return "bg-red-100 text-red-800"
    default: return "bg-gray-100 text-gray-800"
  }
}

export default function ResourcesPage() {
  const categories = ["All", "Analytics", "SEO", "Content Strategy", "Growth", "Monetization", "Design", "Technical"]
  const types = ["All", "Guide", "Template", "Tool", "Video", "Podcast", "Ebook"]

  return (
    <div className="min-h-screen bg-background">
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="text-center mb-12">
          <h1 className="text-4xl md:text-5xl font-bold mb-4">YouTube Creator Resources</h1>
          <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
            Everything you need to succeed on YouTube. From analytics templates to growth strategies, 
            discover tools and resources used by top creators to build successful channels.
          </p>
        </div>

        {/* Stats */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-12">
          <Card>
            <CardContent className="p-4 text-center">
              <Download className="h-8 w-8 mx-auto mb-2 text-primary" />
              <div className="text-2xl font-bold">150K+</div>
              <div className="text-sm text-muted-foreground">Total Downloads</div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-4 text-center">
              <FileText className="h-8 w-8 mx-auto mb-2 text-primary" />
              <div className="text-2xl font-bold">75+</div>
              <div className="text-sm text-muted-foreground">Resources Available</div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-4 text-center">
              <Star className="h-8 w-8 mx-auto mb-2 text-primary" />
              <div className="text-2xl font-bold">4.8</div>
              <div className="text-sm text-muted-foreground">Average Rating</div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-4 text-center">
              <Users className="h-8 w-8 mx-auto mb-2 text-primary" />
              <div className="text-2xl font-bold">15K+</div>
              <div className="text-sm text-muted-foreground">Active Users</div>
            </CardContent>
          </Card>
        </div>

        {/* Resource Categories */}
        <Tabs defaultValue="all" className="mb-12">
          <TabsList className="grid w-full grid-cols-3 lg:grid-cols-6">
            {categories.map((category) => (
              <TabsTrigger key={category} value={category.toLowerCase()}>
                {category}
              </TabsTrigger>
            ))}
          </TabsList>

          <TabsContent value="all" className="mt-8">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {resources.map((resource) => (
                <Card key={resource.id} className="hover:shadow-lg transition-shadow">
                  <CardHeader className="pb-3">
                    <div className="flex items-center justify-between mb-2">
                      <div className="flex items-center gap-2">
                        {getTypeIcon(resource.type)}
                        <Badge variant="outline">{resource.type}</Badge>
                      </div>
                      <Badge className={getDifficultyColor(resource.difficulty)}>
                        {resource.difficulty}
                      </Badge>
                    </div>
                    <CardTitle className="text-lg line-clamp-2">{resource.title}</CardTitle>
                    <CardDescription className="line-clamp-3">{resource.description}</CardDescription>
                  </CardHeader>
                  <CardContent className="pt-0">
                    <div className="flex items-center justify-between text-sm text-muted-foreground mb-4">
                      <div className="flex items-center gap-1">
                        <Star className="h-4 w-4 fill-yellow-400 text-yellow-400" />
                        <span>{resource.rating}</span>
                      </div>
                      <div className="flex items-center gap-1">
                        <Download className="h-4 w-4" />
                        <span>{(resource.downloads / 1000).toFixed(1)}K</span>
                      </div>
                    </div>
                    <Button asChild className="w-full">
                      <Link href={resource.url}>
                        {resource.type === "template" || resource.type === "ebook" ? "Download" : "Access"}
                        <ArrowRight className="ml-2 h-4 w-4" />
                      </Link>
                    </Button>
                  </CardContent>
                </Card>
              ))}
            </div>
          </TabsContent>

          {/* Individual category tabs would filter resources */}
          {categories.slice(1).map((category) => (
            <TabsContent key={category} value={category.toLowerCase()} className="mt-8">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {resources
                  .filter((resource) => resource.category === category)
                  .map((resource) => (
                    <Card key={resource.id} className="hover:shadow-lg transition-shadow">
                      <CardHeader className="pb-3">
                        <div className="flex items-center justify-between mb-2">
                          <div className="flex items-center gap-2">
                            {getTypeIcon(resource.type)}
                            <Badge variant="outline">{resource.type}</Badge>
                          </div>
                          <Badge className={getDifficultyColor(resource.difficulty)}>
                            {resource.difficulty}
                          </Badge>
                        </div>
                        <CardTitle className="text-lg line-clamp-2">{resource.title}</CardTitle>
                        <CardDescription className="line-clamp-3">{resource.description}</CardDescription>
                      </CardHeader>
                      <CardContent className="pt-0">
                        <div className="flex items-center justify-between text-sm text-muted-foreground mb-4">
                          <div className="flex items-center gap-1">
                            <Star className="h-4 w-4 fill-yellow-400 text-yellow-400" />
                            <span>{resource.rating}</span>
                          </div>
                          <div className="flex items-center gap-1">
                            <Download className="h-4 w-4" />
                            <span>{(resource.downloads / 1000).toFixed(1)}K</span>
                          </div>
                        </div>
                        <Button asChild className="w-full">
                          <Link href={resource.url}>
                            {resource.type === "template" || resource.type === "ebook" ? "Download" : "Access"}
                            <ArrowRight className="ml-2 h-4 w-4" />
                          </Link>
                        </Button>
                      </CardContent>
                    </Card>
                  ))}
              </div>
            </TabsContent>
          ))}
        </Tabs>

        {/* Featured Resources */}
        <div className="mb-12">
          <h2 className="text-2xl font-bold mb-6">Featured Resources</h2>
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            <Card className="border-2 border-primary/20">
              <CardContent className="p-6">
                <div className="flex items-start gap-4">
                  <div className="bg-primary/10 text-primary p-3 rounded-full">
                    <BookOpen className="h-6 w-6" />
                  </div>
                  <div className="flex-1">
                    <h3 className="text-lg font-semibold mb-2">Complete YouTube Analytics Course</h3>
                    <p className="text-muted-foreground mb-4">
                      Master YouTube analytics with our comprehensive course covering everything from basic metrics 
                      to advanced growth strategies.
                    </p>
                    <div className="flex items-center gap-4 mb-4">
                      <Badge>Free</Badge>
                      <div className="flex items-center gap-1 text-sm">
                        <Star className="h-4 w-4 fill-yellow-400 text-yellow-400" />
                        <span>4.9</span>
                      </div>
                      <span className="text-sm text-muted-foreground">12 hours</span>
                    </div>
                    <Button asChild>
                      <Link href="/courses/youtube-analytics">
                        Start Course
                      </Link>
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="border-2 border-primary/20">
              <CardContent className="p-6">
                <div className="flex items-start gap-4">
                  <div className="bg-primary/10 text-primary p-3 rounded-full">
                    <Users className="h-6 w-6" />
                  </div>
                  <div className="flex-1">
                    <h3 className="text-lg font-semibold mb-2">Creator Community</h3>
                    <p className="text-muted-foreground mb-4">
                      Join our exclusive community of YouTube creators to share insights, get feedback, 
                      and collaborate on growth strategies.
                    </p>
                    <div className="flex items-center gap-4 mb-4">
                      <Badge>Premium</Badge>
                      <span className="text-sm text-muted-foreground">5,000+ members</span>
                    </div>
                    <Button asChild>
                      <Link href="/community">
                        Join Community
                      </Link>
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>

        {/* CTA Section */}
        <div className="text-center">
          <Card className="bg-gradient-to-r from-primary/10 to-primary/5 border-primary/20">
            <CardContent className="p-8">
              <h2 className="text-2xl font-bold mb-4">Need Custom Resources?</h2>
              <p className="text-muted-foreground mb-6 max-w-2xl mx-auto">
                Can't find what you're looking for? Our team can create custom templates, guides, 
                and tools tailored to your specific YouTube goals and requirements.
              </p>
              <Button size="lg">
                Request Custom Resource
              </Button>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}
