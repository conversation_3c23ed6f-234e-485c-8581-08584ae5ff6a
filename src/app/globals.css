@import './theme.css';
@tailwind base;
@tailwind components;
@tailwind utilities;

body {
  font-family: Arial, Helvetica, sans-serif;
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}

@layer components {
  .tool-card {
    @apply relative bg-card border border-border p-5 rounded-xl transition-all duration-300;
  }
  
  .tool-card:hover {
    @apply shadow-md border-primary/20 -translate-y-1;
  }

  .tool-card-featured {
    @apply relative bg-gradient-to-br from-accent to-background border border-primary/20 p-5 rounded-xl transition-all duration-300;
  }
  
  .tool-card-featured:hover {
    @apply shadow-lg border-primary/40 -translate-y-1;
  }

  .badge {
    @apply inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2;
  }

  /* 示例：为 badge-free 添加暗色模式样式 */
  .badge-free {
    /* 浅色模式 */
    @apply badge bg-green-100 text-green-800 border-transparent;
    /* 暗色模式 */
    @apply dark:bg-green-900 dark:text-green-300;
  }

  /* 示例：为 badge-premium 添加暗色模式样式 */
  .badge-premium {
    /* 浅色模式 */
    @apply badge bg-amber-100 text-amber-800 border-transparent;
    /* 暗色模式 */
    @apply dark:bg-amber-900 dark:text-amber-300;
  }

  /* 你可以根据需要为 .badge-new 和 .badge-featured 添加类似的暗色模式样式 */
  .badge-new {
    /* 浅色模式 */
    @apply badge bg-blue-100 text-blue-800 border-transparent;
    /* 暗色模式 */
    @apply dark:bg-blue-900 dark:text-blue-300;
  }

  .badge-featured {
     /* 浅色模式 */
    @apply badge bg-purple-100 text-purple-800 border-transparent;
    /* 暗色模式 */
    @apply dark:bg-purple-900 dark:text-purple-300;
  }

  .badge-new {
    @apply badge bg-green-500 text-white border-transparent;
  }

  .badge-featured {
    @apply badge bg-amber-500 text-white border-transparent;
  }

  .badge-free {
    @apply badge bg-green-500 text-white border-transparent;
  }

  .badge-premium {
    @apply badge bg-amber-500 text-white border-transparent;
  }

  .nav-link {
    @apply flex items-center gap-2 rounded-md px-3 py-2 text-sm font-medium transition-colors hover:bg-accent hover:text-accent-foreground;
  }

  .nav-link-active {
    @apply bg-accent text-accent-foreground;
  }

  .search-input {
    @apply w-full bg-background/70 backdrop-blur-sm rounded-full px-5 py-3 border border-border focus:border-primary focus:ring-1 focus:ring-primary focus:outline-none text-base;
  }

  /* 检查 glass-card 和 glass-effect 是否需要暗色模式调整 */
  .glass-card {
    @apply bg-white/70 backdrop-blur-sm border border-white/20 shadow-sm dark:bg-black/50 dark:border-white/10;
  }

  .glass-effect {
    @apply bg-white/80 backdrop-blur-md dark:bg-black/60;
  }
  
  /* Improved category button styles */
  .category-button {
    @apply flex items-center gap-2 px-4 py-3 rounded-xl border border-border bg-card hover:border-primary/30 hover:shadow-sm transition-all;
  }
  
  .category-button-active {
    @apply border-primary/50 bg-accent shadow-sm;
  }
  
  /* Better form controls */
  .form-input {
    @apply rounded-md border border-input px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary transition-all;
  }
  
  /* Animation utilities */
  .hover-scale {
    @apply transition-transform duration-200 hover:scale-105;
  }
  
  .hover-lift {
    @apply transition-all duration-300 hover:-translate-y-1 hover:shadow-md;
  }
}


@layer base {
  body {
    @apply bg-background text-foreground;
  }
}
