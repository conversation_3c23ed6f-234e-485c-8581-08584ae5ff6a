import { NextRequest, NextResponse } from 'next/server';
import { PrismaClient } from '@prisma/client';

// 创建 Prisma 客户端实例
let prisma: PrismaClient;

// 优化Prisma连接处理
try {
  prisma = new PrismaClient({
    log: ['error'],
    // 添加连接超时设置
    datasources: {
      db: {
        url: process.env.DATABASE_URL
      }
    }
  });
} catch (e) {
  console.error('初始化Prisma客户端失败:', e);
  // 创建一个不带额外配置的客户端作为后备
  prisma = new PrismaClient();
}

// 定义视频对象的接口
interface VideoItem {
  video_id: string;
  title: string;
  [key: string]: any; // 允许其他属性
}

// 模拟数据生成函数 - 当数据库不可用时使用
function generateMockVideos(channelId: string, page: number, limit: number) {
  const startIndex = (page - 1) * limit;
  const mockVideos = [];
  
  for (let i = 0; i < limit; i++) {
    const index = startIndex + i;
    const date = new Date();
    date.setDate(date.getDate() - index); // 每个视频日期不同
    
    mockVideos.push({
      id: `mock-${index}`,
      channelId: channelId,
      youtubeId: `mock-video-${index}`,
      title: `模拟视频 #${index} (数据库暂不可用)`,
      description: '这是一个模拟视频，由于数据库连接问题而生成',
      publishedAt: date.toISOString(),
      thumbnail: null,
      duration: '0:00',
      viewCount: Math.floor(Math.random() * 10000),
      likeCount: Math.floor(Math.random() * 1000),
      commentCount: Math.floor(Math.random() * 100)
    });
  }
  
  return mockVideos;
}

/**
 * 处理视频信息查询的 API 路由
 * 接收 videoId 参数，返回视频详细信息
 */
export async function GET(request: NextRequest) {
  try {
    // 获取 URL 查询参数
    const { searchParams } = new URL(request.url);
    const videoId = searchParams.get('videoId');
    const channelId = searchParams.get('channelId');
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '12');
    const sortBy = searchParams.get('sortBy') || 'publishedAt';

    // 验证参数 - 必须提供videoId或channelId其中一个
    if (!videoId && !channelId) {
      return NextResponse.json(
        { error: 'Missing videoId or channelId parameter' },
        { status: 400 }
      );
    }

    // 先检查数据库连接状态
    let isDatabaseAvailable = true;
    try {
      // 执行一个简单查询来检查数据库是否可用
      await prisma.$queryRaw`SELECT 1`;
    } catch (connectionError) {
      console.error('[API] 数据库连接不可用:', connectionError);
      isDatabaseAvailable = false;
    }

    // 处理通过videoId获取单个视频的情况
    if (videoId) {
      console.log(`[API] 正在获取视频信息，videoId: ${videoId}`);

      // 如果数据库不可用，返回模拟数据
      if (!isDatabaseAvailable) {
        console.log('[API] 数据库不可用，返回模拟视频数据');
        return NextResponse.json({
          videoId: videoId,
          title: '模拟视频 (数据库暂不可用)',
          description: '这是一个模拟视频，由于数据库连接问题而生成',
          publishedAt: new Date().toISOString(),
          channelId: 'mock-channel',
          channelTitle: '模拟频道',
          thumbnails: { 
            default: { url: '' } 
          },
          statistics: {
            viewCount: '0',
            likeCount: '0',
            commentCount: '0'
          },
          _dbStatus: 'unavailable'
        });
      }

      try {
        // 首先查询数据库，检查是否已有该视频相关的频道数据
        console.log(`[API] 尝试从数据库查询视频: ${videoId}`);
        
        // 查询视频
        const video = await prisma.ytbVideo.findFirst({
          where: {
            youtubeId: videoId,
            deletedAt: null
          }
        });

        // 如果找到了视频，查询对应的频道
        if (video) {
          try {
            let channel;
            
            // 尝试查询频道 - 处理不同类型的channelId
            if (typeof video.channelId === 'bigint') {
              // 如果已经是bigint类型
              channel = await prisma.ytbChannel.findUnique({
                where: { id: video.channelId }
              });
            } else if (typeof video.channelId === 'string' && /^\d+$/.test(video.channelId)) {
              // 如果是纯数字字符串，可以转换为BigInt
              channel = await prisma.ytbChannel.findUnique({
                where: { id: BigInt(video.channelId) }
              });
            } else {
              // 如果是YouTube格式的频道ID (如UC...)，通过channelId查询
              channel = await prisma.ytbChannel.findUnique({
                where: { channelId: video.channelId }
              });
            }

            if (channel) {
              console.log(`[API] 在数据库中找到视频和频道数据，videoId: ${videoId}, channelId: ${video.channelId}`);
              
              // 格式化数据
              return NextResponse.json({
                videoId: video.youtubeId,
                title: video.title || '',
                description: video.description || '',
                publishedAt: video.publishedAt,
                channelId: video.channelId,
                channelTitle: channel.title || '',
                thumbnails: { 
                  default: { url: video.thumbnailUrl || '' } 
                },
                statistics: {
                  viewCount: video.viewCount?.toString() || '0',
                  likeCount: video.likeCount?.toString() || '0',
                  commentCount: video.commentCount?.toString() || '0'
                }
              });
            }
          } catch (channelError) {
            console.error('[API] 查询频道数据出错:', channelError);
            // 如果查询频道数据出错，继续尝试外部API
          }
        }
        
        console.log(`[API] 数据库中未找到视频 ${videoId} 的数据，尝试从外部API获取`);

        // 如果数据库中没有找到视频，尝试从外部API获取
        const channelResponse = await fetch(`${process.env.API_BASE_URL || 'http://127.0.0.1:8100'}/api/channel/from-video?video_url_or_id=${videoId}`, {
          headers: {
            'accept': 'application/json'
          }
        });
        
        if (channelResponse.ok) {
          const channelData = await channelResponse.json();
          console.log(`[API] 从外部API获取到频道数据: ${JSON.stringify(channelData)}`);
          
          // 处理pending状态
          if (channelData.status === 'pending') {
            console.log(`[API] 外部API返回pending状态，任务ID: ${channelData.task_id || '未知'}`);
            return NextResponse.json({
              videoId: videoId,
              status: 'pending',
              message: '频道数据正在处理中，请稍后再试',
              task_id: channelData.task_id || null,
              channelId: channelData.channel_id || null,
              channelTitle: channelData.title || '处理中',
            });
          }
          
          // 使用从外部API获取的频道数据构建视频响应
          return NextResponse.json({
            videoId: videoId,
            title: '视频标题暂无', // 从外部API中无法获取具体视频标题
            description: '视频描述暂无', // 从外部API中无法获取具体视频描述
            publishedAt: channelData.published_at || new Date().toISOString(),
            channelId: channelData.channel_id,
            channelTitle: channelData.title || '',
            thumbnails: { 
              default: { url: channelData.thumbnail_url || '' } 
            },
            statistics: {
              viewCount: channelData.view_count?.toString() || '0',
              likeCount: '0', // 从外部API中无法获取
              commentCount: '0', // 从外部API中无法获取
            },
            channel: {
              id: channelData.channel_id,
              title: channelData.title,
              description: channelData.description,
              customUrl: channelData.custom_url,
              thumbnailUrl: channelData.thumbnail_url,
              subscriberCount: channelData.subscriber_count,
              videoCount: channelData.video_count,
              viewCount: channelData.view_count,
              publishedAt: channelData.published_at,
              channelUrl: channelData.channel_url
            }
          });
        } else {
          console.error(`[API] 外部API请求失败: ${channelResponse.status} ${channelResponse.statusText}`);
        }
      } catch (dbError) {
        console.error('[API] 数据库查询错误:', dbError);
        // 数据库查询错误，继续尝试外部API
      }
    }
    
    // 处理通过channelId获取视频列表的情况
    if (channelId) {
      console.log(`[API] 正在获取频道视频列表，channelId: ${channelId}, page: ${page}, limit: ${limit}, sortBy: ${sortBy}`);
      
      // 如果数据库不可用，返回模拟数据
      if (!isDatabaseAvailable) {
        console.log('[API] 数据库不可用，返回模拟视频列表');
        const mockVideos = generateMockVideos(channelId, page, limit);
        
        return NextResponse.json({
          data: mockVideos,
          pagination: {
            total: 100, // 假设总数
            page: page,
            limit: limit,
            totalPages: 10 // 假设总页数
          },
          _dbStatus: 'unavailable'
        });
      }
      
      try {
        // 计算分页
        const skip = (page - 1) * limit;
        
        // 处理排序选项 - 字段映射表
        const sortFieldMap: Record<string, string> = {
          'recent': 'publishedAt',
          'views': 'viewCount'
        };
        const orderByField = sortFieldMap[sortBy] || sortBy;
        
        console.log(`[API] 排序字段映射: ${sortBy} -> ${orderByField}`);
        
        // 查询总数 - 排除有问题的记录
        const totalCount = await prisma.ytbVideo.count({
          where: {
            channelId: channelId,
            deletedAt: null
          }
        });
        
        try {
          // 首先尝试使用select指定字段进行查询，避开有问题的categoryId字段
          const videos = await prisma.ytbVideo.findMany({
            where: {
              channelId: channelId,
              deletedAt: null,
              publishedAt: {
                not: null,
              },
            },
            select: {
              id: true,
              youtubeId: true,
              title: true,
              description: true,
              publishedAt: true,
              thumbnailUrl: true,
              viewCount: true,
              likeCount: true,
              commentCount: true
            },
            orderBy: {
              [orderByField]: 'desc'
            },
            skip,
            take: limit
          });
          
          console.log(`[API] 成功查询到频道 ${channelId} 的视频列表，共 ${videos.length} 条记录`);
          
          // 格式化返回数据
          const formattedVideos = videos.map(video => ({
            id: video.id.toString(),
            channelId: channelId,
            youtubeId: video.youtubeId,
            title: video.title || '',
            description: video.description || '',
            publishedAt: video.publishedAt,
            thumbnail: video.thumbnailUrl || null,
            duration: '0:00', // 默认值，如果没有实际数据
            viewCount: Number(video.viewCount || 0),
            likeCount: Number(video.likeCount || 0),
            commentCount: Number(video.commentCount || 0)
          }));
          
          // 计算总页数
          const totalPages = Math.ceil(totalCount / limit);
          
          return NextResponse.json({
            data: formattedVideos,
            pagination: {
              total: totalCount,
              page: page,
              limit: limit,
              totalPages: totalPages
            }
          });
        } catch (queryError) {
          console.error('[API] 查询详细视频数据出错，尝试简化查询:', queryError);
          
          // 如果详细查询失败，尝试使用更简单的查询
          const fallbackVideos = await prisma.ytbVideo.findMany({
            where: {
              channelId: channelId,
              deletedAt: null
            },
            select: {
              id: true,
              youtubeId: true,
              title: true,
              publishedAt: true
            },
            orderBy: {
              publishedAt: 'desc' // 始终按发布时间倒序
            },
            skip,
            take: limit
          });
          
          // 格式化返回数据 - 降级版本
          const formattedFallbackVideos = fallbackVideos.map(video => ({
            id: video.id.toString(),
            channelId: channelId,
            youtubeId: video.youtubeId,
            title: video.title || '',
            description: '',
            publishedAt: video.publishedAt,
            thumbnail: null,
            duration: '0:00',
            viewCount: 0,
            likeCount: 0,
            commentCount: 0
          }));
          
          // 计算总页数
          const totalPages = Math.ceil(totalCount / limit);
          
          return NextResponse.json({
            data: formattedFallbackVideos,
            pagination: {
              total: totalCount,
              page: page,
              limit: limit,
              totalPages: totalPages
            }
          });
        }
      } catch (dbError) {
        console.error('[API] 数据库查询错误:', dbError);
        
        // 如果数据库查询出错，返回模拟数据
        console.log('[API] 数据库查询错误，返回模拟视频列表');
        const mockVideos = generateMockVideos(channelId, page, limit);
        
        return NextResponse.json({
          data: mockVideos,
          pagination: {
            total: 100, // 假设总数
            page: page,
            limit: limit,
            totalPages: 10 // 假设总页数
          },
          _dbStatus: 'error'
        });
      }
    }

    // 如果到这里还没有返回数据，说明没有找到对应的视频或视频列表
    return NextResponse.json(
      { error: '未找到视频数据' },
      { status: 404 }
    );

  } catch (error) {
    console.error('[API] 处理请求错误:', error);
    return NextResponse.json(
      { error: '服务器内部错误' },
      { status: 500 }
    );
  } finally {
    // 不在这里断开连接，保持连接池
    // await prisma.$disconnect();
  }
} 