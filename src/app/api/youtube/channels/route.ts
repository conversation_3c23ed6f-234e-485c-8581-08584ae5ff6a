import { NextRequest, NextResponse } from 'next/server';

/**
 * YouTube Data API v3 频道信息获取接口
 * 使用 Google YouTube API 获取频道详细信息
 */

// YouTube API 基础配置
const YOUTUBE_API_BASE_URL = 'https://www.googleapis.com/youtube/v3';
const API_KEY = process.env.YOUTUBE_API_KEY;

// 频道信息接口类型定义
interface YouTubeChannelResponse {
  kind: string;
  etag: string;
  pageInfo: {
    totalResults: number;
    resultsPerPage: number;
  };
  items: YouTubeChannel[];
}

interface YouTubeChannel {
  kind: string;
  etag: string;
  id: string;
  snippet: {
    title: string;
    description: string;
    customUrl?: string;
    publishedAt: string;
    thumbnails: {
      default: { url: string; width: number; height: number };
      medium: { url: string; width: number; height: number };
      high: { url: string; width: number; height: number };
    };
    country?: string;
  };
  statistics: {
    viewCount: string;
    subscriberCount: string;
    hiddenSubscriberCount: boolean;
    videoCount: string;
  };
  contentDetails: {
    relatedPlaylists: {
      uploads: string;
      watchHistory?: string;
      watchLater?: string;
    };
  };
  brandingSettings?: {
    channel: {
      title: string;
      description: string;
      keywords?: string;
      country?: string;
    };
    image: {
      bannerExternalUrl?: string;
    };
  };
}

/**
 * GET /api/youtube/channels
 * 获取 YouTube 频道信息
 * 
 * 查询参数:
 * - id: 频道ID (可选，与username二选一)
 * - username: 频道用户名 (可选，与id二选一)
 * - part: 返回的数据部分，默认为 'snippet,statistics,contentDetails'
 */
export async function GET(request: NextRequest) {
  try {
    // 检查 API Key 是否配置
    if (!API_KEY) {
      console.error('[YouTube API] YOUTUBE_API_KEY 环境变量未配置');
      return NextResponse.json(
        { 
          error: 'YouTube API key not configured',
          message: 'YOUTUBE_API_KEY environment variable is required'
        },
        { status: 500 }
      );
    }

    const { searchParams } = new URL(request.url);
    const channelId = searchParams.get('id');
    const username = searchParams.get('username');
    const part = searchParams.get('part') || 'snippet,statistics,contentDetails,brandingSettings';

    // 验证参数
    if (!channelId && !username) {
      return NextResponse.json(
        { 
          error: 'Missing required parameter',
          message: 'Either "id" or "username" parameter is required'
        },
        { status: 400 }
      );
    }

    console.log(`[YouTube API] 开始获取频道信息 - ID: ${channelId}, Username: ${username}`);

    // 构建 API URL
    const apiUrl = new URL(`${YOUTUBE_API_BASE_URL}/channels`);
    apiUrl.searchParams.set('key', API_KEY);
    apiUrl.searchParams.set('part', part);

    // 根据参数类型设置查询条件
    if (channelId) {
      apiUrl.searchParams.set('id', channelId);
    } else if (username) {
      apiUrl.searchParams.set('forUsername', username);
    }

    console.log(`[YouTube API] 请求URL: ${apiUrl.toString()}`);

    // 调用 YouTube API
    const response = await fetch(apiUrl.toString(), {
      method: 'GET',
      headers: {
        'Accept': 'application/json',
        'User-Agent': 'ReYoutube-Web/1.0'
      }
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error(`[YouTube API] API 请求失败: ${response.status} - ${errorText}`);
      
      return NextResponse.json(
        { 
          error: 'YouTube API request failed',
          status: response.status,
          message: errorText
        },
        { status: response.status }
      );
    }

    const data: YouTubeChannelResponse = await response.json();
    console.log(`[YouTube API] 成功获取数据，找到 ${data.items?.length || 0} 个频道`);

    // 检查是否找到频道
    if (!data.items || data.items.length === 0) {
      return NextResponse.json(
        { 
          error: 'Channel not found',
          message: `No channel found for ${channelId ? `ID: ${channelId}` : `username: ${username}`}`
        },
        { status: 404 }
      );
    }

    // 格式化返回数据
    const channel = data.items[0];
    const formattedChannel = {
      id: channel.id,
      title: channel.snippet.title,
      description: channel.snippet.description,
      customUrl: channel.snippet.customUrl,
      publishedAt: channel.snippet.publishedAt,
      thumbnails: channel.snippet.thumbnails,
      country: channel.snippet.country,
      statistics: {
        viewCount: parseInt(channel.statistics.viewCount),
        subscriberCount: parseInt(channel.statistics.subscriberCount),
        videoCount: parseInt(channel.statistics.videoCount),
        hiddenSubscriberCount: channel.statistics.hiddenSubscriberCount
      },
      contentDetails: channel.contentDetails,
      brandingSettings: channel.brandingSettings,
      // 添加一些计算字段
      metadata: {
        subscriberCountFormatted: formatNumber(parseInt(channel.statistics.subscriberCount)),
        viewCountFormatted: formatNumber(parseInt(channel.statistics.viewCount)),
        videoCountFormatted: formatNumber(parseInt(channel.statistics.videoCount)),
        createdYearsAgo: Math.floor((Date.now() - new Date(channel.snippet.publishedAt).getTime()) / (1000 * 60 * 60 * 24 * 365))
      }
    };

    return NextResponse.json({
      success: true,
      data: formattedChannel,
      source: 'youtube_api',
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('[YouTube API] 处理请求时发生错误:', error);
    
    return NextResponse.json(
      { 
        error: 'Internal server error',
        message: error instanceof Error ? error.message : 'Unknown error occurred'
      },
      { status: 500 }
    );
  }
}

/**
 * 格式化数字显示
 */
function formatNumber(num: number): string {
  if (num >= 1000000000) {
    return (num / 1000000000).toFixed(1) + 'B';
  }
  if (num >= 1000000) {
    return (num / 1000000).toFixed(1) + 'M';
  }
  if (num >= 1000) {
    return (num / 1000).toFixed(1) + 'K';
  }
  return num.toString();
}
