import { NextRequest, NextResponse } from 'next/server';

/**
 * YouTube 频道信息模拟 API
 * 用于在网络受限环境下测试界面功能
 */

// 模拟数据
const MOCK_CHANNELS = {
  'UCVHFbqXqoYvEWM1Ddxl0QDg': {
    id: 'UCVHFbqXqoYvEWM1Ddxl0QDg',
    title: 'Google Developers',
    description: 'The Google Developers channel features talks from events, educational series, best practices, tips, and the latest updates across our products and platforms.',
    customUrl: 'GoogleDevelopers',
    publishedAt: '2007-08-23T00:34:43Z',
    thumbnails: {
      default: { url: 'https://yt3.ggpht.com/ytc/AIdro_kKJlGHdZhU8tUy2K2_1XovoJc-rWCHAV7FEH4=s88-c-k-c0x00ffffff-no-rj', width: 88, height: 88 },
      medium: { url: 'https://yt3.ggpht.com/ytc/AIdro_kKJlGHdZhU8tUy2K2_1XovoJc-rWCHAV7FEH4=s240-c-k-c0x00ffffff-no-rj', width: 240, height: 240 },
      high: { url: 'https://yt3.ggpht.com/ytc/AIdro_kKJlGHdZhU8tUy2K2_1XovoJc-rWCHAV7FEH4=s800-c-k-c0x00ffffff-no-rj', width: 800, height: 800 }
    },
    country: 'US',
    statistics: {
      viewCount: 234567890,
      subscriberCount: 2340000,
      videoCount: 5678,
      hiddenSubscriberCount: false
    }
  },
  'GoogleDevelopers': {
    id: 'UCVHFbqXqoYvEWM1Ddxl0QDg',
    title: 'Google Developers',
    description: 'The Google Developers channel features talks from events, educational series, best practices, tips, and the latest updates across our products and platforms.',
    customUrl: 'GoogleDevelopers',
    publishedAt: '2007-08-23T00:34:43Z',
    thumbnails: {
      default: { url: 'https://yt3.ggpht.com/ytc/AIdro_kKJlGHdZhU8tUy2K2_1XovoJc-rWCHAV7FEH4=s88-c-k-c0x00ffffff-no-rj', width: 88, height: 88 },
      medium: { url: 'https://yt3.ggpht.com/ytc/AIdro_kKJlGHdZhU8tUy2K2_1XovoJc-rWCHAV7FEH4=s240-c-k-c0x00ffffff-no-rj', width: 240, height: 240 },
      high: { url: 'https://yt3.ggpht.com/ytc/AIdro_kKJlGHdZhU8tUy2K2_1XovoJc-rWCHAV7FEH4=s800-c-k-c0x00ffffff-no-rj', width: 800, height: 800 }
    },
    country: 'US',
    statistics: {
      viewCount: 234567890,
      subscriberCount: 2340000,
      videoCount: 5678,
      hiddenSubscriberCount: false
    }
  },
  'TechCrunch': {
    id: 'UCCjyq_K1Xwfg8Lndy7lKMpA',
    title: 'TechCrunch',
    description: 'TechCrunch is a leading technology media property, dedicated to obsessively profiling startups, reviewing new Internet products, and breaking tech news.',
    customUrl: 'TechCrunch',
    publishedAt: '2006-03-07T22:25:09Z',
    thumbnails: {
      default: { url: 'https://yt3.ggpht.com/ytc/AIdro_n8QJQkd5qJ5cVcQxgBl5cK8qJQkd5qJ5cVcQxgBl5cK8=s88-c-k-c0x00ffffff-no-rj', width: 88, height: 88 },
      medium: { url: 'https://yt3.ggpht.com/ytc/AIdro_n8QJQkd5qJ5cVcQxgBl5cK8qJQkd5qJ5cVcQxgBl5cK8=s240-c-k-c0x00ffffff-no-rj', width: 240, height: 240 },
      high: { url: 'https://yt3.ggpht.com/ytc/AIdro_n8QJQkd5qJ5cVcQxgBl5cK8qJQkd5qJ5cVcQxgBl5cK8=s800-c-k-c0x00ffffff-no-rj', width: 800, height: 800 }
    },
    country: 'US',
    statistics: {
      viewCount: 456789012,
      subscriberCount: 3450000,
      videoCount: 12345,
      hiddenSubscriberCount: false
    }
  }
};

/**
 * 格式化数字显示
 */
function formatNumber(num: number): string {
  if (num >= 1000000000) {
    return (num / 1000000000).toFixed(1) + 'B';
  }
  if (num >= 1000000) {
    return (num / 1000000).toFixed(1) + 'M';
  }
  if (num >= 1000) {
    return (num / 1000).toFixed(1) + 'K';
  }
  return num.toString();
}

export async function GET(request: NextRequest) {
  const startTime = Date.now();
  console.log(`[YouTube Mock API] 🎭 开始处理模拟频道请求`);
  
  try {
    const { searchParams } = new URL(request.url);
    const channelId = searchParams.get('id');
    const username = searchParams.get('username');

    console.log(`[YouTube Mock API] 📋 请求参数:`);
    console.log(`[YouTube Mock API]    - channelId: ${channelId || 'null'}`);
    console.log(`[YouTube Mock API]    - username: ${username || 'null'}`);

    // 验证参数
    if (!channelId && !username) {
      console.error('[YouTube Mock API] ❌ 缺少必需参数');
      return NextResponse.json(
        { 
          error: 'Missing required parameter',
          message: 'Either "id" or "username" parameter is required'
        },
        { status: 400 }
      );
    }

    // 查找模拟数据
    const searchKey = channelId || username;
    const mockChannel = MOCK_CHANNELS[searchKey as keyof typeof MOCK_CHANNELS];

    if (!mockChannel) {
      console.log(`[YouTube Mock API] ❌ 未找到模拟数据: ${searchKey}`);
      return NextResponse.json(
        { 
          error: 'Channel not found',
          message: `No mock data found for ${channelId ? `ID: ${channelId}` : `username: ${username}`}`,
          availableChannels: Object.keys(MOCK_CHANNELS)
        },
        { status: 404 }
      );
    }

    console.log(`[YouTube Mock API] ✅ 找到模拟数据: ${mockChannel.title}`);

    // 模拟网络延迟
    await new Promise(resolve => setTimeout(resolve, 500 + Math.random() * 1000));

    // 格式化返回数据
    const formattedChannel = {
      id: mockChannel.id,
      title: mockChannel.title,
      description: mockChannel.description,
      customUrl: mockChannel.customUrl,
      publishedAt: mockChannel.publishedAt,
      thumbnails: mockChannel.thumbnails,
      country: mockChannel.country,
      statistics: {
        viewCount: mockChannel.statistics.viewCount,
        subscriberCount: mockChannel.statistics.subscriberCount,
        videoCount: mockChannel.statistics.videoCount,
        hiddenSubscriberCount: mockChannel.statistics.hiddenSubscriberCount
      },
      contentDetails: {
        relatedPlaylists: {
          uploads: `UU${mockChannel.id.substring(2)}` // 模拟上传播放列表ID
        }
      },
      brandingSettings: {
        channel: {
          title: mockChannel.title,
          description: mockChannel.description,
          country: mockChannel.country
        }
      },
      // 添加一些计算字段
      metadata: {
        subscriberCountFormatted: formatNumber(mockChannel.statistics.subscriberCount),
        viewCountFormatted: formatNumber(mockChannel.statistics.viewCount),
        videoCountFormatted: formatNumber(mockChannel.statistics.videoCount),
        createdYearsAgo: Math.floor((Date.now() - new Date(mockChannel.publishedAt).getTime()) / (1000 * 60 * 60 * 24 * 365))
      }
    };

    const totalDuration = Date.now() - startTime;
    console.log(`[YouTube Mock API] ✅ 模拟请求处理完成，耗时: ${totalDuration}ms`);

    return NextResponse.json({
      success: true,
      data: formattedChannel,
      source: 'mock_data',
      timestamp: new Date().toISOString(),
      note: 'This is mock data for testing purposes when YouTube API is not accessible'
    });

  } catch (error) {
    const totalDuration = Date.now() - startTime;
    console.error(`[YouTube Mock API] 💥 处理模拟请求时发生错误 (耗时: ${totalDuration}ms):`, error);
    
    return NextResponse.json(
      { 
        error: 'Internal server error',
        message: error instanceof Error ? error.message : 'Unknown error occurred'
      },
      { status: 500 }
    );
  }
}
