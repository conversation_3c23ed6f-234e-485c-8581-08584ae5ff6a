import { NextRequest, NextResponse } from 'next/server';

/**
 * YouTube Data API v3 视频信息获取接口
 * 使用 Google YouTube API 获取视频详细信息
 */

// YouTube API 基础配置
const YOUTUBE_API_BASE_URL = 'https://www.googleapis.com/youtube/v3';
const API_KEY = process.env.YOUTUBE_API_KEY;

// 视频信息接口类型定义
interface YouTubeVideoResponse {
  kind: string;
  etag: string;
  pageInfo: {
    totalResults: number;
    resultsPerPage: number;
  };
  items: YouTubeVideo[];
}

interface YouTubeVideo {
  kind: string;
  etag: string;
  id: string;
  snippet: {
    publishedAt: string;
    channelId: string;
    title: string;
    description: string;
    thumbnails: {
      default: { url: string; width: number; height: number };
      medium: { url: string; width: number; height: number };
      high: { url: string; width: number; height: number };
      standard?: { url: string; width: number; height: number };
      maxres?: { url: string; width: number; height: number };
    };
    channelTitle: string;
    tags?: string[];
    categoryId: string;
    liveBroadcastContent: string;
    defaultLanguage?: string;
    defaultAudioLanguage?: string;
  };
  statistics: {
    viewCount: string;
    likeCount: string;
    favoriteCount: string;
    commentCount: string;
  };
  contentDetails: {
    duration: string;
    dimension: string;
    definition: string;
    caption: string;
    licensedContent: boolean;
    contentRating: object;
    projection: string;
  };
}

/**
 * GET /api/youtube/videos
 * 获取 YouTube 视频信息
 * 
 * 查询参数:
 * - id: 视频ID (必需)
 * - part: 返回的数据部分，默认为 'snippet,statistics,contentDetails'
 */
export async function GET(request: NextRequest) {
  const startTime = Date.now();
  console.log(`[YouTube Videos API] 🚀 开始处理视频请求`);

  try {
    // 检查 API Key 是否配置
    console.log(`[YouTube Videos API] 🔑 检查 API Key 配置...`);
    if (!API_KEY) {
      console.error('[YouTube Videos API] ❌ YOUTUBE_API_KEY 环境变量未配置');
      return NextResponse.json(
        {
          error: 'YouTube API key not configured',
          message: 'YOUTUBE_API_KEY environment variable is required'
        },
        { status: 500 }
      );
    }
    console.log(`[YouTube Videos API] ✅ API Key 已配置 (长度: ${API_KEY.length})`);

    const { searchParams } = new URL(request.url);
    const videoId = searchParams.get('id');
    const part = searchParams.get('part') || 'snippet,statistics,contentDetails';

    console.log(`[YouTube Videos API] 📋 请求参数:`);
    console.log(`[YouTube Videos API]    - videoId: ${videoId || 'null'}`);
    console.log(`[YouTube Videos API]    - part: ${part}`);

    // 验证参数
    if (!videoId) {
      console.error('[YouTube Videos API] ❌ 缺少必需参数: videoId');
      return NextResponse.json(
        {
          error: 'Missing required parameter',
          message: 'Video "id" parameter is required'
        },
        { status: 400 }
      );
    }

    console.log(`[YouTube Videos API] 🎬 开始获取视频信息 - ID: ${videoId}`);

    // 构建 API URL
    const apiUrl = new URL(`${YOUTUBE_API_BASE_URL}/videos`);
    apiUrl.searchParams.set('key', API_KEY);
    apiUrl.searchParams.set('part', part);
    apiUrl.searchParams.set('id', videoId);

    console.log(`[YouTube Videos API] 请求URL: ${apiUrl.toString()}`);

    // 调用 YouTube API
    const response = await fetch(apiUrl.toString(), {
      method: 'GET',
      headers: {
        'Accept': 'application/json',
        'User-Agent': 'ReYoutube-Web/1.0'
      }
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error(`[YouTube Videos API] API 请求失败: ${response.status} - ${errorText}`);
      
      return NextResponse.json(
        { 
          error: 'YouTube API request failed',
          status: response.status,
          message: errorText
        },
        { status: response.status }
      );
    }

    const data: YouTubeVideoResponse = await response.json();
    console.log(`[YouTube Videos API] 成功获取数据，找到 ${data.items?.length || 0} 个视频`);

    // 检查是否找到视频
    if (!data.items || data.items.length === 0) {
      return NextResponse.json(
        { 
          error: 'Video not found',
          message: `No video found for ID: ${videoId}`
        },
        { status: 404 }
      );
    }

    // 格式化返回数据
    const video = data.items[0];
    const formattedVideo = {
      id: video.id,
      title: video.snippet.title,
      description: video.snippet.description,
      publishedAt: video.snippet.publishedAt,
      channelId: video.snippet.channelId,
      channelTitle: video.snippet.channelTitle,
      thumbnails: video.snippet.thumbnails,
      tags: video.snippet.tags || [],
      categoryId: video.snippet.categoryId,
      statistics: {
        viewCount: parseInt(video.statistics.viewCount),
        likeCount: parseInt(video.statistics.likeCount),
        favoriteCount: parseInt(video.statistics.favoriteCount),
        commentCount: parseInt(video.statistics.commentCount)
      },
      contentDetails: {
        duration: video.contentDetails.duration,
        dimension: video.contentDetails.dimension,
        definition: video.contentDetails.definition,
        caption: video.contentDetails.caption === 'true',
        licensedContent: video.contentDetails.licensedContent
      },
      // 添加一些计算字段
      metadata: {
        viewCountFormatted: formatNumber(parseInt(video.statistics.viewCount)),
        likeCountFormatted: formatNumber(parseInt(video.statistics.likeCount)),
        commentCountFormatted: formatNumber(parseInt(video.statistics.commentCount)),
        durationFormatted: formatDuration(video.contentDetails.duration),
        publishedDaysAgo: Math.floor((Date.now() - new Date(video.snippet.publishedAt).getTime()) / (1000 * 60 * 60 * 24))
      }
    };

    return NextResponse.json({
      success: true,
      data: formattedVideo,
      source: 'youtube_api',
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('[YouTube Videos API] 处理请求时发生错误:', error);
    
    return NextResponse.json(
      { 
        error: 'Internal server error',
        message: error instanceof Error ? error.message : 'Unknown error occurred'
      },
      { status: 500 }
    );
  }
}

/**
 * 格式化数字显示
 */
function formatNumber(num: number): string {
  if (num >= 1000000000) {
    return (num / 1000000000).toFixed(1) + 'B';
  }
  if (num >= 1000000) {
    return (num / 1000000).toFixed(1) + 'M';
  }
  if (num >= 1000) {
    return (num / 1000).toFixed(1) + 'K';
  }
  return num.toString();
}

/**
 * 格式化 ISO 8601 持续时间为可读格式
 * 例: PT4M13S -> 4:13, PT1H2M30S -> 1:02:30
 */
function formatDuration(duration: string): string {
  const match = duration.match(/PT(?:(\d+)H)?(?:(\d+)M)?(?:(\d+)S)?/);
  if (!match) return '0:00';

  const hours = parseInt(match[1] || '0');
  const minutes = parseInt(match[2] || '0');
  const seconds = parseInt(match[3] || '0');

  if (hours > 0) {
    return `${hours}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
  } else {
    return `${minutes}:${seconds.toString().padStart(2, '0')}`;
  }
}
