import { NextRequest, NextResponse } from 'next/server';

/**
 * YouTube 视频信息模拟 API
 * 用于在网络受限环境下测试界面功能
 */

// 模拟数据
const MOCK_VIDEOS = {
  'dQw4w9WgXcQ': {
    id: 'dQw4w9WgXcQ',
    title: '<PERSON> - Never Gonna Give You Up (Official Video)',
    description: 'The official video for "Never Gonna Give You Up" by <PERSON>\n\n"Never Gonna Give You Up" was a global smash on its release in July 1987, topping the charts in 25 countries including <PERSON>'s native UK and the US Billboard Hot 100.  It also won the Brit Award for Best single in 1988. <PERSON> and <PERSON> wrote and produced the track which was the lead-off single and lead track from <PERSON>'s debut LP "Whenever You Need Somebody".',
    publishedAt: '2009-10-25T06:57:33Z',
    channelId: 'UCuAXFkgsw1L7xaCfnd5JJOw',
    channelTitle: '<PERSON>',
    thumbnails: {
      default: { url: 'https://i.ytimg.com/vi/dQw4w9WgXcQ/default.jpg', width: 120, height: 90 },
      medium: { url: 'https://i.ytimg.com/vi/dQw4w9WgXcQ/mqdefault.jpg', width: 320, height: 180 },
      high: { url: 'https://i.ytimg.com/vi/dQw4w9WgXcQ/hqdefault.jpg', width: 480, height: 360 },
      standard: { url: 'https://i.ytimg.com/vi/dQw4w9WgXcQ/sddefault.jpg', width: 640, height: 480 },
      maxres: { url: 'https://i.ytimg.com/vi/dQw4w9WgXcQ/maxresdefault.jpg', width: 1280, height: 720 }
    },
    tags: ['Rick Astley', 'Never Gonna Give You Up', 'Official Video', 'Stock Aitken Waterman', '80s', 'Pop', 'Dance'],
    categoryId: '10',
    statistics: {
      viewCount: **********,
      likeCount: 12345678,
      favoriteCount: 0,
      commentCount: 1234567
    },
    contentDetails: {
      duration: 'PT3M33S',
      dimension: '2d',
      definition: 'hd',
      caption: 'true',
      licensedContent: true,
      projection: 'rectangular'
    }
  },
  'jNQXAC9IVRw': {
    id: 'jNQXAC9IVRw',
    title: 'Me at the zoo',
    description: 'The first video on YouTube. Maybe it\'s time to go back to the zoo?\n\nThe name of the music is Darude - Sandstorm.',
    publishedAt: '2005-04-23T23:31:52Z',
    channelId: 'UC4QobU6STFB0P71PMvOGN5A',
    channelTitle: 'jawed',
    thumbnails: {
      default: { url: 'https://i.ytimg.com/vi/jNQXAC9IVRw/default.jpg', width: 120, height: 90 },
      medium: { url: 'https://i.ytimg.com/vi/jNQXAC9IVRw/mqdefault.jpg', width: 320, height: 180 },
      high: { url: 'https://i.ytimg.com/vi/jNQXAC9IVRw/hqdefault.jpg', width: 480, height: 360 }
    },
    tags: ['first video', 'zoo', 'elephants', 'youtube history'],
    categoryId: '22',
    statistics: {
      viewCount: 234567890,
      likeCount: 5678901,
      favoriteCount: 0,
      commentCount: 2345678
    },
    contentDetails: {
      duration: 'PT19S',
      dimension: '2d',
      definition: 'sd',
      caption: 'false',
      licensedContent: false,
      projection: 'rectangular'
    }
  },
  '9bZkp7q19f0': {
    id: '9bZkp7q19f0',
    title: 'PSY - GANGNAM STYLE(강남스타일) M/V',
    description: 'PSY - GANGNAM STYLE(강남스타일) M/V @ https://youtu.be/9bZkp7q19f0\n\nPSY - 싸이\nhttps://www.youtube.com/officialpsy\nhttps://www.facebook.com/officialpsy\nhttps://twitter.com/psy_oppa\nhttps://www.instagram.com/42psy42\nhttps://iTunes.com/PSY\n\n#PSY #싸이 #GANGNAMSTYLE #강남스타일',
    publishedAt: '2012-07-15T08:34:21Z',
    channelId: 'UCrDkAvF9ZLzWz6n4frXFbNw',
    channelTitle: 'officialpsy',
    thumbnails: {
      default: { url: 'https://i.ytimg.com/vi/9bZkp7q19f0/default.jpg', width: 120, height: 90 },
      medium: { url: 'https://i.ytimg.com/vi/9bZkp7q19f0/mqdefault.jpg', width: 320, height: 180 },
      high: { url: 'https://i.ytimg.com/vi/9bZkp7q19f0/hqdefault.jpg', width: 480, height: 360 },
      standard: { url: 'https://i.ytimg.com/vi/9bZkp7q19f0/sddefault.jpg', width: 640, height: 480 },
      maxres: { url: 'https://i.ytimg.com/vi/9bZkp7q19f0/maxresdefault.jpg', width: 1280, height: 720 }
    },
    tags: ['PSY', 'GANGNAM STYLE', '강남스타일', 'M/V', 'KPOP', 'Korean Pop', 'Dance'],
    categoryId: '10',
    statistics: {
      viewCount: **********,
      likeCount: 23456789,
      favoriteCount: 0,
      commentCount: 3456789
    },
    contentDetails: {
      duration: 'PT4M12S',
      dimension: '2d',
      definition: 'hd',
      caption: 'true',
      licensedContent: true,
      projection: 'rectangular'
    }
  }
};

/**
 * 格式化数字显示
 */
function formatNumber(num: number): string {
  if (num >= **********) {
    return (num / **********).toFixed(1) + 'B';
  }
  if (num >= 1000000) {
    return (num / 1000000).toFixed(1) + 'M';
  }
  if (num >= 1000) {
    return (num / 1000).toFixed(1) + 'K';
  }
  return num.toString();
}

/**
 * 格式化 ISO 8601 持续时间为可读格式
 */
function formatDuration(duration: string): string {
  const match = duration.match(/PT(?:(\d+)H)?(?:(\d+)M)?(?:(\d+)S)?/);
  if (!match) return '0:00';

  const hours = parseInt(match[1] || '0');
  const minutes = parseInt(match[2] || '0');
  const seconds = parseInt(match[3] || '0');

  if (hours > 0) {
    return `${hours}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
  } else {
    return `${minutes}:${seconds.toString().padStart(2, '0')}`;
  }
}

export async function GET(request: NextRequest) {
  const startTime = Date.now();
  console.log(`[YouTube Mock Videos API] 🎭 开始处理模拟视频请求`);
  
  try {
    const { searchParams } = new URL(request.url);
    const videoId = searchParams.get('id');

    console.log(`[YouTube Mock Videos API] 📋 请求参数:`);
    console.log(`[YouTube Mock Videos API]    - videoId: ${videoId || 'null'}`);

    // 验证参数
    if (!videoId) {
      console.error('[YouTube Mock Videos API] ❌ 缺少必需参数: videoId');
      return NextResponse.json(
        { 
          error: 'Missing required parameter',
          message: 'Video "id" parameter is required'
        },
        { status: 400 }
      );
    }

    // 查找模拟数据
    const mockVideo = MOCK_VIDEOS[videoId as keyof typeof MOCK_VIDEOS];

    if (!mockVideo) {
      console.log(`[YouTube Mock Videos API] ❌ 未找到模拟数据: ${videoId}`);
      return NextResponse.json(
        { 
          error: 'Video not found',
          message: `No mock data found for video ID: ${videoId}`,
          availableVideos: Object.keys(MOCK_VIDEOS)
        },
        { status: 404 }
      );
    }

    console.log(`[YouTube Mock Videos API] ✅ 找到模拟数据: ${mockVideo.title}`);

    // 模拟网络延迟
    await new Promise(resolve => setTimeout(resolve, 300 + Math.random() * 700));

    // 格式化返回数据
    const formattedVideo = {
      id: mockVideo.id,
      title: mockVideo.title,
      description: mockVideo.description,
      publishedAt: mockVideo.publishedAt,
      channelId: mockVideo.channelId,
      channelTitle: mockVideo.channelTitle,
      thumbnails: mockVideo.thumbnails,
      tags: mockVideo.tags,
      categoryId: mockVideo.categoryId,
      statistics: {
        viewCount: mockVideo.statistics.viewCount,
        likeCount: mockVideo.statistics.likeCount,
        favoriteCount: mockVideo.statistics.favoriteCount,
        commentCount: mockVideo.statistics.commentCount
      },
      contentDetails: {
        duration: mockVideo.contentDetails.duration,
        dimension: mockVideo.contentDetails.dimension,
        definition: mockVideo.contentDetails.definition,
        caption: mockVideo.contentDetails.caption === 'true',
        licensedContent: mockVideo.contentDetails.licensedContent
      },
      // 添加一些计算字段
      metadata: {
        viewCountFormatted: formatNumber(mockVideo.statistics.viewCount),
        likeCountFormatted: formatNumber(mockVideo.statistics.likeCount),
        commentCountFormatted: formatNumber(mockVideo.statistics.commentCount),
        durationFormatted: formatDuration(mockVideo.contentDetails.duration),
        publishedDaysAgo: Math.floor((Date.now() - new Date(mockVideo.publishedAt).getTime()) / (1000 * 60 * 60 * 24))
      }
    };

    const totalDuration = Date.now() - startTime;
    console.log(`[YouTube Mock Videos API] ✅ 模拟请求处理完成，耗时: ${totalDuration}ms`);

    return NextResponse.json({
      success: true,
      data: formattedVideo,
      source: 'mock_data',
      timestamp: new Date().toISOString(),
      note: 'This is mock data for testing purposes when YouTube API is not accessible'
    });

  } catch (error) {
    const totalDuration = Date.now() - startTime;
    console.error(`[YouTube Mock Videos API] 💥 处理模拟请求时发生错误 (耗时: ${totalDuration}ms):`, error);
    
    return NextResponse.json(
      { 
        error: 'Internal server error',
        message: error instanceof Error ? error.message : 'Unknown error occurred'
      },
      { status: 500 }
    );
  }
}
