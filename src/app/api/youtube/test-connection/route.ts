import { NextRequest, NextResponse } from 'next/server';

/**
 * YouTube API 连接测试端点
 * 用于诊断网络连接问题
 */

const API_KEY = process.env.YOUTUBE_API_KEY;

export async function GET(request: NextRequest) {
  const startTime = Date.now();
  console.log(`[YouTube Connection Test] 🔍 开始连接测试`);
  
  const testResults = {
    timestamp: new Date().toISOString(),
    apiKeyConfigured: !!API_KEY,
    tests: [] as any[]
  };

  // 测试 1: 检查 API Key 配置
  console.log(`[YouTube Connection Test] 🔑 测试 API Key 配置...`);
  if (!API_KEY) {
    testResults.tests.push({
      name: 'API Key Configuration',
      status: 'failed',
      message: 'YOUTUBE_API_KEY environment variable not configured',
      duration: 0
    });
    
    return NextResponse.json({
      success: false,
      results: testResults,
      summary: 'API Key not configured'
    });
  }

  testResults.tests.push({
    name: 'API Key Configuration',
    status: 'passed',
    message: `API Key configured (length: ${API_KEY.length})`,
    duration: 0
  });

  // 测试 2: DNS 解析测试
  console.log(`[YouTube Connection Test] 🌐 测试 DNS 解析...`);
  const dnsTestStart = Date.now();
  try {
    const dns = require('dns').promises;
    const addresses = await dns.resolve4('www.googleapis.com');
    const dnsDuration = Date.now() - dnsTestStart;
    
    testResults.tests.push({
      name: 'DNS Resolution',
      status: 'passed',
      message: `Resolved to ${addresses.length} addresses: ${addresses.slice(0, 3).join(', ')}`,
      duration: dnsDuration,
      details: { addresses }
    });
    
    console.log(`[YouTube Connection Test] ✅ DNS 解析成功: ${addresses.join(', ')}`);
  } catch (error) {
    const dnsDuration = Date.now() - dnsTestStart;
    testResults.tests.push({
      name: 'DNS Resolution',
      status: 'failed',
      message: `DNS resolution failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
      duration: dnsDuration
    });
    
    console.error(`[YouTube Connection Test] ❌ DNS 解析失败:`, error);
  }

  // 测试 3: 基础连接测试
  console.log(`[YouTube Connection Test] 🔗 测试基础连接...`);
  const basicConnTestStart = Date.now();
  try {
    const response = await fetch('https://www.googleapis.com/', {
      method: 'HEAD',
      signal: AbortSignal.timeout(10000)
    });
    
    const basicConnDuration = Date.now() - basicConnTestStart;
    testResults.tests.push({
      name: 'Basic Connection',
      status: response.ok ? 'passed' : 'warning',
      message: `HTTP ${response.status} - ${response.statusText}`,
      duration: basicConnDuration,
      details: {
        status: response.status,
        headers: Object.fromEntries(response.headers.entries())
      }
    });
    
    console.log(`[YouTube Connection Test] ✅ 基础连接测试: ${response.status}`);
  } catch (error) {
    const basicConnDuration = Date.now() - basicConnTestStart;
    testResults.tests.push({
      name: 'Basic Connection',
      status: 'failed',
      message: `Connection failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
      duration: basicConnDuration
    });
    
    console.error(`[YouTube Connection Test] ❌ 基础连接失败:`, error);
  }

  // 测试 4: YouTube API 端点测试
  console.log(`[YouTube Connection Test] 🎬 测试 YouTube API 端点...`);
  const apiTestStart = Date.now();
  try {
    const apiUrl = `https://www.googleapis.com/youtube/v3/channels?part=snippet&id=UCVHFbqXqoYvEWM1Ddxl0QDg&key=${API_KEY}`;
    
    const response = await fetch(apiUrl, {
      method: 'GET',
      headers: {
        'Accept': 'application/json',
        'User-Agent': 'ReYoutube-Web-Test/1.0'
      },
      signal: AbortSignal.timeout(15000)
    });
    
    const apiTestDuration = Date.now() - apiTestStart;
    
    if (response.ok) {
      const data = await response.json();
      testResults.tests.push({
        name: 'YouTube API Test',
        status: 'passed',
        message: `API call successful - Found ${data.items?.length || 0} channels`,
        duration: apiTestDuration,
        details: {
          status: response.status,
          itemCount: data.items?.length || 0,
          quotaUsed: response.headers.get('x-ratelimit-remaining') || 'unknown'
        }
      });
      
      console.log(`[YouTube Connection Test] ✅ YouTube API 测试成功`);
    } else {
      const errorText = await response.text();
      testResults.tests.push({
        name: 'YouTube API Test',
        status: 'failed',
        message: `API call failed: HTTP ${response.status}`,
        duration: apiTestDuration,
        details: {
          status: response.status,
          error: errorText
        }
      });
      
      console.error(`[YouTube Connection Test] ❌ YouTube API 测试失败: ${response.status}`);
    }
  } catch (error) {
    const apiTestDuration = Date.now() - apiTestStart;
    testResults.tests.push({
      name: 'YouTube API Test',
      status: 'failed',
      message: `API test failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
      duration: apiTestDuration
    });
    
    console.error(`[YouTube Connection Test] ❌ YouTube API 测试异常:`, error);
  }

  // 生成总结
  const totalDuration = Date.now() - startTime;
  const passedTests = testResults.tests.filter(t => t.status === 'passed').length;
  const totalTests = testResults.tests.length;
  const allPassed = passedTests === totalTests;

  console.log(`[YouTube Connection Test] 📊 测试完成: ${passedTests}/${totalTests} 通过`);

  // 生成建议
  const suggestions = [];
  const failedTests = testResults.tests.filter(t => t.status === 'failed');
  
  if (failedTests.some(t => t.name === 'DNS Resolution')) {
    suggestions.push('DNS resolution failed. Check your DNS settings or try using a different DNS server (e.g., 8.8.8.8, 1.1.1.1).');
  }
  
  if (failedTests.some(t => t.name === 'Basic Connection')) {
    suggestions.push('Basic connection to Google APIs failed. Check your firewall settings and internet connection.');
  }
  
  if (failedTests.some(t => t.name === 'YouTube API Test')) {
    suggestions.push('YouTube API access failed. This might be due to network restrictions, API key issues, or regional blocking.');
  }
  
  if (failedTests.length > 0) {
    suggestions.push('Consider using a VPN if YouTube/Google services are blocked in your region.');
    suggestions.push('Check if your network has a proxy or firewall that might be blocking HTTPS requests to googleapis.com.');
  }

  return NextResponse.json({
    success: allPassed,
    results: {
      ...testResults,
      summary: {
        totalTests,
        passedTests,
        failedTests: totalTests - passedTests,
        totalDuration,
        overallStatus: allPassed ? 'all_passed' : 'some_failed'
      },
      suggestions: suggestions.length > 0 ? suggestions : ['All tests passed! Your YouTube API integration should work correctly.']
    }
  });
}
