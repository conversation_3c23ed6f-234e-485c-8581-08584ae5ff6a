import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';

export async function GET(request: NextRequest) {
  try {
    const url = new URL(request.url);
    const username = url.searchParams.get('username');

    if (!username) {
      return NextResponse.json(
        { error: '请提供username查询参数' },
        { status: 400 }
      );
    }

    console.log(`[测试] 查询用户名: ${username}`);

    // 测试两种格式的查询
    const channels = await prisma.ytbChannel.findMany({
      where: {
        OR: [
          { customUrl: `@${username}` },
          { customUrl: username }
        ],
        deletedAt: null,
      },
      select: {
        id: true,
        channelId: true,
        customUrl: true,
        title: true,
      },
      take: 5,
    });

    console.log(`[测试] 查询结果数量: ${channels.length}`);

    return NextResponse.json({
      query: username,
      results: channels,
      count: channels.length,
    });
  } catch (error) {
    console.error('[测试] 查询出错:', error);
    return NextResponse.json(
      { error: '查询处理出错' },
      { status: 500 }
    );
  }
} 