import { NextResponse } from "next/server";
import { PrismaClient, Prisma } from "@prisma/client"; // Import Prisma namespace for types

// Define the expected structure of the query result based on 'select'
interface ChannelQueryResult {
  channelId: string;
  customUrl: string | null; // customUrl can be null based on schema
  title: string;
  thumbnailUrl: string | null; // Add thumbnail URL
  subscriberCount: number | null; // Add subscriber count
  videoCount: number | null; // Add video count
}

// Enable query logging
const prisma = new PrismaClient({
  log: ['query', 'info', 'warn', 'error'],
});

export async function GET(request: Request) {
  const { searchParams } = new URL(request.url);
  const query = searchParams.get("query");

  if (!query || query.trim().length < 2) {
    return NextResponse.json([]);
  }

  console.log(`[API Search] Received query: ${query}`);

  try {
    console.log("[API Search] Executing Prisma query...");
    // Explicitly type the result based on the 'select' fields
    const channels: ChannelQueryResult[] = await prisma.ytbChannel.findMany({
      where: {
        customUrl: {
          contains: query,
          mode: "insensitive",
        },
        deletedAt: null,
      },
      select: {
        channelId: true,
        customUrl: true,
        title: true,
        thumbnailUrl: true, // Select thumbnail URL
        subscriberCount: true, // Select subscriber count
        videoCount: true, // Select video count 
      },
      take: 10,
      orderBy: {
        customUrl: 'asc',
      }
    });
    console.log(`[API Search] Found ${channels.length} channels.`);

    // 只返回必要的字段
    const results = channels
      .filter(channel => Boolean(channel.customUrl))
      .map(channel => ({
        channelId: channel.channelId,
        name: channel.customUrl,
        title: channel.title,
        thumbnailUrl: channel.thumbnailUrl,
        subscriberCount: channel.subscriberCount,
        videoCount: channel.videoCount,
        customUrl: channel.customUrl,
      }));

    return NextResponse.json(results);
  } catch (error) {
    console.error("[API Search] Failed to search channels:", error);
     // Check if it's a known Prisma error for better logging if needed
    if (error instanceof Prisma.PrismaClientKnownRequestError) {
       console.error("Prisma Error Code:", error.code);
    }
    return NextResponse.json({ error: "Internal Server Error" }, { status: 500 });
  } finally {
    await prisma.$disconnect();
  }
}