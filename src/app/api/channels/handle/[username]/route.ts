import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { Prisma } from '@prisma/client';

export async function GET(
  request: NextRequest,
  context: { params: { username: string } }
) {
  try {
    // 获取基础URL
    const baseUrl = process.env.NEXT_PUBLIC_WEB_URL || new URL(request.url).origin;
    console.log(`[API] 当前域名: ${new URL(request.url).origin}, 使用基础URL: ${baseUrl}`);

    // 使用await处理params参数
    const params = await context.params;
    const username = params.username;
    console.log(`[API] 开始查询用户名: ${username} 对应的频道ID`);

    // 构建OR查询条件，同时查找带@和不带@的格式
    const whereCondition: Prisma.YtbChannelWhereInput = {
      OR: [
        { customUrl: `@${username}`, deletedAt: null },
        { customUrl: username, deletedAt: null }
      ]
    };

    // 从数据库中查询用户名对应的频道
    const channel = await prisma.ytbChannel.findFirst({
      where: whereCondition,
      select: {
        channelId: true,
        customUrl: true
      },
    });

    // 如果在数据库中找到了频道，直接重定向到频道详情页
    if (channel) {
      console.log(`[API] 找到用户 ${channel.customUrl} 对应的频道ID: ${channel.channelId}`);

      // 获取当前区域设置或使用默认区域设置
      let locale = 'en';
      const localeMatch = request.headers.get('accept-language')?.match(/^[a-zA-Z-]+/);
      if (localeMatch && ['en', 'zh'].includes(localeMatch[0])) {
        locale = localeMatch[0];
      }

      // 构建重定向URL
      const redirectUrl = `/${locale}/channel/${channel.channelId}`;
      console.log(`[API] 重定向到: ${redirectUrl}`);

      // 确保重定向使用环境变量中的域名或当前请求的域名
      const absoluteRedirectUrl = new URL(redirectUrl, baseUrl);
      console.log(`[API] 完整重定向URL: ${absoluteRedirectUrl.toString()}`);

      return NextResponse.redirect(absoluteRedirectUrl);
    }

    // 数据库中找不到频道，调用外部API获取数据
    console.log(`[API] 找不到用户名为 @${username} 或 ${username} 的频道，尝试外部API查询`);
    
    // 构建POST请求体
    const channelUrl = username.startsWith('@') ? username : `@${username}`;
    const externalApiBody = {
      channel_url: channelUrl,
      max_videos: 50
    };

    try {
      // 调用外部API
      const externalApiUrl = process.env.NEXT_PUBLIC_API_BASE_URL;
      if (!externalApiUrl) {
        console.error('[API] NEXT_PUBLIC_API_BASE_URL 环境变量未定义');
        throw new Error('External API URL configuration missing');
      }

      const apiEndpoint = '/api/channel/videos/history-recent';
      const fullApiUrl = externalApiUrl.endsWith('/api/channel/videos/history-recent') 
        ? externalApiUrl 
        : `${externalApiUrl}${apiEndpoint}`;
      
      console.log(`[API] 准备调用外部API: ${fullApiUrl}`);
      console.log(`[API] 请求参数: ${JSON.stringify(externalApiBody)}`);
      
      const externalApiResponse = await fetch(fullApiUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        },
        body: JSON.stringify(externalApiBody)
      });

      console.log(`[API] 外部API响应状态: ${externalApiResponse.status} ${externalApiResponse.statusText}`);
      
      if (!externalApiResponse.ok) {
        const errorText = await externalApiResponse.text();
        console.error(`[API] 外部API请求失败: ${externalApiResponse.status} ${externalApiResponse.statusText}, 响应内容: ${errorText}`);
        throw new Error(`外部API请求失败: ${externalApiResponse.status} ${externalApiResponse.statusText}`);
      }

      const externalData = await externalApiResponse.json();
      console.log(`[API] 外部API返回数据: ${JSON.stringify(externalData).substring(0, 200)}...`);

      // 获取当前区域设置
      let locale = 'en';
      const localeMatch = request.headers.get('accept-language')?.match(/^[a-zA-Z-]+/);
      if (localeMatch && ['en', 'zh'].includes(localeMatch[0])) {
        locale = localeMatch[0];
      }

      // 构建重定向URL到一个展示外部数据的页面
      // 这里我们使用查询参数传递信息，表明这是外部API数据
      const redirectUrl = `/${locale}/channel/external?username=${encodeURIComponent(username)}&hasExternalData=true`;
      console.log(`[API] 重定向到外部数据页面: ${redirectUrl}`);
      
      // 确保重定向使用环境变量中的域名或当前请求的域名
      const absoluteRedirectUrl = new URL(redirectUrl, baseUrl);
      console.log(`[API] 完整重定向URL: ${absoluteRedirectUrl.toString()}`);

      return NextResponse.redirect(absoluteRedirectUrl);
    } catch (error) {
      console.error(`[API] 调用外部API出错:`, error);
      // 外部API也失败，返回404并带上错误信息
      return NextResponse.json({
        error: '找不到该频道信息',
        message: '该频道在系统中不存在，外部服务查询也失败',
        username: username
      }, { status: 404 });
    }
  } catch (error) {
    console.error(`[API] 处理/@${context.params.username}时出错:`, error);
    return NextResponse.json({
      error: '请求处理出错',
      message: '服务器内部错误，请稍后再试'
    }, { status: 500 });
  }
} 