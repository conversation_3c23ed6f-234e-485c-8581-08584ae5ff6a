import { NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';

// 辅助函数，用于格式化数字为带K或M单位的字符串
function formatNumber(num: number): string {
  if (num >= 1000000) {
    return (num / 1000000).toFixed(1) + 'M';
  }
  if (num >= 1000) {
    return (num / 1000).toFixed(1) + 'K';
  }
  return num.toString();
}

// 获取指定频道的时间统计数据
export async function GET(
  request: Request,
  { params }: { params: { channelId: string } }
) {
  try {
    const { searchParams } = new URL(request.url);
    const timeRange = searchParams.get('timeRange') || '30d'; // 默认为30天
    
    // 在NextJS App Router中，正确地等待params对象
    const { channelId } = await params;

    if (!channelId) {
      return NextResponse.json({ error: '频道ID是必需的' }, { status: 400 });
    }

    // 根据timeRange参数确定period_type
    const periodType = timeRange === '7d' ? 'week' : 'month';

    // 获取最近的统计数据
    const timeStats = await prisma.ytb_channel_time_statistics.findMany({
      where: {
        channel_id: channelId,
        period_type: periodType,
        deleted_at: null,
      },
      orderBy: {
        period_end: 'desc',
      },
      take: 1, // 取最新的一条记录
    });

    if (timeStats.length === 0) {
      // 如果没有找到数据，返回带有特定标识符的失败响应
      return NextResponse.json({
        success: false,
        message: 'NO_STATS_DATA', // 使用标识符而非用户可见文本
      });
    }

    const stats = timeStats[0];
    
    // 准备返回的数据
    const result = {
      views: {
        value: formatNumber(Number(stats.view_change)),
        growthRate: Number(stats.view_growth_rate) || 0,
      },
      subscribers: {
        value: formatNumber(Number(stats.subscriber_change)),
        growthRate: Number(stats.subscriber_growth_rate) || 0,
      },
      revenue: {
        value: `¥${formatNumber(Number(stats.revenue_change))}`,
        growthRate: Number(stats.revenue_growth_rate) || 0,
      },
      timeRange
    };

    return NextResponse.json({
      success: true,
      data: result,
    });
  } catch (error) {
    console.error(`Error fetching channel stats: ${error}`);
    return NextResponse.json(
      { error: '获取频道统计数据失败' },
      { status: 500 }
    );
  }
} 