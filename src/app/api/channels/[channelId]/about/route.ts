import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { differenceInYears } from 'date-fns';

// Helper function to serialize data (handle BigInt, Date, etc.)
function serializeData(data: any): any {
  if (data === null || data === undefined) {
    return data;
  }

  if (typeof data === 'bigint') {
    return data.toString();
  }

  if (data instanceof Date) {
    return data.toISOString();
  }

  if (Array.isArray(data)) {
    return data.map(item => serializeData(item));
  }

  if (typeof data === 'object') {
    const newData: Record<string, any> = {};
    for (const key in data) {
      if (Object.prototype.hasOwnProperty.call(data, key)) {
        const value = data[key];
        if (typeof value === 'bigint') {
          newData[key] = value.toString();
        } else if (value instanceof Date) {
          newData[key] = value.toISOString();
        } else {
          newData[key] = serializeData(value);
        }
      }
    }
    return newData;
  }

  return data;
}

// Function to determine category from topicCategories
function determineCategory(topicCategories: string | null): string {
  if (!topicCategories) return "Uncategorized";

  // Parse the topic categories string
  const categories = topicCategories.split(',').map(cat => cat.trim());

  // Map of YouTube topic categories to user-friendly categories
  const categoryMap: Record<string, string> = {
    "/m/04rlf": "Music",
    "/m/02mscn": "Christian music",
    "/m/0ggq0m": "Classical music",
    "/m/01lyv": "Country",
    "/m/02lkt": "Electronic music",
    "/m/0glt670": "Hip hop music",
    "/m/05rwpb": "Independent music",
    "/m/03_d0": "Jazz",
    "/m/028sqc": "Music of Asia",
    "/m/0g293": "Music of Latin America",
    "/m/064t9": "Pop music",
    "/m/06cqb": "Reggae",
    "/m/06j6l": "Rhythm and blues",
    "/m/06by7": "Rock music",
    "/m/0gywn": "Soul music",
    "/m/0bzvm2": "Gaming",
    "/m/01k8wb": "Knowledge",
    "/m/098wr": "Society",
    "/m/09s1f": "Business",
    "/m/01k8wb": "Education",
    "/m/019_rr": "Lifestyle",
    "/m/032tl": "Fashion",
    "/m/027x7n": "Beauty",
    "/m/02wbm": "Fitness",
    "/m/0kt51": "Health",
    "/m/03glg": "Hobby",
    "/m/068hy": "Pets",
    "/m/041xxh": "Technology",
    "/m/07c1v": "Technology",
    "/m/07bxq": "Tourism",
    "/m/07yv9": "Vehicles",
    "/m/01h6rj": "Entertainment",
    "/m/02jjt": "Entertainment",
    "/m/09kqc": "Humor",
    "/m/02vxn": "Movies",
    "/m/05qjc": "Performing arts",
    "/m/066wd": "Professional wrestling",
    "/m/0f2f9": "TV shows",
    "/m/019_rr": "Lifestyle",
    "/m/02wbm": "Fitness",
    "/m/0kt51": "Health",
    "/m/03glg": "Hobby",
    "/m/068hy": "Pets",
    "/m/05qt0": "Politics",
    "/m/01k8wb": "Knowledge",
    "/m/098wr": "Society",
    "/m/06bvp": "Religion",
    "/m/01h6rj": "Entertainment",
    "/m/05qjc": "Performing arts",
    "/m/0gw5w": "Science & Technology",
    "/m/07c1v": "Technology",
    "/m/01k8wb": "Education",
    "/m/07bxq": "Tourism",
    "/m/07yv9": "Vehicles",
    "/m/06ntj": "Sports",
    "/m/0jm_": "American football",
    "/m/018jz": "Baseball",
    "/m/018w8": "Basketball",
    "/m/01cgz": "Boxing",
    "/m/09xp_": "Cricket",
    "/m/02vx4": "Football",
    "/m/037hz": "Golf",
    "/m/03tmr": "Ice hockey",
    "/m/01h7lh": "Mixed martial arts",
    "/m/0410tth": "Motorsport",
    "/m/07bs0": "Tennis",
    "/m/07_53": "Volleyball",
    "/m/02jjt": "Entertainment",
    "/m/09kqc": "Humor",
    "/m/02vxn": "Movies",
    "/m/05qjc": "Performing arts",
    "/m/066wd": "Professional wrestling",
    "/m/0f2f9": "TV shows"
  };

  // Try to find a match in our category map
  for (const category of categories) {
    if (categoryMap[category]) {
      return categoryMap[category];
    }
  }

  // Default fallback
  return "Other";
}

export async function GET(
  request: NextRequest,
  { params }: { params: { channelId: string } }
) {
  try {
    const { channelId } = await params;

    if (!channelId) {
      return NextResponse.json({ error: 'Channel ID is required' }, { status: 400 });
    }

    // Get channel information from database
    const channel = await prisma.ytbChannel.findUnique({
      where: {
        channelId: channelId,
      },
      select: {
        id: true,
        title: true,
        subscriberCount: true,
        videoCount: true,
        viewCount: true,
        publishedAt: true,
        country: true,
        topicCategories: true,
        customUrl: true,
        description: true,
      },
    });

    if (!channel) {
      return NextResponse.json({ error: 'Channel not found' }, { status: 404 });
    }

    // Calculate channel age
    const channelAge = channel.publishedAt
      ? `${differenceInYears(new Date(), new Date(channel.publishedAt))} years`
      : "Unknown";

    // Determine category from topicCategories
    const category = determineCategory(channel.topicCategories);

    // Prepare response data
    const aboutData = {
      subscriberCount: channel.subscriberCount || 0,
      viewCount: channel.viewCount || 0,
      country: channel.country || "Unknown",
      category: category,
      joinDate: channel.publishedAt ? new Date(channel.publishedAt).toISOString() : null,
      channelAge: channelAge,
      description: channel.description || "",
    };

    // Return serialized data
    return NextResponse.json({
      success: true,
      data: serializeData(aboutData),
    });
  } catch (error) {
    console.error(`Error fetching channel about data: ${error}`);
    return NextResponse.json(
      { error: 'Failed to fetch channel about data' },
      { status: 500 }
    );
  }
}
