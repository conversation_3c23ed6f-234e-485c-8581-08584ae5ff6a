import { NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';

// 辅助函数，用于序列化结果中的 BigInt 和 Date 类型字段
function serializeData(data: any): any {
  if (Array.isArray(data)) {
    return data.map(serializeData);
  } else if (data !== null && typeof data === 'object') {
    const newData: { [key: string]: any } = {};
    for (const key in data) {
      if (Object.prototype.hasOwnProperty.call(data, key)) {
        const value = data[key];
        if (typeof value === 'bigint') {
          // 处理 BigInt
          if (value > BigInt(Number.MAX_SAFE_INTEGER) || value < BigInt(Number.MIN_SAFE_INTEGER)) {
            console.warn(`BigInt value for key "${key}" (${value}) exceeds safe integer limits. Converting to string.`);
            newData[key] = value.toString();
          } else {
            newData[key] = Number(value);
          }
        } else if (value instanceof Date) {
          // 处理 Date 对象
          newData[key] = value.toISOString();
        } else {
          // 递归处理嵌套对象或数组
          newData[key] = serializeData(value);
        }
      }
    }
    return newData;
  }
  return data;
}

export async function GET(
  request: Request,
  { params }: { params: { channelId: string } }
) {
  try {
    const { channelId } = await params;

    // 1. 获取频道信息
    const channel = await prisma.ytbChannel.findUnique({
      where: {
        channelId: channelId,
      },
      select: {
        id: true,
        title: true,
        subscriberCount: true,
        videoCount: true,
        viewCount: true,
        publishedAt: true,
        isVerified: true,
        thumbnailUrl: true,
        customUrl: true,
      },
    });

    if (!channel) {
      return NextResponse.json({ error: 'Channel details not found' }, { status: 404 });
    }

    // 2. 获取该频道的视频列表
    const videos = await prisma.ytbVideo.findMany({
      where: {
        channelId: channelId,
      },
      select: {
        youtubeId: true,
        title: true,
        viewCount: true,
        thumbnailUrl: true,
        likeCount: true,
        commentCount: true,
        publishedAt: true,
      },
      take: 4,
      orderBy: {
        publishedAt: 'desc'
      }
    });

    // 3. 序列化数据
    const serializedChannel = serializeData(channel);
    const serializedVideos = serializeData(videos);

    // 4. 返回组合后的数据
    return NextResponse.json({
      channel: serializedChannel,
      videos: serializedVideos,
    });

  } catch (error) {
    console.error('Error fetching channel and videos:', error);
    if (error instanceof Error) {
      console.error(`Error Name: ${error.name}, Message: ${error.message}`);
      if (error.stack) {
        console.error(`Stack Trace: ${error.stack}`);
      }
    }
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
} 