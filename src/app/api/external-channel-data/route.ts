import { NextRequest, NextResponse } from 'next/server';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { channel_url, max_videos } = body;

    if (!channel_url) {
      return NextResponse.json({ error: 'Missing channel_url' }, { status: 400 });
    }

    const externalApiUrl = process.env.NEXT_PUBLIC_API_BASE_URL;
    if (!externalApiUrl) {
      console.error('[API External Channel Data] NEXT_PUBLIC_API_BASE_URL 环境变量未定义');
      return NextResponse.json({ error: 'Internal Server Error', message: 'External API URL configuration missing' }, { status: 500 });
    }

    // 构建完整的 API URL
    const apiEndpoint = '/api/channel/videos/history-recent';
    const fullApiUrl = externalApiUrl.endsWith('/api/channel/videos/history-recent') 
      ? externalApiUrl 
      : `${externalApiUrl}${apiEndpoint}`;

    const externalApiBody = {
      channel_url,
      max_videos: max_videos || 50 // Use provided max_videos or default to 50
    };

    console.log(`[API External Channel Data] 准备调用外部 API: ${fullApiUrl}`);
    console.log(`[API External Channel Data] 频道: ${channel_url}, 最大视频数: ${max_videos || 50}`);
    console.log(`[API External Channel Data] 请求体: ${JSON.stringify(externalApiBody)}`);

    const externalApiResponse = await fetch(fullApiUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      },
      body: JSON.stringify(externalApiBody)
    });

    console.log(`[API External Channel Data] 外部 API 响应状态: ${externalApiResponse.status} ${externalApiResponse.statusText}`);

    if (!externalApiResponse.ok) {
      const errorText = await externalApiResponse.text();
      console.error(`[API External Channel Data] 外部 API 请求失败: ${externalApiResponse.status} ${externalApiResponse.statusText}`);
      console.error(`[API External Channel Data] 错误响应内容: ${errorText}`);
      return NextResponse.json({ error: 'Failed to fetch data from external API', details: errorText }, { status: externalApiResponse.status });
    }

    const externalData = await externalApiResponse.json();
    console.log(`[API External Channel Data] 成功获取外部 API 数据: ${JSON.stringify(externalData).substring(0, 200)}...`);

    return NextResponse.json(externalData);

  } catch (error) {
    console.error('[API External Channel Data] 处理请求时出错:', error);
    let errorMessage = 'Internal Server Error';
    if (error instanceof Error) {
      errorMessage = error.message;
    }
    return NextResponse.json({ error: 'Failed to process request', message: errorMessage }, { status: 500 });
  }
} 