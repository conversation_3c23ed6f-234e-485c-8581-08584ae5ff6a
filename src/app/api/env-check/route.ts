import { NextRequest, NextResponse } from 'next/server';

export async function GET(request: NextRequest) {
  // 只返回安全的环境变量，不暴露敏感信息
  const safeEnvVars = {
    NEXT_PUBLIC_WEB_URL: process.env.NEXT_PUBLIC_WEB_URL || '未设置',
    NEXT_PUBLIC_API_BASE_URL: process.env.NEXT_PUBLIC_API_BASE_URL || '未设置',
    NODE_ENV: process.env.NODE_ENV,
    SERVER_HOST: request.headers.get('host'),
    REQUEST_URL: request.url,
  };

  return NextResponse.json({
    env: safeEnvVars,
    message: '仅显示非敏感的环境变量',
    time: new Date().toISOString()
  });
} 