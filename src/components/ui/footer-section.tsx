"use client"

import * as React from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Switch } from "@/components/ui/switch"
import { Textarea } from "@/components/ui/textarea"
import {
  <PERSON>lt<PERSON>,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip"
import { Facebook, Instagram, Linkedin, Moon, Send, Sun, Twitter } from "lucide-react"
import { useTranslations } from 'next-intl';

function Footer() {
  const t = useTranslations('footer');
  const [isDarkMode, setIsDarkMode] = React.useState(true)
  // const [isChatOpen, setIsChatOpen] = React.useState(false) // Chat state not used currently

  React.useEffect(() => {
    if (isDarkMode) {
      document.documentElement.classList.add("dark")
    } else {
      document.documentElement.classList.remove("dark")
    }
  }, [isDarkMode])

  const currentYear = new Date().getFullYear();
  const companyName = "Reyoutube.com"; // Can be made dynamic later

  return (
    <footer className="relative border-t bg-background text-foreground transition-colors duration-300">
      <div className="container mx-auto px-4 py-12 md:px-6 lg:px-8">
        <div className="flex flex-col items-center justify-between gap-4 pt-8 text-center md:flex-row">
          <p className="text-sm text-muted-foreground">
            {t('copyrightNotice', { year: currentYear, companyName: companyName })}
          </p>
          <nav className="flex gap-4 text-sm">
            <a href="#" className="transition-colors hover:text-primary">
              {t('legal.privacy')}
            </a>
            <a href="#" className="transition-colors hover:text-primary">
              {t('legal.terms')}
            </a>
          </nav>
        </div>
      </div>
    </footer>
  )
}

export { Footer }