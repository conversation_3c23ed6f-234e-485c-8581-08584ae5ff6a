"use client"

import Link from "next/link"
import { useParams } from "next/navigation"
import { Youtube, Twitter, Github, Mail, BookOpen, BarChart3, Users, TrendingUp } from "lucide-react"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Separator } from "@/components/ui/separator"

export function Footer() {
  const params = useParams()
  const locale = params.locale as string || 'en'

  const footerSections = [
    {
      title: "Platform",
      links: [
        { href: `/${locale}/dashboard`, label: "Dashboard" },
        { href: `/${locale}/rankings`, label: "Rankings" },
        { href: `/${locale}/compare`, label: "Compare Channels" },
        { href: `/${locale}/reports`, label: "Reports" }
      ]
    },
    {
      title: "Learn",
      links: [
        { href: `/${locale}/blog`, label: "Blog" },
        { href: `/${locale}/guides`, label: "Guides" },
        { href: `/${locale}/resources`, label: "Resources" },
        { href: `/${locale}/courses`, label: "Courses" }
      ]
    },
    {
      title: "Support",
      links: [
        { href: `/${locale}/help`, label: "Help Center" },
        { href: `/${locale}/contact`, label: "Contact Us" },
        { href: `/${locale}/community`, label: "Community" },
        { href: `/${locale}/api-docs`, label: "API Documentation" }
      ]
    },
    {
      title: "Company",
      links: [
        { href: `/${locale}/about`, label: "About Us" },
        { href: `/${locale}/careers`, label: "Careers" },
        { href: `/${locale}/privacy`, label: "Privacy Policy" },
        { href: `/${locale}/terms`, label: "Terms of Service" },
        { href: `/${locale}/cookies`, label: "Cookie Policy" },
        { href: `/${locale}/sitemap`, label: "Sitemap" }
      ]
    }
  ]

  return (
    <footer className="bg-background border-t">
      <div className="container mx-auto px-4 py-12">
        {/* Main Footer Content */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-6 gap-8 mb-8">
          {/* Brand Section */}
          <div className="lg:col-span-2">
            <div className="flex items-center gap-2 mb-4">
              <div className="bg-primary text-primary-foreground p-2 rounded-lg">
                <BarChart3 className="h-6 w-6" />
              </div>
              <span className="text-xl font-bold">ReYoutube</span>
            </div>
            <p className="text-muted-foreground mb-4 max-w-sm">
              The most comprehensive YouTube analytics platform for creators, marketers, and analysts. 
              Grow your channel with data-driven insights and expert strategies.
            </p>
            <div className="flex items-center gap-3">
              <Button variant="outline" size="icon" asChild>
                <Link href="https://youtube.com/@reyoutube" target="_blank" rel="noopener noreferrer">
                  <Youtube className="h-4 w-4" />
                </Link>
              </Button>
              <Button variant="outline" size="icon" asChild>
                <Link href="https://twitter.com/reyoutube" target="_blank" rel="noopener noreferrer">
                  <Twitter className="h-4 w-4" />
                </Link>
              </Button>
              <Button variant="outline" size="icon" asChild>
                <Link href="https://github.com/wenhaofree/reyoutube-web" target="_blank" rel="noopener noreferrer">
                  <Github className="h-4 w-4" />
                </Link>
              </Button>
              <Button variant="outline" size="icon" asChild>
                <Link href="mailto:<EMAIL>">
                  <Mail className="h-4 w-4" />
                </Link>
              </Button>
            </div>
          </div>

          {/* Footer Links */}
          {footerSections.map((section) => (
            <div key={section.title}>
              <h3 className="font-semibold mb-3">{section.title}</h3>
              <ul className="space-y-2">
                {section.links.map((link) => (
                  <li key={link.href}>
                    <Link 
                      href={link.href}
                      className="text-muted-foreground hover:text-foreground transition-colors text-sm"
                    >
                      {link.label}
                    </Link>
                  </li>
                ))}
              </ul>
            </div>
          ))}
        </div>

        {/* Newsletter Section */}
        <div className="bg-muted/50 rounded-lg p-6 mb-8">
          <div className="max-w-2xl mx-auto text-center">
            <h3 className="text-lg font-semibold mb-2">Stay Updated with YouTube Trends</h3>
            <p className="text-muted-foreground mb-4">
              Get weekly insights, analytics tips, and growth strategies delivered to your inbox.
            </p>
            <div className="flex flex-col sm:flex-row gap-2 max-w-md mx-auto">
              <Input 
                type="email" 
                placeholder="Enter your email" 
                className="flex-1"
              />
              <Button>Subscribe</Button>
            </div>
            <p className="text-xs text-muted-foreground mt-2">
              No spam. Unsubscribe at any time.
            </p>
          </div>
        </div>

        {/* Key Features Highlight */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
          <div className="text-center">
            <div className="bg-primary/10 text-primary p-3 rounded-full w-12 h-12 flex items-center justify-center mx-auto mb-3">
              <BarChart3 className="h-6 w-6" />
            </div>
            <h4 className="font-medium mb-1">Real-time Analytics</h4>
            <p className="text-sm text-muted-foreground">
              Track your YouTube performance with live data updates and comprehensive metrics.
            </p>
          </div>
          <div className="text-center">
            <div className="bg-primary/10 text-primary p-3 rounded-full w-12 h-12 flex items-center justify-center mx-auto mb-3">
              <TrendingUp className="h-6 w-6" />
            </div>
            <h4 className="font-medium mb-1">Growth Insights</h4>
            <p className="text-sm text-muted-foreground">
              AI-powered predictions and trend analysis to accelerate your channel growth.
            </p>
          </div>
          <div className="text-center">
            <div className="bg-primary/10 text-primary p-3 rounded-full w-12 h-12 flex items-center justify-center mx-auto mb-3">
              <Users className="h-6 w-6" />
            </div>
            <h4 className="font-medium mb-1">Expert Community</h4>
            <p className="text-sm text-muted-foreground">
              Join thousands of successful creators sharing strategies and insights.
            </p>
          </div>
        </div>

        <Separator className="mb-6" />

        {/* Bottom Footer */}
        <div className="flex flex-col md:flex-row justify-between items-center gap-4">
          <div className="text-sm text-muted-foreground">
            © 2024 ReYoutube. All rights reserved.
          </div>
          <div className="flex items-center gap-6 text-sm">
            <Link href={`/${locale}/privacy`} className="text-muted-foreground hover:text-foreground">
              Privacy Policy
            </Link>
            <Link href={`/${locale}/terms`} className="text-muted-foreground hover:text-foreground">
              Terms of Service
            </Link>
            <Link href={`/${locale}/cookies`} className="text-muted-foreground hover:text-foreground">
              Cookie Policy
            </Link>
            <Link href={`/${locale}/sitemap`} className="text-muted-foreground hover:text-foreground">
              Sitemap
            </Link>
          </div>
        </div>

        {/* Trust Indicators */}
        <div className="mt-8 pt-6 border-t text-center">
          <p className="text-xs text-muted-foreground mb-2">
            Trusted by 50,000+ YouTube creators worldwide
          </p>
          <div className="flex justify-center items-center gap-8 opacity-60">
            <div className="text-xs">🔒 SSL Secured</div>
            <div className="text-xs">📊 Real-time Data</div>
            <div className="text-xs">🌍 Global Coverage</div>
            <div className="text-xs">⚡ 99.9% Uptime</div>
          </div>
        </div>
      </div>
    </footer>
  )
}
