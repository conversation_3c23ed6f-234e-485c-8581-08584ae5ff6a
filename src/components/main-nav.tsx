"use client"

import Link from "next/link"
import { usePathname } from "next/navigation"

import { cn } from "@/lib/utils"

const mainNavItems = [
  {
    href: "/dashboard",
    text: "Dashboard"
  },
  {
    href: "/channel",
    text: "Channel Analysis"
  },
  {
    href: "/rankings",
    text: "Rankings"
  },
  {
    href: "/compare",
    text: "Compare"
  },
  {
    href: "/blog",
    text: "Blog"
  },
  {
    href: "/guides",
    text: "Guides"
  },
  {
    href: "/resources",
    text: "Resources"
  },
  {
    href: "/reports",
    text: "Reports"
  }
]

export function MainNav() {
  const pathname = usePathname()

  return (
    <nav className="flex items-center space-x-4 lg:space-x-6">
      {mainNavItems.map((item) => {
        // 对于国际化路由，需要检查路径是否包含当前页面
        const isActive = pathname.includes(item.href)
        
        return (
          <Link
            key={item.href}
            href={item.href}
            className={cn(
              "text-sm font-medium transition-colors hover:text-primary",
              isActive
                ? "text-primary"
                : "text-muted-foreground"
            )}
          >
            {item.text}
          </Link>
        )
      })}
    </nav>
  )
} 