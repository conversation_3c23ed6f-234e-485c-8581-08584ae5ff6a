import { ReactNode } from "react";

interface ChartConfig {
  [key: string]: {
    label: string;
    color: string;
  };
}

interface ChartContainerProps {
  config: ChartConfig;
  children: ReactNode;
  className?: string;
}

export function ChartContainer({ config, children, className = "" }: ChartContainerProps) {
  return (
    <div className={className}>
      <style jsx global>{`
        :root {
          ${Object.entries(config)
            .map(([key, { color }]) => `--color-${key}: ${color};`)
            .join("\n")}
        }
      `}</style>
      {children}
    </div>
  );
} 