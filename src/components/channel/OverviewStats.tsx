import { Card, CardContent } from "@/components/ui/card";
import { Eye, Users, DollarSign, ArrowUpRight, ArrowDownRight, AlertTriangle, Info } from "lucide-react";
import { useState, useEffect } from "react";
import { Tabs, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { useTranslations } from 'next-intl';

interface StatCardProps {
  title: string;
  value: string;
  growthRate: number;
  icon: React.ReactNode;
  iconBgColor: string;
  iconColor: string;
  previousPeriodText: string;
}

const StatCard = ({ title, value, growthRate, icon, iconBgColor, iconColor, previousPeriodText }: StatCardProps) => {
  const isNegative = growthRate < 0;
  
  return (
    <Card>
      <CardContent className="p-6">
        <div className="flex items-center justify-between">
          <div>
            <p className="text-sm font-medium text-slate-500">{title}</p>
            <h3 className="text-2xl font-bold mt-1">{value}</h3>
          </div>
          <div className={`h-12 w-12 ${iconBgColor} rounded-full flex items-center justify-center`}>
            <div className={iconColor}>{icon}</div>
          </div>
        </div>
        <div className="flex items-center mt-4 text-sm">
          {isNegative ? (
            <ArrowDownRight className="h-4 w-4 text-red-500 mr-1" />
          ) : (
            <ArrowUpRight className="h-4 w-4 text-emerald-500 mr-1" />
          )}
          <span className={isNegative ? "text-red-500 font-medium" : "text-emerald-500 font-medium"}>
            {Math.abs(growthRate).toFixed(1)}%
          </span>
          <span className="text-slate-500 ml-1">{previousPeriodText}</span>
        </div>
      </CardContent>
    </Card>
  );
};

const StatCardSkeleton = () => (
  <Card>
    <CardContent className="p-6">
      <div className="animate-pulse">
        <div className="h-4 bg-muted rounded w-3/4 mb-2"></div>
        <div className="h-8 bg-muted rounded w-1/2 mb-4"></div>
        <div className="h-4 bg-muted rounded w-full"></div>
      </div>
    </CardContent>
  </Card>
);

interface ChannelStatsProps {
  channelId: string;
}

interface StatsData {
  views: {
    value: string;
    growthRate: number;
  };
  subscribers: {
    value: string;
    growthRate: number;
  };
  revenue: {
    value: string;
    growthRate: number;
  };
  timeRange: string;
}

export default function OverviewStats({ channelId }: ChannelStatsProps) {
  const [timeRange, setTimeRange] = useState<"7d" | "30d">("7d");
  const [statsData, setStatsData] = useState<StatsData | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  
  const t = useTranslations('channel');

  useEffect(() => {
    const fetchStats = async () => {
      setIsLoading(true);
      setError(null);
      setStatsData(null);
      
      if (!channelId) {
        console.warn('OverviewStats: No channelId provided.');
        setError(t('missingChannelId'));
        setIsLoading(false);
        return;
      }

      console.log(`Fetching stats for channel ${channelId}, range ${timeRange}`);
      
      try {
        const response = await fetch(`/api/channels/${channelId}/stats?timeRange=${timeRange}`);
        
        if (!response.ok) {
          let errorMessage = t('errorFetchingStats');
          try {
            const errorData = await response.json();
            errorMessage = errorData.message || `${t('error')}: ${response.status} ${response.statusText}`;
          } catch (e) {
             errorMessage = `${t('error')}: ${response.status} ${response.statusText}`;
          }
          console.error('API request failed:', errorMessage);
          throw new Error(errorMessage);
        }
        
        const result = await response.json();
        console.log('API response received:', result);
        
        if (result.success && result.data) {
          console.log('Stats data successfully fetched and parsed.');
          setStatsData(result.data);
        } else {
          if (result.message === 'NO_STATS_DATA') {
            console.warn('API indicated no data found.');
            setError(null); 
          } else {
            const message = result.message || t('errorFetchingStats');
            console.warn('API indicated failure:', message);
            setError(message);
          }
          setStatsData(null);
        }
      } catch (err: any) {
        console.error('Error fetching channel stats:', err);
        if (err.message === 'NO_STATS_DATA') { 
            setError(null);
        } else {
            setError(err.message || t('errorFetchingStats'));
        }
        setStatsData(null);
      } finally {
        console.log('Finished fetching stats, setting isLoading to false.');
        setIsLoading(false);
      }
    };
    
    fetchStats();
    
  }, [channelId, timeRange, t]);

  const renderContent = () => {
    if (isLoading) {
      return (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          <StatCardSkeleton />
          <StatCardSkeleton />
          <StatCardSkeleton />
        </div>
      );
    }

    if (error && !statsData) {
      return (
        <div className="bg-red-50 border-l-4 border-red-500 text-red-700 p-4 rounded text-center flex items-center justify-center space-x-2">
          <AlertTriangle className="h-5 w-5" />
          <span>{error}</span>
        </div>
      );
    }
    
    if (!statsData) {
       return (
         <div className="bg-blue-50 border-l-4 border-blue-500 text-blue-700 p-4 rounded text-center flex items-center justify-center space-x-2">
           <Info className="h-5 w-5" />
           <span>{t('noStatsData')}</span>
         </div>
       );
    }

    const cardsData = [
      {
        title: `${t(`timeRanges.${timeRange}`)} ${t('viewCount')}`,
        value: statsData.views.value,
        growthRate: statsData.views.growthRate,
        icon: <Eye className="h-6 w-6" />,
        iconBgColor: "bg-emerald-100",
        iconColor: "text-emerald-600",
      },
      {
        title: `${t(`timeRanges.${timeRange}`)} ${t('subscribers')}`,
        value: statsData.subscribers.value,
        growthRate: statsData.subscribers.growthRate,
        icon: <Users className="h-6 w-6" />,
        iconBgColor: "bg-purple-100",
        iconColor: "text-purple-600",
      },
      {
        title: `${t(`timeRanges.${timeRange}`)} ${t('estimatedRevenue')}`,
        value: statsData.revenue.value,
        growthRate: statsData.revenue.growthRate,
        icon: <DollarSign className="h-6 w-6" />,
        iconBgColor: "bg-amber-100",
        iconColor: "text-amber-600",
      },
    ];

    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {cardsData.map((stat, index) => (
          <StatCard
            key={index}
            {...stat}
            previousPeriodText={t('previousPeriod')}
          />
        ))}
      </div>
    );
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-xl font-semibold">{t('dataOverview')}</h2>
        <Tabs value={timeRange} onValueChange={(value) => setTimeRange(value as "7d" | "30d")}>
          <TabsList>
            <TabsTrigger value="7d">{t('timeRanges.7days')}</TabsTrigger>
            <TabsTrigger value="30d">{t('timeRanges.30days')}</TabsTrigger>
          </TabsList>
        </Tabs>
      </div>
      {renderContent()} 
    </div>
  );
} 