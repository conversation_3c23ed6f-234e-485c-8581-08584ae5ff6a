import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Globe } from "lucide-react"

// 模拟数据类型
interface CountryData {
  name: string
  value: number
}

// 模拟数据
const topCountriesData: CountryData[] = [
  { name: "中国", value: 65 },
  { name: "美国", value: 12 },
  { name: "印度", value: 5 },
  { name: "日本", value: 4 },
  { name: "德国", value: 3 },
  { name: "其他", value: 11 },
]

export function TopCountries() {
  return (
    <Card>
      <CardHeader>
        <CardTitle>热门国家/地区</CardTitle>
        <CardDescription>观众主要来源地</CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {topCountriesData.map((country) => (
            <div key={country.name} className="flex items-center justify-between">
              <div className="flex items-center">
                <Globe className="h-4 w-4 mr-2 text-slate-500" />
                <span>{country.name}</span>
              </div>
              <div className="flex items-center">
                <span className="font-medium">{country.value}%</span>
              </div>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  )
} 