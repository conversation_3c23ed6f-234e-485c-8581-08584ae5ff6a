import { Card, CardContent, CardDescription, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card"
import { ChartContainer, ChartTooltip, ChartTooltipContent } from "@/components/ui/chart"
import {
  ResponsiveContaine<PERSON>,
  <PERSON><PERSON><PERSON> as Recharts<PERSON><PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON>,
} from "recharts"

// 模拟数据类型
interface DeviceData {
  name: string
  value: number
}

// 模拟数据
const deviceData: DeviceData[] = [
  { name: "移动设备", value: 62 },
  { name: "桌面电脑", value: 28 },
  { name: "平板电脑", value: 7 },
  { name: "智能电视", value: 3 },
]

// 颜色配置
const COLORS = ["#0088FE", "#00C49F", "#FFBB28", "#FF8042"]

export function DeviceDistribution() {
  return (
    <Card>
      <CardHeader>
        <CardTitle>设备分布</CardTitle>
        <CardDescription>观众使用的设备类型</CardDescription>
      </CardHeader>
      <CardContent>
        <ChartContainer
          config={{
            value: {
              label: "百分比",
              color: "hsl(var(--chart-1))",
            },
          }}
          className="h-[250px]"
        >
          <ResponsiveContainer width="100%" height="100%">
            <RechartsPieChart>
              <Pie
                data={deviceData}
                cx="50%"
                cy="50%"
                labelLine={false}
                outerRadius={80}
                fill="#8884d8"
                dataKey="value"
                label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
              >
                {deviceData.map((entry, index) => (
                  <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                ))}
              </Pie>
              <ChartTooltip content={<ChartTooltipContent />} />
            </RechartsPieChart>
          </ResponsiveContainer>
        </ChartContainer>
      </CardContent>
    </Card>
  )
} 