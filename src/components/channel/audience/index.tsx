import { GeographicDistribution } from "./geographic-distribution"
import { AgeDistribution } from "./age-distribution"
import { GenderDistribution } from "./gender-distribution"
import { DeviceDistribution } from "./device-distribution"
import { TopCountries } from "./top-countries"
import { EngagementTrends } from "./engagement-trends"

export function AudienceAnalytics() {
  return (
    <>
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-6">
        <GeographicDistribution />
        <AgeDistribution />
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-6">
        <GenderDistribution />
        <DeviceDistribution />
        <TopCountries />
      </div>

      <EngagementTrends />
    </>
  )
}

export { 
  GeographicDistribution,
  AgeDistribution,
  GenderDistribution,
  DeviceDistribution,
  TopCountries,
  EngagementTrends
} 