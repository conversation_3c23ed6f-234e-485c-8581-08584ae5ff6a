import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Globe, TrendingUp, TrendingDown } from "lucide-react"
import { Badge } from "@/components/ui/badge"

// 模拟数据类型
interface GeoDistributionData {
  country: string
  percentage: number
  viewers: number
  growth: number
}

// 模拟数据
const geoData: GeoDistributionData[] = [
  {
    country: "中国",
    percentage: 45.8,
    viewers: 1250000,
    growth: 12.5
  },
  {
    country: "美国",
    percentage: 15.2,
    viewers: 415000,
    growth: 8.3
  },
  {
    country: "日本",
    percentage: 8.5,
    viewers: 232000,
    growth: -2.1
  },
  {
    country: "韩国",
    percentage: 6.3,
    viewers: 172000,
    growth: 15.7
  },
  {
    country: "新加坡",
    percentage: 4.2,
    viewers: 115000,
    growth: 5.2
  },
  {
    country: "其他",
    percentage: 20,
    viewers: 546000,
    growth: 3.8
  }
]

// 格式化数字为K/M
function formatNumber(num: number) {
  if (num >= 1000000) {
    return (num / 1000000).toFixed(1) + "M"
  }
  if (num >= 1000) {
    return (num / 1000).toFixed(1) + "K"
  }
  return num.toString()
}

export function GeographicDistribution() {
  return (
    <Card className="lg:col-span-2">
      <CardHeader>
        <CardTitle>观众地理分布</CardTitle>
        <CardDescription>观众所在国家/地区分布</CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-6">
          {/* 总览数据 */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="bg-slate-50 p-4 rounded-lg">
              <div className="text-sm text-slate-500">覆盖国家/地区</div>
              <div className="text-2xl font-bold mt-1">42</div>
            </div>
            <div className="bg-slate-50 p-4 rounded-lg">
              <div className="text-sm text-slate-500">主要市场数量</div>
              <div className="text-2xl font-bold mt-1">5</div>
            </div>
            <div className="bg-slate-50 p-4 rounded-lg">
              <div className="text-sm text-slate-500">国际化指数</div>
              <div className="text-2xl font-bold mt-1">76.3%</div>
            </div>
          </div>

          {/* 地理分布列表 */}
          <div className="space-y-4">
            {geoData.map((item) => (
              <div
                key={item.country}
                className="flex items-center justify-between p-4 bg-white rounded-lg border border-slate-200 hover:border-slate-300 transition-colors"
              >
                <div className="flex items-center space-x-4">
                  <div className="p-2 bg-slate-100 rounded-full">
                    <Globe className="h-5 w-5 text-slate-600" />
                  </div>
                  <div>
                    <div className="font-medium">{item.country}</div>
                    <div className="text-sm text-slate-500">{formatNumber(item.viewers)} 观众</div>
                  </div>
                </div>
                <div className="flex items-center space-x-4">
                  <div className="text-right">
                    <div className="font-medium">{item.percentage}%</div>
                    <div className="flex items-center text-sm">
                      {item.growth > 0 ? (
                        <>
                          <TrendingUp className="h-4 w-4 text-emerald-500 mr-1" />
                          <span className="text-emerald-500">+{item.growth}%</span>
                        </>
                      ) : (
                        <>
                          <TrendingDown className="h-4 w-4 text-red-500 mr-1" />
                          <span className="text-red-500">{item.growth}%</span>
                        </>
                      )}
                    </div>
                  </div>
                  <div className="w-24 bg-slate-100 rounded-full h-2">
                    <div
                      className="bg-emerald-500 h-2 rounded-full"
                      style={{ width: `${item.percentage}%` }}
                    />
                  </div>
                </div>
              </div>
            ))}
          </div>

          {/* 底部说明 */}
          <div className="text-sm text-slate-500 flex items-center justify-between">
            <div>* 数据更新于 2024年3月</div>
            <Badge variant="outline">实时数据</Badge>
          </div>
        </div>
      </CardContent>
    </Card>
  )
} 