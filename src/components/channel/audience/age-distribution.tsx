import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { ChartContainer, ChartTooltip, ChartTooltipContent } from "@/components/ui/chart"
import {
  ResponsiveContaine<PERSON>,
  <PERSON><PERSON><PERSON> as Recharts<PERSON><PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON>,
} from "recharts"

// 模拟数据类型
interface AgeData {
  name: string
  value: number
}

// 模拟数据
const ageData: AgeData[] = [
  { name: "18-24岁", value: 30 },
  { name: "25-34岁", value: 45 },
  { name: "35-44岁", value: 15 },
  { name: "45-54岁", value: 7 },
  { name: "55岁以上", value: 3 },
]

// 颜色配置
const COLORS = ["#0088FE", "#00C49F", "#FFBB28", "#FF8042", "#8884D8", "#82ca9d"]

export function AgeDistribution() {
  return (
    <Card>
      <CardHeader>
        <CardTitle>年龄分布</CardTitle>
        <CardDescription>观众年龄段分布</CardDescription>
      </CardHeader>
      <CardContent>
        <ChartContainer
          config={{
            value: {
              label: "百分比",
              color: "hsl(var(--chart-1))",
            },
          }}
          className="h-[300px]"
        >
          <ResponsiveContainer width="100%" height="100%">
            <RechartsPieChart>
              <Pie
                data={ageData}
                cx="50%"
                cy="50%"
                labelLine={false}
                outerRadius={80}
                fill="#8884d8"
                dataKey="value"
                label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
              >
                {ageData.map((entry, index) => (
                  <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                ))}
              </Pie>
              <ChartTooltip content={<ChartTooltipContent />} />
            </RechartsPieChart>
          </ResponsiveContainer>
        </ChartContainer>
      </CardContent>
    </Card>
  )
} 