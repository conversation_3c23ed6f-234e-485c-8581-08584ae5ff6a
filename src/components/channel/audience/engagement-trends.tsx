import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { ChartContainer, ChartTooltip, ChartTooltipContent } from "@/components/ui/chart"
import {
  Responsive<PERSON>ontaine<PERSON>,
  <PERSON><PERSON>hart as Recharts<PERSON>ar<PERSON><PERSON>,
  <PERSON>,
  XAxis,
  YAxis,
  CartesianGrid,
  Legend
} from "recharts"

// 模拟数据类型
interface EngagementData {
  name: string
  likes: number
  comments: number
  shares: number
}

// 模拟数据
const engagementData: EngagementData[] = [
  { name: "1月", likes: 3200, comments: 420, shares: 150 },
  { name: "2月", likes: 3500, comments: 450, shares: 180 },
  { name: "3月", likes: 3000, comments: 400, shares: 160 },
  { name: "4月", likes: 3800, comments: 480, shares: 200 },
  { name: "5月", likes: 4200, comments: 520, shares: 220 },
  { name: "6月", likes: 4500, comments: 550, shares: 240 },
  { name: "7月", likes: 5000, comments: 600, shares: 260 },
]

export function EngagementTrends() {
  return (
    <Card>
      <CardHeader>
        <CardTitle>观众互动趋势</CardTitle>
        <CardDescription>观众互动数据随时间变化</CardDescription>
      </CardHeader>
      <CardContent>
        <ChartContainer
          config={{
            likes: {
              label: "点赞",
              color: "hsl(var(--chart-1))",
            },
            comments: {
              label: "评论",
              color: "hsl(var(--chart-2))",
            },
            shares: {
              label: "分享",
              color: "hsl(var(--chart-3))",
            },
          }}
          className="h-80"
        >
          <ResponsiveContainer width="100%" height="100%">
            <RechartsBarChart data={engagementData} margin={{ top: 5, right: 30, left: 20, bottom: 5 }}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="name" />
              <YAxis />
              <ChartTooltip content={<ChartTooltipContent />} />
              <Legend />
              <Bar dataKey="likes" fill="var(--color-likes)" />
              <Bar dataKey="comments" fill="var(--color-comments)" />
              <Bar dataKey="shares" fill="var(--color-shares)" />
            </RechartsBarChart>
          </ResponsiveContainer>
        </ChartContainer>
      </CardContent>
    </Card>
  )
} 