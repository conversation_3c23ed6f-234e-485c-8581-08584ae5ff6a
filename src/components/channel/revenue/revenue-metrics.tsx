import { Card, CardContent } from "@/components/ui/card"
import { ArrowUpRight, ArrowDownRight } from "lucide-react"

// 模拟数据类型
interface RevenueMetric {
  name: string
  value: string
  change: string
  trend: "up" | "down"
}

// 模拟数据
const revenueMetricsData: RevenueMetric[] = [
  { name: "RPM (每千次观看收益)", value: "¥2.90", change: "+0.30", trend: "up" },
  { name: "预计年收入", value: "¥73,200", change: "+17.3%", trend: "up" },
  { name: "会员订阅增长率", value: "24.5%", change: "+3.2%", trend: "up" },
  { name: "每观看小时收益", value: "¥71.76", change: "-2.1%", trend: "down" },
]

export function RevenueMetrics() {
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
      {revenueMetricsData.map((metric) => (
        <Card key={metric.name}>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <p className="text-sm font-medium text-slate-500">{metric.name}</p>
              {metric.trend === "up" ? (
                <ArrowUpRight className="h-4 w-4 text-emerald-500" />
              ) : (
                <ArrowDownRight className="h-4 w-4 text-red-500" />
              )}
            </div>
            <h3 className="text-2xl font-bold mt-1">{metric.value}</h3>
            <div className="flex items-center mt-4 text-sm">
              {metric.trend === "up" ? (
                <span className="text-emerald-500 font-medium">{metric.change}</span>
              ) : (
                <span className="text-red-500 font-medium">{metric.change}</span>
              )}
              <span className="text-slate-500 ml-1">较上月</span>
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  )
} 