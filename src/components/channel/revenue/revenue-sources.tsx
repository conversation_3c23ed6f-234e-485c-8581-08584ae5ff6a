import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { ChartContainer, ChartTooltip, ChartTooltipContent } from "@/components/ui/chart"
import {
  ResponsiveContaine<PERSON>,
  <PERSON><PERSON><PERSON> as Recharts<PERSON><PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON>
} from "recharts"

// 模拟数据类型
interface RevenueSource {
  name: string
  value: number
}

// 模拟数据
const revenueSourcesData: RevenueSource[] = [
  { name: "广告收入", value: 65 },
  { name: "会员订阅", value: 20 },
  { name: "赞助内容", value: 10 },
  { name: "商品销售", value: 5 },
]

// 颜色配置
const COLORS = ["#0088FE", "#00C49F", "#FFBB28", "#FF8042"]

export function RevenueSources() {
  return (
    <Card>
      <CardHeader>
        <CardTitle>收益来源</CardTitle>
        <CardDescription>各收益来源占比</CardDescription>
      </CardHeader>
      <CardContent>
        <ChartContainer
          config={{
            value: {
              label: "百分比",
              color: "hsl(var(--chart-1))",
            },
          }}
          className="h-[300px]"
        >
          <ResponsiveContainer width="100%" height="100%">
            <RechartsPieChart>
              <Pie
                data={revenueSourcesData}
                cx="50%"
                cy="50%"
                labelLine={false}
                outerRadius={80}
                fill="#8884d8"
                dataKey="value"
                label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
              >
                {revenueSourcesData.map((entry, index) => (
                  <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                ))}
              </Pie>
              <ChartTooltip content={<ChartTooltipContent />} />
            </RechartsPieChart>
          </ResponsiveContainer>
        </ChartContainer>
      </CardContent>
    </Card>
  )
} 