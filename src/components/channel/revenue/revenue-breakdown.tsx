import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { ChartContainer, ChartTooltip, ChartTooltipContent } from "@/components/ui/chart"
import {
  ResponsiveContainer,
  Bar<PERSON>hart as RechartsBar<PERSON>hart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Legend
} from "recharts"

// 模拟数据类型
interface RevenueBreakdownData {
  month: string
  ads: number
  membership: number
  sponsorship: number
  merchandise: number
}

// 模拟数据
const revenueBreakdownData: RevenueBreakdownData[] = [
  { month: "1月", ads: 2800, membership: 300, sponsorship: 100, merchandise: 0 },
  { month: "2月", ads: 3200, membership: 400, sponsorship: 200, merchandise: 0 },
  { month: "3月", ads: 2900, membership: 350, sponsorship: 150, merchandise: 0 },
  { month: "4月", ads: 3500, membership: 450, sponsorship: 150, merchandise: 0 },
  { month: "5月", ads: 3800, membership: 500, sponsorship: 200, merchandise: 0 },
  { month: "6月", ads: 4300, membership: 600, sponsorship: 300, merchandise: 0 },
  { month: "7月", ads: 5000, membership: 700, sponsorship: 300, merchandise: 100 },
]

export function RevenueBreakdown() {
  return (
    <Card>
      <CardHeader>
        <CardTitle>收益明细</CardTitle>
        <CardDescription>各收益来源随时间变化</CardDescription>
      </CardHeader>
      <CardContent>
        <ChartContainer
          config={{
            ads: {
              label: "广告收入",
              color: "hsl(var(--chart-1))",
            },
            membership: {
              label: "会员订阅",
              color: "hsl(var(--chart-2))",
            },
            sponsorship: {
              label: "赞助内容",
              color: "hsl(var(--chart-3))",
            },
            merchandise: {
              label: "商品销售",
              color: "hsl(var(--chart-4))",
            },
          }}
          className="h-80"
        >
          <ResponsiveContainer width="100%" height="100%">
            <RechartsBarChart data={revenueBreakdownData} margin={{ top: 5, right: 30, left: 20, bottom: 5 }}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="month" />
              <YAxis />
              <ChartTooltip content={<ChartTooltipContent />} />
              <Legend />
              <Bar dataKey="ads" stackId="a" fill="var(--color-ads)" />
              <Bar dataKey="membership" stackId="a" fill="var(--color-membership)" />
              <Bar dataKey="sponsorship" stackId="a" fill="var(--color-sponsorship)" />
              <Bar dataKey="merchandise" stackId="a" fill="var(--color-merchandise)" />
            </RechartsBarChart>
          </ResponsiveContainer>
        </ChartContainer>
      </CardContent>
    </Card>
  )
} 