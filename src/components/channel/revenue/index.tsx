import { RevenueMetrics } from "./revenue-metrics"
import { RevenueTrend } from "./revenue-trend"
import { RevenueSources } from "./revenue-sources"
import { RevenueBreakdown } from "./revenue-breakdown"
import { RevenueForecast } from "./revenue-forecast"

export function RevenueAnalytics() {
  return (
    <>
      <RevenueMetrics />
      
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-6">
        <RevenueTrend />
        <RevenueSources />
      </div>
      
      <RevenueBreakdown />
      <RevenueForecast />
    </>
  )
}

export {
  RevenueMetrics,
  RevenueTrend,
  RevenueSources,
  RevenueBreakdown,
  RevenueForecast
} 