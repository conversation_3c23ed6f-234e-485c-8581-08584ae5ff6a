import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { TrendingUp } from "lucide-react"

// 模拟数据类型
interface ForecastData {
  month: string
  amount: string
  change: string
}

// 模拟数据
const forecastData: ForecastData[] = [
  {
    month: "8月预测",
    amount: "¥6,800",
    change: "较7月增长 11.5%"
  },
  {
    month: "9月预测",
    amount: "¥7,400",
    change: "较8月增长 8.8%"
  },
  {
    month: "10月预测",
    amount: "¥8,100",
    change: "较9月增长 9.5%"
  }
]

export function RevenueForecast() {
  return (
    <Card className="mt-6">
      <CardHeader>
        <CardTitle>收益预测</CardTitle>
        <CardDescription>未来3个月的收益预测</CardDescription>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {forecastData.map((item) => (
            <div key={item.month} className="bg-slate-50 p-6 rounded-lg">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-medium">{item.month}</h3>
                <TrendingUp className="h-5 w-5 text-emerald-500" />
              </div>
              <div className="text-3xl font-bold mb-2">{item.amount}</div>
              <div className="text-sm text-slate-500">{item.change}</div>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  )
} 