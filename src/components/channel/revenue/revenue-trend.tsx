import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { ChartContainer, ChartTooltip, ChartTooltipContent } from "@/components/ui/chart"
import {
  ResponsiveContainer,
  LineChart as RechartsLine<PERSON>hart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Legend
} from "recharts"

// 模拟数据类型
interface RevenueTrendData {
  date: string
  revenue: number
}

// 模拟数据
const viewsData: RevenueTrendData[] = [
  { date: "1月", revenue: 3200 },
  { date: "2月", revenue: 3800 },
  { date: "3月", revenue: 3400 },
  { date: "4月", revenue: 4100 },
  { date: "5月", revenue: 4500 },
  { date: "6月", revenue: 5200 },
  { date: "7月", revenue: 6100 },
]

export function RevenueTrend() {
  return (
    <Card className="lg:col-span-2">
      <CardHeader>
        <CardTitle>收益趋势</CardTitle>
        <CardDescription>过去7个月的收益数据</CardDescription>
      </CardHeader>
      <CardContent>
        <ChartContainer
          config={{
            revenue: {
              label: "收益 (¥)",
              color: "hsl(var(--chart-1))",
            },
          }}
          className="h-80"
        >
          <ResponsiveContainer width="100%" height="100%">
            <RechartsLineChart data={viewsData} margin={{ top: 5, right: 30, left: 20, bottom: 5 }}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="date" />
              <YAxis />
              <ChartTooltip content={<ChartTooltipContent />} />
              <Legend />
              <Line
                type="monotone"
                dataKey="revenue"
                stroke="var(--color-revenue)"
                strokeWidth={2}
                activeDot={{ r: 8 }}
              />
            </RechartsLineChart>
          </ResponsiveContainer>
        </ChartContainer>
      </CardContent>
    </Card>
  )
} 