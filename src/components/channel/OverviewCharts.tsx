import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardContent, CardDescription } from "@/components/ui/card";
import { ChartContainer } from "@/components/charts/ChartContainer";
import { ChartTooltipContent } from "@/components/charts/ChartTooltipContent";
import { Respons<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>hart as <PERSON><PERSON>rts<PERSON>ine<PERSON>hart, CartesianGrid, XAxis, YAxis, Tooltip, Legend, Line } from "recharts";
import { <PERSON><PERSON>, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { useState, useEffect, useRef } from "react";
import { Circle, XCircle, BarChart2 } from "lucide-react";
import { useTranslations } from 'next-intl';

interface ChartData {
  date: string;
  views: number;
  subscribers: number;
  viewsChange?: number;
  subscribersChange?: number;
}

interface ChannelDailyData {
  day: string;
  date: string;
  subsChange: string;
  subsTotal: number;
  viewsChange: string;
  viewsTotal: number;
  revenueRange: string;
  isLive?: boolean;
}

interface SummaryData {
  label: string;
  subsChange: string;
  viewsChange: string;
  revenueRange: string;
}

interface ChartResponseData {
  chartData: ChartData[];
  channelDailyData: ChannelDailyData[];
  summaryData: SummaryData[];
}

interface OverviewChartsProps {
  channelId?: string;
}

// 默认API请求模式
const USE_MOCK_DATA = false; // 使用真实API数据

// 修改组件，添加一个数据源标识
export default function OverviewCharts({ channelId = "TESTUCQOt3FI6FBeYecatZq7auyA" }: OverviewChartsProps) {
  const [timeRange, setTimeRange] = useState<"7d" | "30d">("7d");
  const [chartData, setChartData] = useState<ChartData[]>([]);
  const [channelDailyData, setChannelDailyData] = useState<ChannelDailyData[]>([]);
  const [summaryData, setSummaryData] = useState<SummaryData[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const requestTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const requestCompletedRef = useRef(false);

  // 打印channelId调试信息
  console.log('当前组件channelId:', {
    channelId,
    type: typeof channelId,
    isEmpty: !channelId,
    length: channelId?.length
  });

  // 使用internationalization hooks
  const t = useTranslations('channel');

  // 显示空数据状态的函数，用于数据加载失败或没有数据时
  const showEmptyState = () => {
    console.log('设置空数据状态');
    setChartData([]);
    setChannelDailyData([]);
    setSummaryData([]);
    setIsLoading(false);
    requestCompletedRef.current = true;
  };

  useEffect(() => {
    // 组件挂载时记录日志
    console.log('OverviewCharts组件已挂载，准备获取数据');

    // 重置请求状态
    requestCompletedRef.current = false;

    const fetchChartData = async () => {
      console.log('开始获取图表数据', { channelId, timeRange, timestamp: new Date().toISOString() });
      setIsLoading(true);
      setError(null);

      // 如果指定使用模拟数据，则直接显示空状态
      if (USE_MOCK_DATA) {
        console.log('已设置USE_MOCK_DATA=true，显示空状态');
        showEmptyState();
        return;
      }

      // 设置请求超时，3秒后如果请求未完成则显示空状态
      requestTimeoutRef.current = setTimeout(() => {
        if (!requestCompletedRef.current) {
          console.warn('请求超时，显示空状态');
          showEmptyState();
        }
      }, 3000);

      try {
        const url = `/api/channels/${channelId}/charts?timeRange=${timeRange}`;
        console.log('请求URL:', url);

        const controller = new AbortController();
        const signal = controller.signal;

        // 10秒后中止请求
        const timeoutId = setTimeout(() => controller.abort(), 10000);

        console.log('发送fetch请求', { timestamp: new Date().toISOString() });
        const response = await fetch(url, { signal });
        clearTimeout(timeoutId);

        console.log('收到响应', {
          status: response.status,
          statusText: response.statusText,
          timestamp: new Date().toISOString()
        });

        if (!response.ok) {
          console.error('API请求失败', { status: response.status, statusText: response.statusText });
          // 尝试读取错误响应的具体内容
          try {
            const errorText = await response.text();
            console.error('错误响应内容:', errorText);
          } catch (readError) {
            console.error('无法读取错误响应:', readError);
          }
          throw new Error(`${t('error')}: ${response.status} ${response.statusText}`);
        }

        console.log('解析响应JSON');
        const result = await response.json();
        console.log('获取到的数据结构:', Object.keys(result));

        if (result.success && result.data) {
          console.log('设置图表数据', {
            chartDataLength: result.data.chartData?.length || 0,
            channelDailyDataLength: result.data.channelDailyData?.length || 0,
            summaryDataLength: result.data.summaryData?.length || 0
          });

          // 检查数据是否为空
          if (
            (!result.data.chartData || result.data.chartData.length === 0) &&
            (!result.data.channelDailyData || result.data.channelDailyData.length === 0) &&
            (!result.data.summaryData || result.data.summaryData.length === 0)
          ) {
            console.log('API返回数据为空');
            setError(t('noData'));
            showEmptyState();
          } else {
            setChartData(result.data.chartData || []);
            setChannelDailyData(result.data.channelDailyData || []);
            setSummaryData(result.data.summaryData || []);
          }
        } else {
          console.error('API返回失败', result.message || '未知错误', result);
          setError(result.message || t('noData'));
          // 显示空状态
          showEmptyState();
        }
      } catch (err: any) {
        console.error('获取图表数据时发生错误:', err.message || err);
        setError(err.message || t('error'));
        // 显示空状态
        showEmptyState();
      } finally {
        if (requestTimeoutRef.current) {
          clearTimeout(requestTimeoutRef.current);
          requestTimeoutRef.current = null;
        }

        requestCompletedRef.current = true;
        console.log('数据获取流程完成，设置isLoading为false', { timestamp: new Date().toISOString() });
        setIsLoading(false);
      }
    };

    if (channelId) {
      fetchChartData();
    } else {
      console.warn('未提供channelId，跳过数据获取');
      // 如果没有channelId，显示空状态并设置错误
      setError(t('missingChannelId'));
      showEmptyState();
    }

    // 清理函数
    return () => {
      console.log('组件卸载，清理超时定时器');
      if (requestTimeoutRef.current) {
        clearTimeout(requestTimeoutRef.current);
      }
    };
  }, [channelId, timeRange, t]);

  console.log('当前组件状态:', {
    isLoading,
    error,
    chartDataLength: chartData.length,
    channelDailyDataLength: channelDailyData.length,
    summaryDataLength: summaryData.length,
    timestamp: new Date().toISOString()
  });

  // 空数据状态组件
  const EmptyState = ({ message }: { message: string }) => (
    <div className="flex flex-col items-center justify-center p-8 text-center bg-muted/30 rounded-lg h-64">
      <BarChart2 className="h-16 w-16 text-muted mb-4" />
      <h3 className="text-lg font-medium mb-2">{t('noData')}</h3>
      <p className="text-muted-foreground max-w-md">{message}</p>
    </div>
  );

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center mb-4">
        <h2 className="text-xl font-semibold">{t('viewsOverTime')}</h2>
        <Tabs value={timeRange} onValueChange={(value) => setTimeRange(value as "7d" | "30d")}>
          <TabsList>
            <TabsTrigger value="7d">{t('timeRanges.7days')}</TabsTrigger>
            <TabsTrigger value="30d">{t('timeRanges.30days')}</TabsTrigger>
          </TabsList>
        </Tabs>
      </div>

      {/* 如果API错误或无数据，显示提示 */}
      {error && (
        <div className="bg-red-50 border-l-4 border-red-500 text-red-700 p-3 mb-4 text-sm rounded">
          <div className="flex items-center">
            <XCircle className="h-5 w-5 mr-2" />
            <span>{error}</span>
          </div>
        </div>
      )}

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
        {/* 观看趋势图表 */}
        <Card>
          <CardHeader>
            <CardTitle>{t('viewsOverTime')}</CardTitle>
            <CardDescription>
              {timeRange === "7d" ? t('timeRanges.7days') + t('viewCount') : t('timeRanges.30days') + t('viewCount')}
            </CardDescription>
          </CardHeader>
          <CardContent>
            {isLoading ? (
              <div className="flex items-center justify-center h-80">
                <div className="animate-spin h-10 w-10 border-4 border-primary border-t-transparent rounded-full"></div>
              </div>
            ) : chartData.length === 0 ? (
              <EmptyState message={t('noChartData')} />
            ) : (
              <ChartContainer
                config={{
                  views: {
                    label: t('viewCount'),
                    color: "#ff0000",
                  },
                }}
                className="h-80"
              >
                <ResponsiveContainer width="100%" height="100%" aspect={1.8}>
                  <RechartsLineChart data={chartData} margin={{ top: 10, right: 30, left: 20, bottom: 10 }}>
                    <CartesianGrid strokeDasharray="3 3" stroke="rgba(0,0,0,0.05)" />
                    <XAxis dataKey="date" />
                    <YAxis
                      tickFormatter={(value) => {
                        if (value === 0) return '0';
                        if (value >= 1000000) return (value / 1000000).toFixed(1) + 'M';
                        if (value >= 1000) return (value / 1000).toFixed(1) + 'K';
                        return value.toString();
                      }}
                      domain={['dataMin - 10000', 'dataMax + 10000']}
                      scale="linear"
                      allowDecimals={false}
                      tickCount={7}
                    />
                    <Tooltip content={<ChartTooltipContent />} />
                    <Legend />
                    <Line
                      type="linear"
                      dataKey="views"
                      stroke="var(--color-views)"
                      strokeWidth={4}
                      dot={{ r: 3, strokeWidth: 2, fill: "var(--color-views)" }}
                      activeDot={{ r: 8, strokeWidth: 2 }}
                      fill="var(--color-views)"
                      fillOpacity={0.2}
                      isAnimationActive={true}
                    />
                  </RechartsLineChart>
                </ResponsiveContainer>
              </ChartContainer>
            )}
          </CardContent>
        </Card>

        {/* 订阅趋势图表 */}
        <Card>
          <CardHeader>
            <CardTitle>{t('subscribersOverTime')}</CardTitle>
            <CardDescription>
              {timeRange === "7d" ? t('timeRanges.7days') + t('subscribers') : t('timeRanges.30days') + t('subscribers')}
            </CardDescription>
          </CardHeader>
          <CardContent>
            {isLoading ? (
              <div className="flex items-center justify-center h-80">
                <div className="animate-spin h-10 w-10 border-4 border-primary border-t-transparent rounded-full"></div>
              </div>
            ) : chartData.length === 0 ? (
              <EmptyState message={t('noChartData')} />
            ) : (
              <ChartContainer
                config={{
                  subscribers: {
                    label: t('subscribers'),
                    color: "#0066ff",
                  },
                }}
                className="h-80"
              >
                <ResponsiveContainer width="100%" height="100%" aspect={1.8}>
                  <RechartsLineChart data={chartData} margin={{ top: 10, right: 30, left: 20, bottom: 10 }}>
                    <CartesianGrid strokeDasharray="3 3" stroke="rgba(0,0,0,0.05)" />
                    <XAxis dataKey="date" />
                    <YAxis
                      tickFormatter={(value) => {
                        if (value === 0) return '0';
                        if (value >= 1000000) return (value / 1000000).toFixed(1) + 'M';
                        if (value >= 1000) return (value / 1000).toFixed(1) + 'K';
                        return value.toString();
                      }}
                      domain={['dataMin - 10000', 'dataMax + 10000']}
                      scale="linear"
                      allowDecimals={false}
                      tickCount={7}
                    />
                    <Tooltip content={<ChartTooltipContent />} />
                    <Legend />
                    <Line
                      type="linear"
                      dataKey="subscribers"
                      stroke="var(--color-subscribers)"
                      strokeWidth={4}
                      dot={{ r: 3, strokeWidth: 2, fill: "var(--color-subscribers)" }}
                      activeDot={{ r: 8, strokeWidth: 2 }}
                      fill="var(--color-subscribers)"
                      fillOpacity={0.2}
                      isAnimationActive={true}
                    />
                  </RechartsLineChart>
                </ResponsiveContainer>
              </ChartContainer>
            )}
          </CardContent>
        </Card>
      </div>

      {/* 频道数据表格 */}
      <div>
        <Card>
          <CardHeader>
            <CardTitle>{t('dataOverview')}</CardTitle>
            <CardDescription>{t('timeRanges.7days')} {t('channelComparison')}</CardDescription>
          </CardHeader>
          <CardContent>
            {isLoading ? (
              <div className="flex items-center justify-center h-20">
                <div className="animate-spin h-10 w-10 border-4 border-primary border-t-transparent rounded-full"></div>
              </div>
            ) : channelDailyData.length === 0 ? (
              <EmptyState message={t('noDailyData')} />
            ) : (
              <div className="overflow-x-auto">
                <table className="w-full text-sm">
                  <thead>
                    <tr className="bg-muted border-b">
                      <th className="px-4 py-3 text-left font-medium text-muted-foreground">{t('day')}</th>
                      <th className="px-4 py-3 text-left font-medium text-muted-foreground">{t('date')}</th>
                      <th className="px-4 py-3 text-left font-medium text-muted-foreground">{t('subsChange')}</th>
                      <th className="px-4 py-3 text-left font-medium text-muted-foreground">{t('subsTotal')}</th>
                      <th className="px-4 py-3 text-left font-medium text-muted-foreground">{t('viewsChange')}</th>
                      <th className="px-4 py-3 text-left font-medium text-muted-foreground">{t('viewsTotal')}</th>
                      <th className="px-4 py-3 text-left font-medium text-muted-foreground">{t('revenueRange')}</th>
                    </tr>
                  </thead>
                  <tbody>
                    {channelDailyData.map((day, index) => (
                      <tr key={index} className="border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted">
                        <td className="px-4 py-3 flex items-center">
                          {day.isLive && (
                            <Circle className="h-3 w-3 mr-1 text-red-500 fill-red-500" />
                          )}
                          <span className={day.isLive ? "text-red-500 font-bold" : ""}>
                            {day.day}
                          </span>
                        </td>
                        <td className="px-4 py-3 text-muted-foreground">{day.date}</td>
                        <td className={`px-4 py-3 ${String(day.subsChange).startsWith("+") ? "text-green-500" : "text-red-500"}`}>
                          {day.subsChange}
                        </td>
                        <td className="px-4 py-3">{day.subsTotal.toLocaleString()}</td>
                        <td className={`px-4 py-3 ${String(day.viewsChange).startsWith("+") ? "text-green-500" : "text-red-500"}`}>
                          {day.viewsChange}
                        </td>
                        <td className="px-4 py-3">{day.viewsTotal.toLocaleString()}</td>
                        <td className="px-4 py-3 text-muted-foreground">{day.revenueRange}</td>
                      </tr>
                    ))}
                    {/* 摘要行 */}
                    {summaryData.map((summary, index) => (
                      <tr key={`summary-${index}`} className="border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted font-medium">
                        <td className="px-4 py-3" colSpan={2}>
                          {summary.label}
                        </td>
                        <td className={`px-4 py-3 ${String(summary.subsChange).startsWith("+") ? "text-green-500" : "text-red-500"}`}>
                          {summary.subsChange}
                        </td>
                        <td className="px-4 py-3"></td>
                        <td className={`px-4 py-3 ${String(summary.viewsChange).startsWith("+") ? "text-green-500" : "text-red-500"}`}>
                          {summary.viewsChange}
                        </td>
                        <td className="px-4 py-3"></td>
                        <td className="px-4 py-3 text-muted-foreground">{summary.revenueRange}</td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  );
}