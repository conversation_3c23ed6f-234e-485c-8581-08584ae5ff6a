import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card"
import { ChartContainer, ChartTooltip, ChartTooltipContent } from "@/components/ui/chart"
import {
  ResponsiveContainer,
  LineChart as Recharts<PERSON>ine<PERSON>hart,
  Line,
  XAxis,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON>tesianGrid,
  Legend
} from "recharts"
import { TrendingUp } from "lucide-react"
import { PredictionData } from "./types"

interface PredictionChartProps {
  title: string
  description: string
  data: PredictionData[]
  predictedValue: string
  change: string
  changeType: "up" | "down"
}

export function PredictionChart({
  title,
  description,
  data,
  predictedValue,
  change,
  changeType
}: PredictionChartProps) {
  return (
    <Card>
      <CardHeader className="pb-2">
        <div className="flex items-center justify-between">
          <CardTitle className="text-base">{title}</CardTitle>
          <TrendingUp className="h-5 w-5 text-purple-500" />
        </div>
      </CardHeader>
      <CardContent>
        <ChartContainer
          config={{
            actual: {
              label: "历史数据",
              color: "hsl(var(--chart-1))",
            },
            predicted: {
              label: "预测数据",
              color: "hsl(var(--chart-2))",
            },
            upperBound: {
              label: "上限区间",
              color: "hsl(var(--chart-3))",
            },
            lowerBound: {
              label: "下限区间",
              color: "hsl(var(--chart-4))",
            },
          }}
          className="h-80"
        >
          <ResponsiveContainer width="100%" height="100%">
            <RechartsLineChart
              data={data}
              margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
            >
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="date" allowDataOverflow={true} />
              <YAxis />
              <ChartTooltip content={<ChartTooltipContent />} />
              <Legend />
              <Line
                type="monotone"
                dataKey="actual"
                stroke="var(--color-actual)"
                strokeWidth={2}
                dot={{ r: 3 }}
                activeDot={{ r: 8 }}
              />
              <Line
                type="monotone"
                dataKey="predicted"
                stroke="var(--color-predicted)"
                strokeWidth={2}
                strokeDasharray="5 5"
                dot={{ r: 3 }}
              />
              <Line
                type="monotone"
                dataKey="upperBound"
                stroke="var(--color-upperBound)"
                strokeWidth={1}
                strokeDasharray="3 3"
                dot={false}
              />
              <Line
                type="monotone"
                dataKey="lowerBound"
                stroke="var(--color-lowerBound)"
                strokeWidth={1}
                strokeDasharray="3 3"
                dot={false}
              />
            </RechartsLineChart>
          </ResponsiveContainer>
        </ChartContainer>
        <div className="mt-4 flex items-center justify-between">
          <div>
            <div className="text-sm font-medium text-slate-500">{description}</div>
            <div className="text-2xl font-bold">{predictedValue}</div>
          </div>
          <div className={`flex items-center ${changeType === 'up' ? 'text-emerald-500' : 'text-red-500'}`}>
            <TrendingUp className="mr-1 h-5 w-5" />
            <span className="font-medium">{change}</span>
          </div>
        </div>
      </CardContent>
    </Card>
  )
} 