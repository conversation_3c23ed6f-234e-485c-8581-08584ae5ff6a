import { <PERSON>, CardContent } from "@/components/ui/card"
import { PredictionHeader } from "./prediction-header"
import { PredictionAlert } from "./prediction-alert"
import { PredictionChartsGrid } from "./prediction-charts-grid"
import { ImpactFactors } from "./impact-factors"

export function PredictionAnalytics() {
  return (
    <Card className="mb-6">
      <PredictionHeader />
      <CardContent>
        <PredictionAlert />
        <PredictionChartsGrid />
        <ImpactFactors />
      </CardContent>
    </Card>
  )
}

export {
  PredictionHeader,
  PredictionAlert,
  PredictionChartsGrid,
  ImpactFactors
} 