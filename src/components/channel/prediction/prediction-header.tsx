import { Card<PERSON>escription, CardHeader, CardTitle } from "@/components/ui/card"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"

export function PredictionHeader() {
  return (
    <CardHeader>
      <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
        <div>
          <CardTitle>趋势预测分析</CardTitle>
          <CardDescription>基于历史数据的未来表现预测</CardDescription>
        </div>
        <div className="flex flex-col sm:flex-row gap-2">
          <Select defaultValue="30days">
            <SelectTrigger className="w-[180px]">
              <SelectValue placeholder="预测时间范围" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="7days">未来7天</SelectItem>
              <SelectItem value="30days">未来30天</SelectItem>
              <SelectItem value="90days">未来90天</SelectItem>
              <SelectItem value="6months">未来6个月</SelectItem>
            </SelectContent>
          </Select>
          <Select defaultValue="all">
            <SelectTrigger className="w-[180px]">
              <SelectValue placeholder="预测指标" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">所有指标</SelectItem>
              <SelectItem value="views">观看量</SelectItem>
              <SelectItem value="subscribers">订阅量</SelectItem>
              <SelectItem value="revenue">收益</SelectItem>
              <SelectItem value="engagement">互动率</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>
    </CardHeader>
  )
} 