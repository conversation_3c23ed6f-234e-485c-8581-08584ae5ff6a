import { PredictionChart } from "./prediction-chart"
import { predictionData } from "./mock-data"

export function PredictionChartsGrid() {
  return (
    <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
      <PredictionChart 
        title="观看量预测"
        description="30天后预计观看量"
        data={predictionData}
        predictedValue="2.4M"
        change="+14.3%"
        changeType="up"
      />
      
      <PredictionChart 
        title="订阅量预测"
        description="30天后预计订阅量"
        data={predictionData}
        predictedValue="2.52M"
        change="+2.9%"
        changeType="up"
      />
      
      <PredictionChart 
        title="收益预测"
        description="30天后预计月收益"
        data={predictionData}
        predictedValue="¥7,200"
        change="+18.0%"
        changeType="up"
      />
      
      <PredictionChart 
        title="互动率预测"
        description="30天后预计互动率"
        data={predictionData}
        predictedValue="9.8%"
        change="+0.3%"
        changeType="up"
      />
    </div>
  )
} 