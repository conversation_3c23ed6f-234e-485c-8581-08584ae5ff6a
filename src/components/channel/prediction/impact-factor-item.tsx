import { 
  TrendingUp, 
  TrendingDown, 
  Clock, 
  <PERSON>, 
  ThumbsUp, 
  <PERSON><PERSON><PERSON>, 
  MessageSquare 
} from "lucide-react"
import { ImpactFactor, FactorType } from "./types"
import { Badge } from "@/components/ui/badge"

interface ImpactFactorItemProps {
  factor: ImpactFactor
}

export function ImpactFactorItem({ factor }: ImpactFactorItemProps) {
  const getIcon = (iconName: string) => {
    switch (iconName) {
      case "trend-up":
        return <TrendingUp className="h-5 w-5" />
      case "trend-down":
        return <TrendingDown className="h-5 w-5" />
      case "clock":
        return <Clock className="h-5 w-5" />
      case "users":
        return <Users className="h-5 w-5" />
      case "thumbs-up":
        return <ThumbsUp className="h-5 w-5" />
      case "bar-chart":
        return <BarChart className="h-5 w-5" />
      case "message-square":
        return <MessageSquare className="h-5 w-5" />
      default:
        return <Bar<PERSON>hart className="h-5 w-5" />
    }
  }

  const getStyleByType = (type: FactorType) => {
    switch (type) {
      case "positive":
        return {
          containerClass: "bg-emerald-50 p-3 rounded-lg",
          iconBgClass: "bg-emerald-100 p-2 rounded-full mr-3",
          iconClass: "text-emerald-600"
        }
      case "negative":
        return {
          containerClass: "bg-red-50 p-3 rounded-lg",
          iconBgClass: "bg-red-100 p-2 rounded-full mr-3",
          iconClass: "text-red-600"
        }
      case "suggestion":
        return {
          containerClass: "bg-blue-50 p-3 rounded-lg",
          iconBgClass: "bg-blue-100 p-2 rounded-full mr-3",
          iconClass: "text-blue-600"
        }
    }
  }

  const style = getStyleByType(factor.type)
  
  return (
    <div className={style.containerClass}>
      <div className="flex items-center mb-2">
        <div className={style.iconBgClass}>
          <span className={style.iconClass}>{getIcon(factor.icon)}</span>
        </div>
        <p className="font-medium">{factor.title}</p>
        {factor.impact && (
          <Badge 
            className={`ml-auto ${factor.type === 'positive' ? 'bg-emerald-100 text-emerald-800 hover:bg-emerald-100' : 'bg-red-100 text-red-800 hover:bg-red-100'}`}
          >
            {factor.impact}
          </Badge>
        )}
      </div>
      <p className="text-sm text-slate-600">
        {factor.description}
      </p>
    </div>
  )
} 