import { Card, CardContent, CardDescription, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card"
import { ImpactFactorItem } from "./impact-factor-item"
import { impactFactors } from "./mock-data"

export function ImpactFactors() {
  // 过滤出不同类型的因素
  const positiveFactors = impactFactors.filter(factor => factor.type === "positive")
  const negativeFactors = impactFactors.filter(factor => factor.type === "negative")
  const suggestionFactors = impactFactors.filter(factor => factor.type === "suggestion")

  return (
    <Card>
      <CardHeader>
        <CardTitle>影响因素分析</CardTitle>
        <CardDescription>影响频道未来表现的关键因素</CardDescription>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="space-y-4">
            <h3 className="font-medium text-lg">正面因素</h3>
            <div className="space-y-3">
              {positiveFactors.map((factor, index) => (
                <ImpactFactorItem key={`positive-${index}`} factor={factor} />
              ))}
            </div>
          </div>

          <div className="space-y-4">
            <h3 className="font-medium text-lg">负面因素</h3>
            <div className="space-y-3">
              {negativeFactors.map((factor, index) => (
                <ImpactFactorItem key={`negative-${index}`} factor={factor} />
              ))}
            </div>
          </div>

          <div className="space-y-4">
            <h3 className="font-medium text-lg">建议措施</h3>
            <div className="space-y-3">
              {suggestionFactors.map((factor, index) => (
                <ImpactFactorItem key={`suggestion-${index}`} factor={factor} />
              ))}
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  )
} 