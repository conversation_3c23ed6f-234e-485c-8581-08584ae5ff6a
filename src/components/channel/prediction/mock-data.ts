import { PredictionData, ImpactFactor } from "./types"

// 预测数据
export const predictionData: PredictionData[] = [
  { date: "1月", actual: 1200000, predicted: null, upperBound: null, lowerBound: null },
  { date: "2月", actual: 1350000, predicted: null, upperBound: null, lowerBound: null },
  { date: "3月", actual: 1100000, predicted: null, upperBound: null, lowerBound: null },
  { date: "4月", actual: 1450000, predicted: null, upperBound: null, lowerBound: null },
  { date: "5月", actual: 1600000, predicted: null, upperBound: null, lowerBound: null },
  { date: "6月", actual: 1800000, predicted: null, upperBound: null, lowerBound: null },
  { date: "7月", actual: 2100000, predicted: null, upperBound: null, lowerBound: null },
  { date: "8月", actual: null, predicted: 2400000, upperBound: 2600000, lowerBound: 2200000 },
  { date: "9月", actual: null, predicted: 2650000, upperBound: 2900000, lowerBound: 2400000 },
  { date: "10月", actual: null, predicted: 2850000, upperBound: 3200000, lowerBound: 2500000 },
]

// 影响因素数据
export const impactFactors: ImpactFactor[] = [
  {
    type: "positive",
    title: "内容发布频率",
    description: "每周发布频率高于行业平均",
    icon: "trend-up",
    impact: "+8.2%"
  },
  {
    type: "positive",
    title: "互动率提升",
    description: "观众互动率持续增长",
    icon: "thumbs-up",
    impact: "+5.7%"
  },
  {
    type: "positive",
    title: "受众增长",
    description: "核心受众群体稳定增长",
    icon: "users",
    impact: "+4.3%"
  },
  {
    type: "negative",
    title: "平台算法变化",
    description: "平台推荐算法调整",
    icon: "trend-down",
    impact: "-3.1%"
  },
  {
    type: "negative",
    title: "观看时长下降",
    description: "部分内容观看完成率降低",
    icon: "clock",
    impact: "-2.5%"
  },
  {
    type: "suggestion",
    title: "优化内容长度",
    description: "根据数据分析，8-12分钟的内容表现最佳，建议调整内容长度至此区间",
    icon: "bar-chart"
  },
  {
    type: "suggestion",
    title: "扩展目标受众",
    description: "数据显示25-34岁人群增长潜力大，建议增加针对此人群的内容",
    icon: "users"
  },
  {
    type: "suggestion",
    title: "提高互动率",
    description: "增加互动环节和号召性用语，鼓励观众评论和分享",
    icon: "message-square"
  }
] 