// 预测数据类型
export interface PredictionData {
  date: string
  actual: number | null
  predicted: number | null
  upperBound: number | null
  lowerBound: number | null
}

// 影响因素类型
export type FactorType = "positive" | "negative" | "suggestion"

export interface ImpactFactor {
  type: FactorType
  title: string
  description: string
  icon: string
  impact?: string
}

// 预测指标卡片类型
export interface PredictionMetric {
  title: string
  value: string
  change: string
  trend: "up" | "down"
} 