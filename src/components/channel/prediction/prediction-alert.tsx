import { AlertCircle } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"

export function PredictionAlert() {
  return (
    <div className="flex items-center justify-between mb-6 bg-amber-50 p-4 rounded-lg border border-amber-200">
      <div className="flex items-center">
        <AlertCircle className="h-5 w-5 text-amber-500 mr-2" />
        <p className="text-sm text-amber-700">
          预测结果基于历史数据分析，实际结果可能因多种因素而有所不同
        </p>
      </div>
      <Button variant="outline" size="sm" className="text-amber-600 border-amber-200">
        了解预测方法
      </Button>
    </div>
  )
} 