import Head from 'next/head'

interface SEOHeadProps {
  title: string
  description: string
  keywords?: string
  canonicalUrl?: string
  ogImage?: string
  ogType?: string
  twitterCard?: string
  author?: string
  publishedTime?: string
  modifiedTime?: string
  locale?: string
  alternateLocales?: { locale: string; url: string }[]
  structuredData?: object
}

export function SEOHead({
  title,
  description,
  keywords,
  canonicalUrl,
  ogImage = '/images/og-default.jpg',
  ogType = 'website',
  twitterCard = 'summary_large_image',
  author,
  publishedTime,
  modifiedTime,
  locale = 'en',
  alternateLocales = [],
  structuredData
}: SEOHeadProps) {
  const siteName = 'ReYoutube'
  const fullTitle = title.includes(siteName) ? title : `${title} | ${siteName}`
  const baseUrl = process.env.NEXT_PUBLIC_WEB_URL || 'https://www.reyoutube.com'
  const fullCanonicalUrl = canonicalUrl || baseUrl
  const fullOgImage = ogImage.startsWith('http') ? ogImage : `${baseUrl}${ogImage}`

  return (
    <Head>
      {/* Basic Meta Tags */}
      <title>{fullTitle}</title>
      <meta name="description" content={description} />
      {keywords && <meta name="keywords" content={keywords} />}
      {author && <meta name="author" content={author} />}
      <meta name="robots" content="index, follow" />
      <meta name="viewport" content="width=device-width, initial-scale=1" />
      
      {/* Canonical URL */}
      <link rel="canonical" href={fullCanonicalUrl} />
      
      {/* Language and Locale */}
      <meta httpEquiv="content-language" content={locale} />
      <html lang={locale} />
      
      {/* Alternate Language Versions */}
      {alternateLocales.map(({ locale: altLocale, url }) => (
        <link key={altLocale} rel="alternate" hrefLang={altLocale} href={url} />
      ))}
      
      {/* Open Graph Meta Tags */}
      <meta property="og:title" content={fullTitle} />
      <meta property="og:description" content={description} />
      <meta property="og:type" content={ogType} />
      <meta property="og:url" content={fullCanonicalUrl} />
      <meta property="og:image" content={fullOgImage} />
      <meta property="og:image:alt" content={title} />
      <meta property="og:site_name" content={siteName} />
      <meta property="og:locale" content={locale.replace('-', '_')} />
      
      {/* Article specific Open Graph tags */}
      {ogType === 'article' && (
        <>
          {author && <meta property="article:author" content={author} />}
          {publishedTime && <meta property="article:published_time" content={publishedTime} />}
          {modifiedTime && <meta property="article:modified_time" content={modifiedTime} />}
        </>
      )}
      
      {/* Twitter Card Meta Tags */}
      <meta name="twitter:card" content={twitterCard} />
      <meta name="twitter:title" content={fullTitle} />
      <meta name="twitter:description" content={description} />
      <meta name="twitter:image" content={fullOgImage} />
      <meta name="twitter:site" content="@reyoutube" />
      <meta name="twitter:creator" content="@reyoutube" />
      
      {/* Additional Meta Tags for YouTube Analytics Site */}
      <meta name="application-name" content={siteName} />
      <meta name="theme-color" content="#0066cc" />
      <meta name="msapplication-TileColor" content="#0066cc" />
      
      {/* Favicon and Icons */}
      <link rel="icon" type="image/x-icon" href="/favicon.ico" />
      <link rel="icon" type="image/png" sizes="32x32" href="/favicon-32x32.png" />
      <link rel="icon" type="image/png" sizes="16x16" href="/favicon-16x16.png" />
      <link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png" />
      <link rel="manifest" href="/site.webmanifest" />
      
      {/* Structured Data */}
      {structuredData && (
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{
            __html: JSON.stringify(structuredData)
          }}
        />
      )}
      
      {/* Additional SEO Meta Tags */}
      <meta name="format-detection" content="telephone=no" />
      <meta name="mobile-web-app-capable" content="yes" />
      <meta name="apple-mobile-web-app-capable" content="yes" />
      <meta name="apple-mobile-web-app-status-bar-style" content="default" />
      
      {/* Preconnect to External Domains */}
      <link rel="preconnect" href="https://fonts.googleapis.com" />
      <link rel="preconnect" href="https://fonts.gstatic.com" crossOrigin="anonymous" />
      <link rel="preconnect" href="https://www.google-analytics.com" />
      <link rel="preconnect" href="https://www.googletagmanager.com" />
      
      {/* DNS Prefetch for Performance */}
      <link rel="dns-prefetch" href="//fonts.googleapis.com" />
      <link rel="dns-prefetch" href="//www.google-analytics.com" />
      <link rel="dns-prefetch" href="//www.googletagmanager.com" />
    </Head>
  )
}

// Utility function to generate structured data for different page types
export const generateStructuredData = {
  website: (name: string, url: string, description: string) => ({
    "@context": "https://schema.org",
    "@type": "WebSite",
    "name": name,
    "url": url,
    "description": description,
    "potentialAction": {
      "@type": "SearchAction",
      "target": {
        "@type": "EntryPoint",
        "urlTemplate": `${url}/search?q={search_term_string}`
      },
      "query-input": "required name=search_term_string"
    }
  }),
  
  organization: (name: string, url: string, logo: string, description: string) => ({
    "@context": "https://schema.org",
    "@type": "Organization",
    "name": name,
    "url": url,
    "logo": logo,
    "description": description,
    "sameAs": [
      "https://twitter.com/reyoutube",
      "https://github.com/wenhaofree/reyoutube-web",
      "https://youtube.com/@reyoutube"
    ]
  }),
  
  article: (title: string, description: string, url: string, author: string, publishedTime: string, modifiedTime?: string) => ({
    "@context": "https://schema.org",
    "@type": "Article",
    "headline": title,
    "description": description,
    "url": url,
    "author": {
      "@type": "Person",
      "name": author
    },
    "publisher": {
      "@type": "Organization",
      "name": "ReYoutube",
      "logo": {
        "@type": "ImageObject",
        "url": `${process.env.NEXT_PUBLIC_WEB_URL}/images/logo.png`
      }
    },
    "datePublished": publishedTime,
    "dateModified": modifiedTime || publishedTime
  }),
  
  howTo: (title: string, description: string, steps: string[]) => ({
    "@context": "https://schema.org",
    "@type": "HowTo",
    "name": title,
    "description": description,
    "step": steps.map((step, index) => ({
      "@type": "HowToStep",
      "position": index + 1,
      "text": step
    }))
  }),
  
  faq: (questions: { question: string; answer: string }[]) => ({
    "@context": "https://schema.org",
    "@type": "FAQPage",
    "mainEntity": questions.map(({ question, answer }) => ({
      "@type": "Question",
      "name": question,
      "acceptedAnswer": {
        "@type": "Answer",
        "text": answer
      }
    }))
  }),
  
  breadcrumb: (items: { name: string; url: string }[]) => ({
    "@context": "https://schema.org",
    "@type": "BreadcrumbList",
    "itemListElement": items.map((item, index) => ({
      "@type": "ListItem",
      "position": index + 1,
      "name": item.name,
      "item": item.url
    }))
  })
}

// Default SEO configurations for different page types
export const defaultSEOConfig = {
  home: {
    title: "ReYoutube - YouTube Analytics & Channel Growth Platform",
    description: "The most comprehensive YouTube analytics platform for creators, marketers, and analysts. Analyze channels, track performance, and grow your YouTube presence with data-driven insights.",
    keywords: "YouTube analytics, channel analytics, YouTube SEO, video analytics, creator tools, YouTube growth, social media analytics"
  },
  
  blog: {
    title: "YouTube Analytics Blog - Expert Insights & Strategies",
    description: "Expert insights, strategies, and guides to help you master YouTube analytics, grow your channel, and maximize your content's potential.",
    keywords: "YouTube analytics blog, YouTube growth strategies, video marketing, content creation, YouTube SEO tips"
  },
  
  guides: {
    title: "YouTube Analytics Guides - Step-by-Step Tutorials",
    description: "Comprehensive, step-by-step guides to help you master YouTube analytics, optimize your content, and grow your channel with data-driven strategies.",
    keywords: "YouTube analytics guides, YouTube tutorials, channel growth guides, video optimization, analytics fundamentals"
  },
  
  about: {
    title: "About ReYoutube - YouTube Analytics Platform",
    description: "Learn about ReYoutube's mission to democratize YouTube analytics and empower creators with professional-grade insights and tools.",
    keywords: "about ReYoutube, YouTube analytics company, creator tools, video analytics platform"
  },
  
  contact: {
    title: "Contact ReYoutube - Support & Help",
    description: "Get in touch with ReYoutube's support team. We're here to help you with YouTube analytics, technical support, and any questions you may have.",
    keywords: "ReYoutube contact, YouTube analytics support, customer service, help center"
  },
  
  privacy: {
    title: "Privacy Policy - ReYoutube",
    description: "Learn how ReYoutube protects your privacy and handles your personal information. Our commitment to data security and transparency.",
    keywords: "privacy policy, data protection, GDPR compliance, user privacy"
  },
  
  terms: {
    title: "Terms of Service - ReYoutube",
    description: "Terms and conditions for using ReYoutube's YouTube analytics platform. User rights, responsibilities, and service guidelines.",
    keywords: "terms of service, user agreement, service terms, legal terms"
  }
}
