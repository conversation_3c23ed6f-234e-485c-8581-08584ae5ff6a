"use client";

import { useTranslations } from 'next-intl';

export function Features() {
  const t = useTranslations('home');
  
  return (
    <section className="w-full py-12 md:py-16">
      <div className="container mx-auto px-4 md:px-6">
        <div className="max-w-xl mx-auto">
          <div className="relative">
            <div className="relative p-4 rounded-lg border-2 border-red-500 bg-white dark:bg-gray-900 shadow-md mb-20">
              <div className="flex items-center bg-gray-100 dark:bg-gray-800 rounded px-3 py-2">
                <span className="flex-shrink-0 text-gray-500 dark:text-gray-400 mr-2">🔒</span>
                <span className="text-gray-600 dark:text-gray-300 font-medium italic">
                  https://www.<span id="re-highlight" className="text-red-500 font-bold relative">
                    re
                    {/* 定位箭头到"re"的正下方 */}
                    <div className="absolute -bottom-16 left-1/2 transform -translate-x-1/2 z-10">
                      <svg className="icon w-8 h-8" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg">
                        <path d="M998.4 970.666667c-258.133333-89.6-428.8-298.666667-441.6-535.466667h204.8c8.533333 0 17.066667-4.266667 19.2-12.8 4.266667-8.533333 2.133333-17.066667-4.266667-23.466667L411.733333 17.066667c-8.533333-8.533333-23.466667-8.533333-29.866666 0L17.066667 398.933333c-6.4 6.4-8.533333 14.933333-4.266667 23.466667 4.266667 8.533333 10.666667 12.8 19.2 12.8h189.866667c42.666667 326.4 364.8 578.133333 744.533333 578.133333h25.6c10.666667 0 19.2-8.533333 21.333333-17.066666 2.133333-12.8-4.266667-21.333333-14.933333-25.6z" fill="#d81e06"></path>
                      </svg>
                      <p className="text-center text-[#777777] text-xs whitespace-nowrap mt-1">
                        {t('features.quickDataAccess')}
                      </p>
                    </div>
                  </span>youtube.com/@openai
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
