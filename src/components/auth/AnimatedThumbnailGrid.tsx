import Image from "next/image";
import { cn } from "@/lib/utils"; // 假设你的 utils 文件路径

// 从参考代码中提取的图片 URL 列表
// 增加了列数（复制了一些列）以更好地填充水平空间
const imageUrls = [
  // Row 1 (Scroll Up)
  [
    "https://storage.googleapis.com/icons-vs/login-signup/beast/mrbeast_3.jpg",
    "https://storage.googleapis.com/icons-vs/login-signup/popular-channels/popular_2.jpg",
    "https://storage.googleapis.com/icons-vs/login-signup/popular-channels/popular_4.jpg",
    "https://storage.googleapis.com/icons-vs/login-signup/popular-channels/popular_6.jpg",
    "https://storage.googleapis.com/icons-vs/login-signup/beast/mrbeast_2.jpg",
    "https://storage.googleapis.com/icons-vs/login-signup/beast/mrbeast_1.jpg",
    "https://storage.googleapis.com/icons-vs/login-signup/popular-channels/popular_1.jpg",
    "https://storage.googleapis.com/icons-vs/login-signup/popular-channels/popular_3.jpg",
  ],
  // Row 2 (Scroll Down)
  [
    "https://storage.googleapis.com/icons-vs/login-signup/beast/mrbeast_4.jpg",
    "https://storage.googleapis.com/icons-vs/login-signup/beast/mrbeast_5.jpg",
    "https://storage.googleapis.com/icons-vs/login-signup/popular-channels/popular_12.jpg",
    "https://storage.googleapis.com/icons-vs/login-signup/popular-channels/popular_11.jpg",
    "https://storage.googleapis.com/icons-vs/login-signup/popular-channels/popular_9.jpg",
    "https://storage.googleapis.com/icons-vs/login-signup/popular-channels/popular_7.jpg",
    "https://storage.googleapis.com/icons-vs/login-signup/popular-channels/popular_8.jpg",
    "https://storage.googleapis.com/icons-vs/login-signup/beast/mrbeast_6.jpg",
  ],
   // Row 3 (Scroll Up)
  [
    "https://storage.googleapis.com/icons-vs/login-signup/popular-channels/popular_14.jpg",
    "https://storage.googleapis.com/icons-vs/login-signup/popular-channels/popular_17.jpg",
    "https://storage.googleapis.com/icons-vs/login-signup/beast/mrbeast_8.jpg",
    "https://storage.googleapis.com/icons-vs/login-signup/popular-channels/popular_15.jpg",
    "https://storage.googleapis.com/icons-vs/login-signup/popular-channels/popular_13.jpg",
    "https://storage.googleapis.com/icons-vs/login-signup/popular-channels/popular_16.jpg",
    "https://storage.googleapis.com/icons-vs/login-signup/beast/mrbeast_7.jpg",
    "https://storage.googleapis.com/icons-vs/login-signup/popular-channels/popular_18.jpg",
  ],
   // Row 4 (Scroll Down)
  [
    "https://storage.googleapis.com/icons-vs/login-signup/popular-channels/popular_20.jpg",
    "https://storage.googleapis.com/icons-vs/login-signup/popular-channels/popular_19.jpg",
    "https://storage.googleapis.com/icons-vs/login-signup/beast/mrbeast_11.jpg",
    "https://storage.googleapis.com/icons-vs/login-signup/popular-channels/popular_23.jpg",
    "https://storage.googleapis.com/icons-vs/login-signup/beast/mrbeast_10.jpg",
    "https://storage.googleapis.com/icons-vs/login-signup/beast/mrbeast_12.jpg",
    "https://storage.googleapis.com/icons-vs/login-signup/popular-channels/popular_21.jpg",
    "https://storage.googleapis.com/icons-vs/login-signup/popular-channels/popular_22.jpg",
  ],
  // Row 5 (Scroll Up) - Copied Row 1
  [
    "https://storage.googleapis.com/icons-vs/login-signup/beast/mrbeast_3.jpg",
    "https://storage.googleapis.com/icons-vs/login-signup/popular-channels/popular_2.jpg",
    "https://storage.googleapis.com/icons-vs/login-signup/popular-channels/popular_4.jpg",
    "https://storage.googleapis.com/icons-vs/login-signup/popular-channels/popular_6.jpg",
    "https://storage.googleapis.com/icons-vs/login-signup/beast/mrbeast_2.jpg",
    "https://storage.googleapis.com/icons-vs/login-signup/beast/mrbeast_1.jpg",
    "https://storage.googleapis.com/icons-vs/login-signup/popular-channels/popular_1.jpg",
    "https://storage.googleapis.com/icons-vs/login-signup/popular-channels/popular_3.jpg",
  ],
   // Row 6 (Scroll Down) - Copied Row 2
  [
    "https://storage.googleapis.com/icons-vs/login-signup/beast/mrbeast_4.jpg",
    "https://storage.googleapis.com/icons-vs/login-signup/beast/mrbeast_5.jpg",
    "https://storage.googleapis.com/icons-vs/login-signup/popular-channels/popular_12.jpg",
    "https://storage.googleapis.com/icons-vs/login-signup/popular-channels/popular_11.jpg",
    "https://storage.googleapis.com/icons-vs/login-signup/popular-channels/popular_9.jpg",
    "https://storage.googleapis.com/icons-vs/login-signup/popular-channels/popular_7.jpg",
    "https://storage.googleapis.com/icons-vs/login-signup/popular-channels/popular_8.jpg",
    "https://storage.googleapis.com/icons-vs/login-signup/beast/mrbeast_6.jpg",
  ],
    // Row 7 (Scroll Up) - Copied Row 3
  [
    "https://storage.googleapis.com/icons-vs/login-signup/popular-channels/popular_14.jpg",
    "https://storage.googleapis.com/icons-vs/login-signup/popular-channels/popular_17.jpg",
    "https://storage.googleapis.com/icons-vs/login-signup/beast/mrbeast_8.jpg",
    "https://storage.googleapis.com/icons-vs/login-signup/popular-channels/popular_15.jpg",
    "https://storage.googleapis.com/icons-vs/login-signup/popular-channels/popular_13.jpg",
    "https://storage.googleapis.com/icons-vs/login-signup/popular-channels/popular_16.jpg",
    "https://storage.googleapis.com/icons-vs/login-signup/beast/mrbeast_7.jpg",
    "https://storage.googleapis.com/icons-vs/login-signup/popular-channels/popular_18.jpg",
  ],
   // Row 8 (Scroll Down) - Copied Row 4
  [
    "https://storage.googleapis.com/icons-vs/login-signup/popular-channels/popular_20.jpg",
    "https://storage.googleapis.com/icons-vs/login-signup/popular-channels/popular_19.jpg",
    "https://storage.googleapis.com/icons-vs/login-signup/beast/mrbeast_11.jpg",
    "https://storage.googleapis.com/icons-vs/login-signup/popular-channels/popular_23.jpg",
    "https://storage.googleapis.com/icons-vs/login-signup/beast/mrbeast_10.jpg",
    "https://storage.googleapis.com/icons-vs/login-signup/beast/mrbeast_12.jpg",
    "https://storage.googleapis.com/icons-vs/login-signup/popular-channels/popular_21.jpg",
    "https://storage.googleapis.com/icons-vs/login-signup/popular-channels/popular_22.jpg",
  ],
];

// 计算重复次数以确保无缝滚动
const REPEAT_FACTOR = 3;

interface AnimatedThumbnailRowProps {
  images: string[];
  direction?: "up" | "down";
}

function AnimatedThumbnailRow({ images, direction = "up" }: AnimatedThumbnailRowProps) {
  const animationClass = direction === "up" ? "animate-scroll-up" : "animate-scroll-down";
  const repeatedImages = Array(REPEAT_FACTOR).fill(images).flat();

  return (
    // Apply animation directly here, increased vertical spacing
    <div className={cn("flex flex-col space-y-6", animationClass)}>
      {repeatedImages.map((src, index) => (
        // Increased size and shadow
        <div key={`${direction}-${index}-${src}`} className="h-40 w-40 shrink-0 overflow-hidden rounded-lg shadow-xl hover:shadow-2xl transition-shadow duration-300">
          <Image
            src={src}
            alt={`Thumbnail ${index + 1}`}
            width={160} // Match h-40 w-40 (10rem = 160px)
            height={160} // Match h-40 w-40
            className="h-full w-full object-cover"
            loading="eager"
            priority={index < 10} // Adjust based on how many images are initially visible
          />
        </div>
      ))}
    </div>
  );
}

export function AnimatedThumbnailGrid() {
  return (
    // Ensure this container fills the parent and clips the overflowing animation
    // Increased horizontal padding/spacing slightly
    <div className="relative h-full w-full overflow-hidden bg-muted p-6">
       {/* Masking to fade top and bottom */}
       {/* Increased horizontal spacing between columns */}
      <div className="absolute inset-0 flex justify-center space-x-6 [mask-image:linear-gradient(to_bottom,transparent_0%,black_10%,black_90%,transparent_100%)]">
        {imageUrls.map((rowImages, rowIndex) => (
          <AnimatedThumbnailRow
            key={rowIndex}
            images={rowImages}
            direction={rowIndex % 2 === 0 ? "up" : "down"}
          />
        ))}
      </div>
      {/* Optional overlay can remain commented out or be used for styling */}
      {/* <div className="absolute inset-0 bg-gradient-to-b from-muted/50 via-transparent to-muted/50 dark:from-muted/80 dark:to-muted/80" /> */}
    </div>
  );
} 