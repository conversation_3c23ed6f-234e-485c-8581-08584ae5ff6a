import { PrismaClient } from '@prisma/client'

const globalForPrisma = globalThis as unknown as {
  prisma: PrismaClient | undefined
}

// 创建Prisma客户端实例，添加日志和连接池配置
export const prisma = globalForPrisma.prisma ?? new PrismaClient({
  log: process.env.NODE_ENV === 'development' ? ['query', 'error', 'warn'] : ['error'],
  datasources: {
    db: {
      url: process.env.DATABASE_URL,
    },
  },
  // 注意：连接池配置通过环境变量和URL参数设置
  // 例如: DATABASE_URL=postgresql://user:password@host:port/db?connection_limit=10&pool_timeout=30
})

if (process.env.NODE_ENV !== 'production') globalForPrisma.prisma = prisma

// 数据库操作重试包装器
export async function withRetry<T>(
  operation: () => Promise<T>,
  maxRetries = 3,
  retryDelay = 500
): Promise<T> {
  let lastError: Error | null = null;
  
  for (let attempt = 0; attempt < maxRetries; attempt++) {
    try {
      return await operation();
    } catch (error) {
      lastError = error as Error;
      
      // 只重试连接相关错误
      if (!(
        error instanceof Error && 
        (error.message.includes('connection') || 
         error.message.includes('timeout') ||
         error.message.includes('pool'))
      )) {
        throw error;
      }
      
      console.error(`数据库操作失败(尝试 ${attempt + 1}/${maxRetries}):`, error);
      
      // 指数退避重试延迟
      const delay = retryDelay * Math.pow(2, attempt);
      await new Promise(resolve => setTimeout(resolve, delay));
    }
  }
  
  throw lastError || new Error('数据库操作重试失败');
}

// 确保数据库连接函数
export async function ensureConnection() {
  try {
    // 执行简单查询验证连接
    await prisma.$queryRaw`SELECT 1 as connectivity_check`;
    return true;
  } catch (error) {
    console.error('数据库连接检查失败:', error);
    
    // 尝试重新连接
    try {
      await prisma.$disconnect();
      await prisma.$connect();
      return true;
    } catch (reconnectError) {
      console.error('数据库重连失败:', reconnectError);
      return false;
    }
  }
}

// 扩展类型，处理password字段问题
export type UserWithPassword = {
  password?: string;
} & Awaited<ReturnType<typeof prisma.user.findFirst>>;

/**
 * 扩展的findUser方法，支持使用password字段查询和返回
 */
export async function findUserWithPassword(email: string, provider: string) {
  // 这里使用any绕过TypeScript类型检查
  const user = await withRetry(async () => {
    return await prisma.user.findFirst({
      where: {
        email,
        signinProvider: provider,
        isDeleted: false,
      } as any,
    }) as UserWithPassword;
  });
  
  return user;
}

/**
 * 创建用户，支持password字段
 */
export async function createUserWithPassword(userData: { 
  uuid: string;
  email: string;
  password: string;
  signinProvider: string;
  nickname?: string;
}) {
  // 使用any绕过类型检查
  const user = await withRetry(async () => {
    return await prisma.user.create({
      data: {
        uuid: userData.uuid,
        email: userData.email,
        password: userData.password,
        signinProvider: userData.signinProvider,
        nickname: userData.nickname,
        isDeleted: false,
      } as any,
    });
  });
  
  return user;
}

/**
 * 优雅关闭数据库连接
 * 在应用退出时调用此函数可确保所有查询完成并正确关闭连接
 */
export async function disconnectPrisma() {
  await prisma.$disconnect();
}
