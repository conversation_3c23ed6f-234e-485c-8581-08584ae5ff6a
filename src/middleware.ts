import { NextResponse } from 'next/server'
import type { NextRequest } from 'next/server'
import { locales, defaultLocale } from './i18n/routing';

export async function middleware(request: NextRequest) {
  try {
    const pathname = request.nextUrl.pathname
    const url = request.nextUrl.toString();

    // 获取基础URL，优先使用环境变量中的值
    const baseUrl = process.env.NEXT_PUBLIC_WEB_URL || request.nextUrl.origin;
    console.log(`[中间件] 当前请求域名: ${request.nextUrl.origin}, 使用基础URL: ${baseUrl}`);

    // 只处理包含 /watch?v= 或 /@ 的URL
    const isWatchUrl = pathname === '/watch' && request.nextUrl.searchParams.has('v');
    const isAtUrl = pathname.startsWith('/@');

    if (isWatchUrl || isAtUrl) {
      console.log(`[中间件] 检测到目标URL格式: ${url}`);
      
      // 检测直接的 /watch?v=xxx 格式
      if (isWatchUrl) {
        try {
          // 获取视频ID
          const videoId = request.nextUrl.searchParams.get('v');
          if (videoId) {
            console.log(`[中间件] 检测到watch格式URL访问: ${pathname}?v=${videoId}`);
            
            // 获取当前语言环境，检查是否有特定语言设置
            const locale = request.cookies.get('NEXT_LOCALE')?.value || defaultLocale;
            
            // 构建重定向URL到watch页面
            const redirectUrl = new URL(`/${locale}/watch`, baseUrl);
            // 添加查询参数
            redirectUrl.searchParams.set('videoUrl', `https://www.youtube.com/watch?v=${videoId}`);
            redirectUrl.searchParams.set('hasExternalVideo', 'true');
            
            // 添加缓存控制头，避免浏览器频繁请求重定向
            const response = NextResponse.redirect(redirectUrl);
            response.headers.set('Cache-Control', 'public, max-age=60');
            
            console.log(`[中间件] 重定向到watch页面: ${redirectUrl.toString()}`);
            return response;
          }
        } catch (error) {
          console.error(`[中间件] 处理watch格式URL时出错:`, error);
          // 发生错误时重定向到404页面
          const errorUrl = new URL('/404', baseUrl);
          console.log(`[中间件] 错误重定向到: ${errorUrl.toString()}`);
          return NextResponse.redirect(errorUrl);
        }
      }

      // 检查是否匹配 @watch 格式的 URL (例如: /@https://www.reyoutube.com/watch?v=UnBsU0o4y7c)
      const watchMatch = pathname.match(/^\/@(https?:\/\/[^\/]+\/watch\?v=.+)$/);
      if (watchMatch) {
        try {
          const videoUrl = watchMatch[1];
          console.log(`[中间件] 检测到@watch格式URL访问: ${pathname}, videoUrl: ${videoUrl}`);
          
          // 获取当前语言环境
          const locale = defaultLocale;
          
          // 构建重定向URL到watch页面
          const redirectUrl = new URL(`/${locale}/watch`, baseUrl);
          // 添加videoUrl作为查询参数
          redirectUrl.searchParams.set('videoUrl', videoUrl);
          redirectUrl.searchParams.set('hasExternalVideo', 'true');
          
          console.log(`[中间件] 重定向到watch页面: ${redirectUrl.toString()}`);
          return NextResponse.redirect(redirectUrl);
        } catch (error) {
          console.error(`[中间件] 处理@watch格式URL时出错:`, error);
          // 发生错误时重定向到404页面
          const errorUrl = new URL('/404', baseUrl);
          console.log(`[中间件] 错误重定向到: ${errorUrl.toString()}`);
          return NextResponse.redirect(errorUrl);
        }
      }

      // 检查是否匹配 /@username 格式，允许后面有可选的路径如 /videos
      const usernameMatch = pathname.match(/^\/@([^\/]+)(?:\/.*)?$/);
      console.log(`[中间件] 匹配结果: ${usernameMatch}`);
      if (usernameMatch) {
        try {
          const username = usernameMatch[1];
          // 排除以http或https开头的URL，因为这些已经在前面的watchMatch中处理过了
          if (username.startsWith('http://') || username.startsWith('https://')) {
            console.log(`[中间件] 跳过以http开头的URL: ${username}`);
            // 继续处理其他中间件逻辑
          } else {
            console.log(`[中间件] 检测到@格式URL访问: ${pathname}, username: ${username}`);
            
            // 构建API路由URL来处理用户名查询
            const apiUrl = request.nextUrl.clone();
            apiUrl.pathname = `/api/channels/handle/${username}`;
            
            // 打印重写URL信息用于调试
            console.log(`[中间件] 重写URL: ${apiUrl.toString()}, 协议: ${apiUrl.protocol}, 主机: ${apiUrl.host}`);
            
            // 重写到API路由进行处理
            return NextResponse.rewrite(apiUrl);
          }
        } catch (error) {
          console.error(`[中间件] 处理@格式URL时出错:`, error);
          // 发生错误时重定向到404页面
          const errorUrl = new URL('/404', baseUrl);
          console.log(`[中间件] 错误重定向到: ${errorUrl.toString()}`);
          return NextResponse.redirect(errorUrl);
        }
      }
    } else {
      // 定义不需要本地化的路径
      const pathsToExclude = [
        '/sitemap.xml',
        '/sitemap-0.xml',
        '/robots.txt',
        '/ads.txt',
        '/favicon.ico'
      ];

      // 定义静态资源路径模式
      const staticAssetPatterns = [
        /^\/images\//,           // /images/ 目录下的所有文件
        /^\/icons\//,            // /icons/ 目录下的所有文件
        /^\/assets\//,           // /assets/ 目录下的所有文件
        /^\/downloads\//,        // /downloads/ 目录下的所有文件
        /\.(jpg|jpeg|png|gif|svg|ico|webp|avif)$/i,  // 图片文件
        /\.(css|js|woff|woff2|ttf|eot)$/i,           // 样式和字体文件
        /\.(pdf|doc|docx|xls|xlsx|zip|rar)$/i        // 文档文件
      ];

      // 检查是否为静态资源
      const isStaticAsset = staticAssetPatterns.some(pattern => pattern.test(pathname));

      // 如果是静态资源或需要排除的路径，则不进行语言重定向
      if (pathsToExclude.includes(pathname) || isStaticAsset) {
        console.log(`[中间件] 检测到静态资源或排除路径 ${pathname}，跳过语言重定向。`);
        return; // 让请求继续，不进行重定向
      }

      // 处理国际化默认逻辑
      const pathnameHasLocale = locales.some(
        locale => pathname.startsWith(`/${locale}/`) || pathname === `/${locale}`
      );

      if (pathnameHasLocale) return;

      // 默认使用英文区域设置
      const locale = defaultLocale;
      const newUrl = new URL(`/${locale}${pathname.startsWith('/') ? '' : '/'}${pathname}`, baseUrl);
      console.log(`[中间件] 重定向到默认语言: ${newUrl.toString()}`);
      return NextResponse.redirect(newUrl);
    }
  } catch (error) {
    console.error(`[中间件] 全局错误:`, error);
    // 获取基础URL，优先使用环境变量中的值
    const baseUrl = process.env.NEXT_PUBLIC_WEB_URL || request.nextUrl.origin;
    // 发生全局错误时重定向到首页
    const homeUrl = new URL(`/${defaultLocale}`, baseUrl);
    console.log(`[中间件] 全局错误，重定向到: ${homeUrl.toString()}`);
    return NextResponse.redirect(homeUrl);
  }
}

export const config = {
  // 匹配所有路径，但排除API路由、静态资源等
  matcher: [
    '/((?!api|_next/static|_next/image|favicon.ico|cdn-cgi|images|icons|assets|downloads).*)',
    // 但仍然匹配 @username 和 /watch 格式的URL
    '/@:path*',
    '/watch'
  ]
}