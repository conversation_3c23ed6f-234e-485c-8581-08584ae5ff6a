/** @type {import('next-sitemap').IConfig} */
module.exports = {
  // 网站URL
  siteUrl: process.env.NEXT_PUBLIC_WEB_URL || 'https://www.reyoutube.com',
  
  // 生成的文件目录
  outDir: 'public',
  
  // 生成robots.txt
  generateRobotsTxt: true,
  
  // robots.txt 配置
  robotsTxtOptions: {
    policies: [
      {
        userAgent: '*',
        allow: '/',
        disallow: ['/api/*', '/auth/*']
      }
    ],
  },
  
  // 排除路径
  exclude: [
    '/server-sitemap.xml',
    '/404',
    '/api/*', 
    '/_next/*', 
    '/cdn-cgi/*'
  ],
  
  // 不自动生成alternateRefs
  alternateRefs: [],
  
  // 不使用默认transform，我们将自己处理所有URLs
  transform: undefined,
  
  // 关闭自动lastmod
  autoLastmod: false,
  
  // 手动添加所有我们需要的URLs
  additionalPaths: async () => {
    const baseUrl = process.env.NEXT_PUBLIC_WEB_URL || 'https://www.reyoutube.com';
    const locales = ['en', 'zh'];
    const routes = [
      '',  // 首页
      '/channel/external',
      '/compare',
      '/dashboard',
      '/favorites',
      '/login',
      '/orders',
      '/profile',
      '/rankings',
      '/reports',
      '/tasks',
      '/watch',
    ];
    
    const paths = [];
    
    // 添加首页和基本页面
    paths.push({
      loc: '/',
      lastmod: new Date().toISOString(),
      changefreq: 'daily',
      priority: 1.0,
    });
    
    paths.push({
      loc: '/sitemap.xml',
      lastmod: new Date().toISOString(),
      changefreq: 'daily',
      priority: 0.5,
    });
    
    paths.push({
      loc: '/robots.txt',
      lastmod: new Date().toISOString(),
      changefreq: 'monthly',
      priority: 0.5,
    });
    
    // 为每个语言和路由创建URL
    for (const locale of locales) {
      for (const route of routes) {
        const path = `/${locale}${route}`;
        
        // 仅添加URL，不添加alternateRefs
        paths.push({
          loc: path,
          lastmod: new Date().toISOString(),
          changefreq: 'daily',
          priority: 0.8,
        });
      }
    }
    
    return paths;
  }
} 