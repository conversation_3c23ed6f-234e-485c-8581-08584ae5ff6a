#!/usr/bin/env node

/**
 * YouTube API 测试脚本
 * 用于验证 YouTube Data API v3 集成是否正常工作
 */

const https = require('https');
const { URL } = require('url');

// 配置
const BASE_URL = process.env.NEXT_PUBLIC_WEB_URL || 'http://localhost:3000';
const API_KEY = process.env.YOUTUBE_API_KEY;

// 测试用例
const TEST_CASES = {
  channels: [
    { type: 'id', value: 'UCVHFbqXqoYvEWM1Ddxl0QDg', name: 'Google Developers (ID)' },
    { type: 'username', value: 'GoogleDevelopers', name: 'Google Developers (Username)' },
    { type: 'username', value: 'TechCrunch', name: 'TechCrunch' },
  ],
  videos: [
    { id: 'dQw4w9WgXcQ', name: '<PERSON> - Never Gonna Give You Up' },
    { id: 'jNQXAC9IVRw', name: 'Me at the zoo (First YouTube video)' },
  ]
};

/**
 * 发送 HTTP 请求
 */
function makeRequest(url) {
  return new Promise((resolve, reject) => {
    const urlObj = new URL(url);
    const options = {
      hostname: urlObj.hostname,
      port: urlObj.port || (urlObj.protocol === 'https:' ? 443 : 80),
      path: urlObj.pathname + urlObj.search,
      method: 'GET',
      headers: {
        'Accept': 'application/json',
        'User-Agent': 'ReYoutube-Test-Script/1.0'
      }
    };

    const req = (urlObj.protocol === 'https:' ? https : require('http')).request(options, (res) => {
      let data = '';
      
      res.on('data', (chunk) => {
        data += chunk;
      });
      
      res.on('end', () => {
        try {
          const jsonData = JSON.parse(data);
          resolve({
            status: res.statusCode,
            headers: res.headers,
            data: jsonData
          });
        } catch (error) {
          resolve({
            status: res.statusCode,
            headers: res.headers,
            data: data,
            parseError: error.message
          });
        }
      });
    });

    req.on('error', (error) => {
      reject(error);
    });

    req.setTimeout(10000, () => {
      req.destroy();
      reject(new Error('Request timeout'));
    });

    req.end();
  });
}

/**
 * 测试频道 API
 */
async function testChannelsAPI() {
  console.log('\n🔍 测试频道 API...\n');
  
  for (const testCase of TEST_CASES.channels) {
    try {
      const url = `${BASE_URL}/api/youtube/channels?${testCase.type}=${encodeURIComponent(testCase.value)}`;
      console.log(`📡 测试: ${testCase.name}`);
      console.log(`   URL: ${url}`);
      
      const response = await makeRequest(url);
      
      if (response.status === 200 && response.data.success) {
        const channel = response.data.data;
        console.log(`   ✅ 成功: ${channel.title}`);
        console.log(`   📊 订阅者: ${channel.metadata.subscriberCountFormatted}`);
        console.log(`   👀 观看数: ${channel.metadata.viewCountFormatted}`);
        console.log(`   🎥 视频数: ${channel.metadata.videoCountFormatted}`);
      } else {
        console.log(`   ❌ 失败: ${response.status} - ${response.data.error || response.data.message || 'Unknown error'}`);
      }
    } catch (error) {
      console.log(`   💥 错误: ${error.message}`);
    }
    
    console.log('');
  }
}

/**
 * 测试视频 API
 */
async function testVideosAPI() {
  console.log('\n🎬 测试视频 API...\n');
  
  for (const testCase of TEST_CASES.videos) {
    try {
      const url = `${BASE_URL}/api/youtube/videos?id=${encodeURIComponent(testCase.id)}`;
      console.log(`📡 测试: ${testCase.name}`);
      console.log(`   URL: ${url}`);
      
      const response = await makeRequest(url);
      
      if (response.status === 200 && response.data.success) {
        const video = response.data.data;
        console.log(`   ✅ 成功: ${video.title}`);
        console.log(`   📺 频道: ${video.channelTitle}`);
        console.log(`   👀 观看数: ${video.metadata.viewCountFormatted}`);
        console.log(`   👍 点赞数: ${video.metadata.likeCountFormatted}`);
        console.log(`   ⏱️ 时长: ${video.metadata.durationFormatted}`);
      } else {
        console.log(`   ❌ 失败: ${response.status} - ${response.data.error || response.data.message || 'Unknown error'}`);
      }
    } catch (error) {
      console.log(`   💥 错误: ${error.message}`);
    }
    
    console.log('');
  }
}

/**
 * 检查环境配置
 */
function checkEnvironment() {
  console.log('🔧 检查环境配置...\n');
  
  console.log(`   BASE_URL: ${BASE_URL}`);
  console.log(`   YOUTUBE_API_KEY: ${API_KEY ? '✅ 已配置' : '❌ 未配置'}`);
  
  if (!API_KEY) {
    console.log('\n⚠️  警告: YOUTUBE_API_KEY 环境变量未设置');
    console.log('   请在 .env.local 文件中添加您的 YouTube API 密钥');
    console.log('   获取方式: https://console.developers.google.com/');
  }
  
  console.log('');
}

/**
 * 测试直接 YouTube API 连接
 */
async function testDirectYouTubeAPI() {
  if (!API_KEY) {
    console.log('⏭️  跳过直接 YouTube API 测试 (API Key 未配置)\n');
    return;
  }
  
  console.log('🌐 测试直接 YouTube API 连接...\n');
  
  try {
    const url = `https://www.googleapis.com/youtube/v3/channels?part=snippet&id=UCVHFbqXqoYvEWM1Ddxl0QDg&key=${API_KEY}`;
    console.log('📡 测试直接 API 调用...');
    
    const response = await makeRequest(url);
    
    if (response.status === 200 && response.data.items && response.data.items.length > 0) {
      const channel = response.data.items[0];
      console.log(`   ✅ 成功连接 YouTube API`);
      console.log(`   📺 频道: ${channel.snippet.title}`);
    } else {
      console.log(`   ❌ API 调用失败: ${response.status}`);
      if (response.data.error) {
        console.log(`   错误: ${response.data.error.message}`);
      }
    }
  } catch (error) {
    console.log(`   💥 连接错误: ${error.message}`);
  }
  
  console.log('');
}

/**
 * 主函数
 */
async function main() {
  console.log('🚀 YouTube API 集成测试\n');
  console.log('=' .repeat(50));
  
  // 检查环境
  checkEnvironment();
  
  // 测试直接 YouTube API
  await testDirectYouTubeAPI();
  
  // 测试本地 API 端点
  await testChannelsAPI();
  await testVideosAPI();
  
  console.log('✨ 测试完成!\n');
  console.log('💡 提示:');
  console.log('   - 如果测试失败，请检查 YOUTUBE_API_KEY 环境变量');
  console.log('   - 确保 YouTube Data API v3 已在 Google Cloud Console 中启用');
  console.log('   - 检查 API 密钥的使用限制和配额');
  console.log('   - 访问测试页面: http://localhost:3000/test/youtube-api');
}

// 运行测试
if (require.main === module) {
  main().catch(console.error);
}

module.exports = {
  makeRequest,
  testChannelsAPI,
  testVideosAPI,
  checkEnvironment,
  testDirectYouTubeAPI
};
