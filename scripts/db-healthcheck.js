/**
 * 数据库连接健康检查脚本
 * 用于监控和诊断数据库连接问题
 */

const { PrismaClient } = require('@prisma/client');
// 移除对 src/lib/prisma 的依赖，因为它在此脚本中未使用且导致错误
// const { ensureConnection, disconnectPrisma } = require('../src/lib/prisma');

async function checkDatabaseHealth() {
  console.log('🔍 开始数据库健康检查...');
  
  try {
    // 导入Prisma客户端
    const prisma = new PrismaClient({
      log: ['error', 'warn'],
    });
    
    // 测试连接
    console.log('测试数据库连接...');
    await prisma.$connect();
    console.log('✅ 数据库连接成功');
    
    // 获取数据库版本
    console.log('\n获取数据库信息:');
    const versionResult = await prisma.$queryRaw`SELECT version();`;
    console.log(`PostgreSQL版本: ${versionResult[0].version}`);
    
    // 检查活跃连接数
    console.log('\n检查活跃连接:');
    const connectionsResult = await prisma.$queryRaw`
      SELECT count(*) as active_connections 
      FROM pg_stat_activity 
      WHERE datname = current_database();
    `;
    console.log(`活跃连接数: ${connectionsResult[0].active_connections}`);
    
    // 检查表统计信息
    console.log('\n检查表数据量:');
    const tableStats = await prisma.$queryRaw`
      SELECT 
        schemaname, 
        relname as table_name, 
        n_live_tup as row_count
      FROM pg_stat_user_tables
      ORDER BY n_live_tup DESC
      LIMIT 10;
    `;
    
    console.log('Top 10 表数据量:');
    tableStats.forEach(stat => {
      console.log(`${stat.schemaname}.${stat.table_name}: ${stat.row_count} 行`);
    });
    
    // 检查空间使用情况
    console.log('\n检查数据库空间使用:');
    const dbSize = await prisma.$queryRaw`
      SELECT 
        pg_size_pretty(pg_database_size(current_database())) as db_size;
    `;
    console.log(`数据库大小: ${dbSize[0].db_size}`);
    
    // 断开连接
    await prisma.$disconnect();
    console.log('\n✅ 数据库健康检查完成');
    
  } catch (error) {
    console.error('\n❌ 数据库健康检查失败:', error);
    process.exit(1);
  }
}

// 执行健康检查
checkDatabaseHealth().catch(e => {
  console.error('未捕获的错误:', e);
  process.exit(1);
}); 