#!/usr/bin/env node

/**
 * 网络诊断脚本
 * 用于诊断 YouTube API 连接问题
 */

const https = require('https');
const http = require('http');
const dns = require('dns').promises;
const { URL } = require('url');
const fs = require('fs');
const path = require('path');

// 加载环境变量
function loadEnvFile() {
  const envFiles = ['.env.local', '.env'];
  
  for (const envFile of envFiles) {
    const envPath = path.join(process.cwd(), envFile);
    if (fs.existsSync(envPath)) {
      console.log(`📄 加载环境文件: ${envFile}`);
      const envContent = fs.readFileSync(envPath, 'utf8');
      const lines = envContent.split('\n');
      
      for (const line of lines) {
        const trimmedLine = line.trim();
        if (trimmedLine && !trimmedLine.startsWith('#')) {
          const [key, ...valueParts] = trimmedLine.split('=');
          if (key && valueParts.length > 0) {
            const value = valueParts.join('=').replace(/^['"]|['"]$/g, '');
            if (!process.env[key]) {
              process.env[key] = value;
            }
          }
        }
      }
      break;
    }
  }
}

loadEnvFile();

const API_KEY = process.env.YOUTUBE_API_KEY;

/**
 * 简单的 HTTP 请求函数
 */
function makeRequest(url, timeout = 10000) {
  return new Promise((resolve, reject) => {
    const urlObj = new URL(url);
    const options = {
      hostname: urlObj.hostname,
      port: urlObj.port || (urlObj.protocol === 'https:' ? 443 : 80),
      path: urlObj.pathname + urlObj.search,
      method: 'GET',
      headers: {
        'Accept': 'application/json',
        'User-Agent': 'ReYoutube-Diagnostic/1.0'
      },
      timeout: timeout
    };

    const req = (urlObj.protocol === 'https:' ? https : http).request(options, (res) => {
      let data = '';
      
      res.on('data', (chunk) => {
        data += chunk;
      });
      
      res.on('end', () => {
        resolve({
          status: res.statusCode,
          headers: res.headers,
          data: data
        });
      });
    });

    req.on('error', (error) => {
      reject(error);
    });

    req.on('timeout', () => {
      req.destroy();
      reject(new Error(`Request timeout after ${timeout}ms`));
    });

    req.end();
  });
}

/**
 * DNS 解析测试
 */
async function testDNS() {
  console.log('\n🌐 DNS 解析测试...\n');
  
  const domains = [
    'www.googleapis.com',
    'youtube.googleapis.com',
    'google.com'
  ];
  
  for (const domain of domains) {
    try {
      console.log(`🔍 解析 ${domain}...`);
      const addresses = await dns.resolve4(domain);
      console.log(`   ✅ 成功: ${addresses.join(', ')}`);
    } catch (error) {
      console.log(`   ❌ 失败: ${error.message}`);
    }
  }
}

/**
 * 基础连接测试
 */
async function testBasicConnection() {
  console.log('\n🔗 基础连接测试...\n');
  
  const endpoints = [
    'https://www.google.com',
    'https://www.googleapis.com',
    'https://youtube.googleapis.com'
  ];
  
  for (const endpoint of endpoints) {
    try {
      console.log(`📡 连接 ${endpoint}...`);
      const startTime = Date.now();
      const response = await makeRequest(endpoint, 15000);
      const duration = Date.now() - startTime;
      
      console.log(`   ✅ 成功: HTTP ${response.status} (${duration}ms)`);
    } catch (error) {
      console.log(`   ❌ 失败: ${error.message}`);
    }
  }
}

/**
 * YouTube API 测试
 */
async function testYouTubeAPI() {
  console.log('\n🎬 YouTube API 测试...\n');
  
  if (!API_KEY) {
    console.log('⚠️  跳过 YouTube API 测试 (API Key 未配置)');
    return;
  }
  
  const testCases = [
    {
      name: '频道信息 (ID)',
      url: `https://www.googleapis.com/youtube/v3/channels?part=snippet&id=UCVHFbqXqoYvEWM1Ddxl0QDg&key=${API_KEY}`
    },
    {
      name: '频道信息 (用户名)',
      url: `https://www.googleapis.com/youtube/v3/channels?part=snippet&forUsername=GoogleDevelopers&key=${API_KEY}`
    },
    {
      name: '视频信息',
      url: `https://www.googleapis.com/youtube/v3/videos?part=snippet&id=dQw4w9WgXcQ&key=${API_KEY}`
    }
  ];
  
  for (const testCase of testCases) {
    try {
      console.log(`📡 测试 ${testCase.name}...`);
      const startTime = Date.now();
      const response = await makeRequest(testCase.url, 20000);
      const duration = Date.now() - startTime;
      
      if (response.status === 200) {
        try {
          const data = JSON.parse(response.data);
          console.log(`   ✅ 成功: 找到 ${data.items?.length || 0} 项结果 (${duration}ms)`);
        } catch (parseError) {
          console.log(`   ⚠️  响应解析失败: ${parseError.message}`);
        }
      } else {
        console.log(`   ❌ 失败: HTTP ${response.status} (${duration}ms)`);
        if (response.data) {
          try {
            const errorData = JSON.parse(response.data);
            console.log(`   📄 错误详情: ${errorData.error?.message || response.data.substring(0, 100)}`);
          } catch {
            console.log(`   📄 响应: ${response.data.substring(0, 100)}...`);
          }
        }
      }
    } catch (error) {
      console.log(`   ❌ 异常: ${error.message}`);
    }
  }
}

/**
 * 网络环境检查
 */
async function checkNetworkEnvironment() {
  console.log('\n🔧 网络环境检查...\n');
  
  // 检查代理设置
  const proxyVars = ['HTTP_PROXY', 'HTTPS_PROXY', 'http_proxy', 'https_proxy'];
  let hasProxy = false;
  
  for (const proxyVar of proxyVars) {
    if (process.env[proxyVar]) {
      console.log(`🔄 发现代理设置: ${proxyVar}=${process.env[proxyVar]}`);
      hasProxy = true;
    }
  }
  
  if (!hasProxy) {
    console.log('📝 未检测到代理设置');
  }
  
  // 检查 Node.js 版本
  console.log(`📦 Node.js 版本: ${process.version}`);
  
  // 检查操作系统
  console.log(`💻 操作系统: ${process.platform} ${process.arch}`);
  
  // 检查环境变量
  console.log(`🔑 API Key: ${API_KEY ? '已配置' : '未配置'}`);
  if (API_KEY) {
    console.log(`   长度: ${API_KEY.length} 字符`);
    console.log(`   前缀: ${API_KEY.substring(0, 10)}...`);
  }
}

/**
 * 生成诊断报告
 */
function generateReport() {
  console.log('\n📋 诊断建议...\n');
  
  console.log('🔧 常见解决方案:');
  console.log('   1. 检查网络连接是否正常');
  console.log('   2. 确认防火墙未阻止 HTTPS 请求');
  console.log('   3. 检查是否需要配置代理服务器');
  console.log('   4. 尝试使用 VPN 如果 Google 服务被屏蔽');
  console.log('   5. 验证 YouTube API Key 是否有效');
  console.log('   6. 确认 YouTube Data API v3 已启用');
  
  console.log('\n🌐 网络测试命令:');
  console.log('   curl -I https://www.googleapis.com');
  console.log('   nslookup www.googleapis.com');
  console.log('   ping google.com');
  
  console.log('\n📚 相关资源:');
  console.log('   - Google Cloud Console: https://console.cloud.google.com/');
  console.log('   - YouTube API 文档: https://developers.google.com/youtube/v3');
  console.log('   - 项目文档: docs/YOUTUBE_API_SETUP.md');
}

/**
 * 主函数
 */
async function main() {
  console.log('🚀 YouTube API 网络诊断工具\n');
  console.log('=' .repeat(50));
  
  try {
    await checkNetworkEnvironment();
    await testDNS();
    await testBasicConnection();
    await testYouTubeAPI();
    generateReport();
    
    console.log('\n✨ 诊断完成!');
  } catch (error) {
    console.error('\n💥 诊断过程中发生错误:', error);
  }
}

// 运行诊断
if (require.main === module) {
  main().catch(console.error);
}

module.exports = { main, testDNS, testBasicConnection, testYouTubeAPI };
